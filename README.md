# Auto Care SaaS

## 项目简介
汽车服务管理系统，提供车辆维修、保养、预约等一站式服务。系统采用多租户架构，支持多门店管理，提供完整的车辆维修流程管理。

## 技术栈
- 后端：Spring Boot 2.7.5 + MyBatis 2.2.2 + MySQL 8.0
- 前端：Vue 3 + Element Plus
- 工具：Maven 3.8+、Git


## 文档导航
- [系统架构](docs/技术架构.md)
- [系统管理控制](docs/系统权限设计.md)
- [车辆维修流转](docs/车辆维修流程设计.md)
- [车辆维修业务](docs/车辆维修业务设计.md)
- [开发规范](docs/开发规范.md)
- [接口文档](docs/接口文档.md)
- [API文档](http://localhost:8080/swagger-ui.html)

## API文档
项目集成了Swagger，提供了可视化的API接口文档。通过Swagger，您可以直观地了解所有API接口的详细信息，并且可以直接在浏览器中测试接口。

### 访问方式
- 启动项目后，访问：http://localhost:8080/swagger-ui.html

### 功能模块
API接口按照以下模块分类：
1. **认证管理** - 用户登录、注销、验证码等认证相关接口
2. **用户管理** - 用户创建、查询、修改、导入导出等接口
3. **角色管理** - 角色创建、权限分配等接口
4. **车辆管理** - 车辆信息维护相关接口
5. **维修管理** - 维修工单、维修进度等接口
6. **配件管理** - 配件库存、配件使用等接口
7. **统计报表** - 业务数据统计相关接口

### 使用说明
1. 在Swagger UI页面，API按模块分组显示
2. 点击对应接口可查看详细信息，包括：
   - 接口URL和请求方式
   - 请求参数说明
   - 响应结果示例
   - 可能的错误码
3. 可直接在页面上进行接口测试：
   - 填写请求参数
   - 点击"Execute"按钮发送请求
   - 查看响应结果

### 开发规范
所有新增接口都必须添加Swagger注解，包括但不限于：
- `@Api` - 在控制器类上标注，说明模块功能
- `@ApiOperation` - 在方法上标注，说明接口功能
- `@ApiParam` - 在参数上标注，说明参数含义
- `@ApiModel` - 在DTO类上标注，说明数据模型含义
- `@ApiModelProperty` - 在DTO属性上标注，说明属性含义



## 开发环境要求
- JDK 1.8+
- Maven 3.8+
- MySQL 8.0+
- Redis 6.0+
- Node.js 16+

## 目录结构
```plaintext
src/
├── main/
│   ├── java/
│   │   └── com/extracme/saas/autocare/
│   │       ├── config/                 # 配置类目录
│   │       │   ├── SwaggerConfig       # Swagger配置
│   │       │   └── WebMvcConfig        # Web MVC配置
│   │       ├── controller/             # 控制器目录
│   │       │   └── api/                # API接口
│   │       ├── service/                # 服务层目录
│   │       │   ├── base/               # 服务接口
│   │       │   └── impl/               # 服务实现
│   │       ├── repository/             # 数据访问层目录
│   │       │   └── impl/               # 数据访问实现
│   │       ├── model/                  # 数据模型目录
│   │       │   ├── entity/             # 数据库实体
│   │       │   ├── dto/                # 数据传输对象
│   │       │   └── vo/                 # 视图对象
│   │       ├── mapper/                 # MyBatis映射目录
│   │       │   ├── base/               # 基础Mapper
│   │       │   └── extend/             # 扩展Mapper
│   │       └── common/                 # 公共组件目录
│   │           ├── exception/          # 异常类
│   │           ├── util/               # 工具类
│   │           └── constant/           # 常量定义
│   └── resources/
│       ├── mapper/                     # MyBatis映射文件
│       ├── application.yml             # 应用配置
│       ├── application-dev.yml         # 开发环境配置
│       ├── application-test.yml        # 测试环境配置
│       └── application-prod.yml        # 生产环境配置
└── test/
    └── java/
        └── com/extracme/saas/autocare/
            ├── service/                # 服务测试
            └── controller/             # 控制器测试
```

## 开发规范
- 遵循阿里巴巴Java开发手册
- 使用统一的代码格式化工具
- 提交前进行代码格式化
- 遵循Git Flow工作流
- 编写单元测试，覆盖率要求>80%
- 遵循MyBatis开发规范
- 遵循Spring开发规范
- API接口添加Swagger注解，保持文档与代码的一致性

## 最近更新

### 2025-01-25 - 操作日志用户名称格式优化
**功能描述：** 修改操作日志记录功能中的用户名称显示格式，将数据库中保存的创建人和操作人名称字段从简单的username改为"昵称【手机号】"的格式，提升操作日志的可读性和用户识别度。

**修改内容：**
1. **OperateLogService接口修改**
   - 修改 `recordOperateLogAsync` 方法参数，将 `username` 参数替换为 `nickname` 和 `mobile` 参数
   - 更新方法注释，明确参数含义和用途
   - 保持接口向后兼容，不影响现有调用方式

2. **OperateLogServiceImpl实现类修改**
   - 添加 `formatUserDisplayName()` 私有方法，实现用户名称格式化逻辑
   - 支持多种格式化场景：
     - 正常情况：`昵称【手机号】`（如：张三【***********】）
     - 只有昵称：直接显示昵称
     - 只有手机号：直接显示手机号
     - 都为空：显示"未知用户"
   - 更新所有 `recordOperateLogAsync` 方法实现，使用格式化后的用户名称

3. **OperateLogUtil工具类修改**
   - 修复调用 `recordOperateLogAsync` 方法时缺少 `mobile` 参数的问题
   - 确保从 `LoginUser` 对象中正确获取 `nickname` 和 `mobile` 字段
   - 保持工具类的易用性和向后兼容性

4. **测试文件更新**
   - OperateLogDebugTool：更新测试方法，使用新的参数格式
   - OperateLogAsyncDemo：更新演示代码，展示新的用户名称格式
   - OperateLogIntegrationTest：更新集成测试，验证格式化后的用户名称
   - OperateLogServiceTest：添加用户名称格式化功能的专项测试

**格式化规则：**
- **标准格式**：`昵称【手机号】`（如：张三【***********】）
- **只有昵称**：直接显示昵称（如：张三）
- **只有手机号**：直接显示手机号（如：***********）
- **信息缺失**：显示"未知用户"

**技术实现：**
- 使用 `StringUtils.hasText()` 进行参数校验
- 采用中文方括号【】提升可读性
- 在异步方法中进行格式化，确保性能不受影响
- 保持原有的异步记录机制和错误处理逻辑

**数据库影响：**
- 操作日志表 `sys_operate_log` 中的 `create_by` 和 `update_by` 字段将保存格式化后的用户名称
- 现有数据不受影响，新记录将使用新格式
- 提升操作日志的可读性和用户识别度

**使用示例：**
```java
// 原有调用方式保持不变
OperateLogUtil.recordUserLog(OperateLogOperateTypeEnum.CREATE, "创建用户：张三");

// 直接调用Service的新方式
operateLogService.recordOperateLogAsync(
    OperateLogModuleTypeEnum.USER_MANAGEMENT,
    OperateLogOperateTypeEnum.CREATE,
    "创建用户：张三",
    "系统自动记录",
    1L,
    "张三",          // 用户昵称
    "***********",  // 用户手机号
    1L
);
```

**日志效果对比：**
- **修改前**：创建人显示为 `admin`
- **修改后**：创建人显示为 `张三【***********】`

### 2025-01-25 - 用户管理接口添加accountType字段支持
**功能描述：** 在用户创建和更新接口中添加accountType（账号类型）字段支持，允许前端通过DTO对象传递账号类型信息，完善用户类型管理功能。

**修改内容：**
1. **UserCreateDTO修改**
   - 添加 `accountType` 字段（Integer类型，可为空）
   - 添加数据验证注解：`@Min(value = 0)` 和 `@Max(value = 1)`
   - 添加 `@ApiModelProperty` 注解，提供完整的API文档说明
   - 字段说明："账号类型：0-普通，1-修理厂"
   - 示例值：0

2. **UserUpdateDTO修改**
   - 添加 `accountType` 字段（Integer类型，可为空）
   - 添加数据验证注解：`@Min(value = 0)` 和 `@Max(value = 1)`
   - 添加 `@ApiModelProperty` 注解，保持与创建DTO的一致性
   - 支持用户更新时修改账号类型

3. **UserServiceImpl业务逻辑修改**
   - `createUser()` 方法：添加 `user.setAccountType(createDTO.getAccountType())` 处理
   - `updateUser()` 方法：添加accountType字段的条件更新逻辑
   - 保持现有的字段验证和业务规则不变

4. **测试文件更新**
   - UserServiceTest：更新测试数据包含accountType字段
   - UserControllerTest：更新测试用例包含accountType字段

**数据库字段对应确认：**
- SysUser实体类中的`accountType`字段对应数据库表`sys_user.account_type`
- 数据库字段类型：TINYINT，与Integer类型兼容
- 字段约束：0-普通，1-修理厂
- 字段用途：区分普通用户和修理厂用户，支持不同类型用户的权限管理

**数据验证：**
- 添加了 `@Min(value = 0)` 和 `@Max(value = 1)` 验证注解
- 确保accountType字段值只能是0或1
- 验证失败时返回"账号类型值不正确"的错误信息

**技术特点：**
- 遵循现有的代码风格和命名规范
- 保持与其他字段相同的处理方式
- 确保多租户架构下的数据隔离正确性
- 所有字段都添加了完整的API文档注解和数据验证

**使用场景：**
- 创建用户时可以指定账号类型（普通用户或修理厂用户）
- 更新用户信息时可以修改用户的账号类型
- 支持基于账号类型的用户权限控制和功能区分
- 为修理厂用户提供专门的功能和界面

**API示例：**
```json
// 创建普通用户请求
{
  "username": "normal_user",
  "nickname": "普通用户",
  "mobile": "***********",
  "email": "<EMAIL>",
  "orgId": "ORG001",
  "roleIds": [1],
  "status": 1,
  "approvalLevel": 1,
  "accountType": 0,
  "repairDepotId": null
}

// 创建修理厂用户请求
{
  "username": "depot_admin",
  "nickname": "修理厂管理员",
  "mobile": "***********",
  "email": "<EMAIL>",
  "orgId": "ORG001",
  "roleIds": [2],
  "status": 1,
  "approvalLevel": 2,
  "accountType": 1,
  "repairDepotId": "DEPOT001"
}

// 更新用户账号类型请求
{
  "id": 1,
  "accountType": 1
}
```

### 2025-01-25 - 用户管理接口字段调整：garageId改为repairDepotId
**功能描述：** 将用户创建和更新接口中的garageId字段调整为repairDepotId，并修改数据类型从Long改为String，以与数据库字段保持一致。

**修改内容：**
1. **UserCreateDTO修改**
   - 字段重命名：`garageId` → `repairDepotId`
   - 数据类型调整：`Long` → `String`
   - 注释更新：将"关联修理厂ID"改为"关联维修站点ID"
   - API文档注解更新：示例值从"1001"改为"DEPOT001"

2. **UserUpdateDTO修改**
   - 字段重命名：`garageId` → `repairDepotId`
   - 数据类型调整：`Long` → `String`
   - 注释更新：将"关联修理厂ID"改为"关联维修站点ID"
   - API文档注解更新：示例值从"1001"改为"DEPOT001"

3. **UserVO响应对象修改**
   - 字段重命名：`garageId` → `repairDepotId`
   - 数据类型调整：`Long` → `String`
   - API文档注解更新：将"关联修理厂ID"改为"关联维修站点ID"

4. **UserServiceImpl业务逻辑修改**
   - `createUser()` 方法：`user.setGarageId()` → `user.setRepairDepotId()`
   - `updateUser()` 方法：`updateDTO.getGarageId()` → `updateDTO.getRepairDepotId()`
   - 确保与SysUser实体类中的repairDepotId字段保持一致

5. **测试文件更新**
   - UserServiceTest：更新测试数据，使用String类型的维修站点ID
   - UserControllerTest：更新测试用例，使用新的字段名和数据类型

**数据库字段对应：**
- SysUser实体类中的`repairDepotId`字段对应数据库表`sys_user.repair_depot_id`
- 数据库字段类型：VARCHAR，与修改后的String类型保持一致
- 字段用途：关联维修站点信息，支持修理厂类型用户的站点管理

**API示例：**
```json
// 创建用户请求（修改后）
{
  "username": "depot_admin",
  "nickname": "维修站点管理员",
  "mobile": "***********",
  "email": "<EMAIL>",
  "orgId": "ORG001",
  "roleIds": [1, 2],
  "status": 1,
  "approvalLevel": 1,
  "repairDepotId": "DEPOT001"
}

// 更新用户请求（修改后）
{
  "id": 1,
  "nickname": "新昵称",
  "repairDepotId": "DEPOT002"
}

// 查询用户响应（修改后）
{
  "id": 1,
  "nickname": "维修站点管理员",
  "repairDepotId": "DEPOT001",
  "repairShop": "XX维修站点",
  // ... 其他字段
}
```

### 2025-01-25 - 用户管理接口添加garage_id字段支持
**功能描述：** 为用户创建和更新接口添加garage_id字段支持，用于关联修理厂信息，完善用户与修理厂的关联关系。

**修改内容：**
1. **UserCreateDTO修改**
   - 添加 `garageId` 字段（Long类型，可为空）
   - 添加 `@ApiModelProperty` 注解，提供完整的API文档说明
   - 字段说明："关联修理厂ID，用于修理厂类型用户"
   - 示例值：1001

2. **UserUpdateDTO修改**
   - 添加 `garageId` 字段（Long类型，可为空）
   - 添加 `@ApiModelProperty` 注解，保持与创建DTO的一致性
   - 支持用户更新时修改关联的修理厂信息

3. **UserServiceImpl业务逻辑修改**
   - `createUser()` 方法：添加 `user.setGarageId(createDTO.getGarageId())` 处理
   - `updateUser()` 方法：添加garageId字段的条件更新逻辑
   - 保持现有的字段验证和业务规则不变

4. **UserVO响应对象修改**
   - 添加 `garageId` 字段，确保查询结果包含修理厂关联信息
   - 添加 `@ApiModelProperty` 注解，完善API文档

5. **数据库支持确认**
   - 确认 `sys_user` 表已包含 `garage_id` 字段（BIGINT类型，可为空）
   - 确认 `SysUser` 实体类已包含对应的 `garageId` 属性和getter/setter方法
   - MyBatis映射文件已正确配置字段映射关系

**技术特点：**
- 遵循现有的代码风格和命名规范
- 保持与其他字段相同的处理方式
- 确保多租户架构下的数据隔离正确性
- 所有字段都添加了完整的API文档注解

**使用场景：**
- 创建修理厂类型用户时，可以直接关联对应的修理厂
- 更新用户信息时，可以修改用户关联的修理厂
- 查询用户信息时，可以获取用户关联的修理厂ID
- 支持基于修理厂的用户权限控制和数据过滤

**API示例：**
```json
// 创建用户请求
{
  "username": "garage_admin",
  "nickname": "修理厂管理员",
  "mobile": "***********",
  "email": "<EMAIL>",
  "orgId": "ORG001",
  "roleIds": [1, 2],
  "status": 1,
  "approvalLevel": 1,
  "garageId": 1001
}

// 更新用户请求
{
  "id": 1,
  "nickname": "新昵称",
  "garageId": 1002
}

// 查询用户响应
{
  "id": 1,
  "nickname": "修理厂管理员",
  "garageId": 1001,
  "repairShop": "XX修理厂",
  // ... 其他字段
}
```

### 2025-01-25 - 业务日志SQL过滤器配置优化
**功能描述：** 为业务日志文件添加SQL语句过滤器，确保business.log只记录真正的业务操作日志，排除数据库查询语句。

**修改内容：**
1. **logback.xml配置文件修改**
   - 在 `BUSINESS_FILE` appender中添加了 `EvaluatorFilter` 过滤器
   - 使用 `JaninoEventEvaluator` 实现复杂的过滤逻辑
   - 过滤规则包括：
     - 排除MyBatis Mapper接口的SQL日志（`com.extracme.saas.autocare.repository`、`com.extracme.saas.autocare.mapper`）
     - 排除MyBatis框架相关的SQL日志（`org.apache.ibatis`、`org.mybatis`、`com.baomidou.mybatisplus`）
     - 排除数据库连接池相关日志（`com.zaxxer.hikari`、`com.alibaba.druid`）
     - 排除包含SQL关键字的DEBUG级别日志（如：`SELECT`、`INSERT`、`UPDATE`、`DELETE`等）
     - 排除MyBatis特有的日志标识（如：`==>`、`<==`、`Preparing:`、`Parameters:`、`Total:`）

2. **过滤器工作原理**
   - `onMatch="DENY"`：匹配过滤条件的日志将被拒绝（不记录）
   - `onMismatch="ACCEPT"`：不匹配过滤条件的日志将被接受（正常记录）
   - 使用Janino表达式引擎进行复杂的条件判断

3. **影响范围**
   - 仅影响 `business.log` 文件的日志输出
   - 不影响控制台输出和其他日志文件（`system.log`、`error.log`）
   - 保持所有环境配置的一致性（dev、sit、prod）

**技术优势：**
- 使用高性能的Janino表达式引擎，对系统性能影响极小
- 过滤规则全面，覆盖了所有常见的SQL日志场景
- 配置灵活，可根据需要轻松调整过滤条件

**使用效果：**
- 业务日志文件将只包含真正的业务操作日志，如用户登录、数据修改、业务流程等
- 大幅减少日志文件大小，提高日志查看和分析效率
- 便于运维人员快速定位业务问题，无需在大量SQL日志中查找

**配置示例：**
```xml
<!-- 业务日志输出 -->
<appender name="BUSINESS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!-- 文件配置... -->
    
    <!-- SQL日志过滤器：排除MyBatis和数据库相关的SQL日志 -->
    <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
        <evaluator class="ch.qos.logback.classic.boolex.JaninoEventEvaluator">
            <expression>
                <!-- 排除MyBatis Mapper接口的SQL日志 -->
                logger.startsWith("com.extracme.saas.autocare.repository") ||
                logger.startsWith("com.extracme.saas.autocare.mapper") ||
                <!-- 排除MyBatis框架相关的SQL日志 -->
                logger.startsWith("org.apache.ibatis") ||
                <!-- 排除包含SQL关键字的DEBUG级别日志 -->
                (level == DEBUG &amp;&amp; (
                    message.contains("==>") ||
                    message.toUpperCase().contains("SELECT ") ||
                    message.contains("SQL")
                ))
            </expression>
        </evaluator>
        <onMismatch>ACCEPT</onMismatch>
        <onMatch>DENY</onMatch>
    </filter>
</appender>
```

### 2025-05-25 - 审批层级分页查询接口简化
**功能描述：** 简化审批层级分页查询接口，移除所有查询条件参数，只保留基础的分页功能。

**修改内容：**
1. **BasicController修改**
   - 修改 `getApprovalLevelsList()` 方法参数：`ApprovalLevelsQueryDTO` → `BasePageDTO`
   - 更新API文档注解：移除"支持按审批层级和金额范围筛选"的描述
   - 简化接口功能说明，只保留"分页查询审批层级列表，基于当前用户租户进行数据隔离"

2. **ApprovalLevelsService接口修改**
   - 修改 `getApprovalLevelsList()` 方法参数：`ApprovalLevelsQueryDTO` → `BasePageDTO`
   - 更新方法注释：将"查询DTO"改为"分页参数"
   - 移除ApprovalLevelsQueryDTO的导入，添加BasePageDTO的导入

3. **ApprovalLevelsServiceImpl实现修改**
   - 修改 `getApprovalLevelsList()` 方法实现，使用新的 `findAll()` 方法
   - 移除所有查询条件逻辑，直接查询所有审批层级记录
   - 保持按ID降序排序的逻辑不变

4. **TableApprovalLevelsService接口扩展**
   - 新增 `findAll()` 方法：查询所有审批层级列表（按ID降序排序）
   - 保持原有的 `findByCondition()` 方法不变，确保向后兼容

5. **TableApprovalLevelsServiceImpl实现扩展**
   - 实现新的 `findAll()` 方法，使用MyBatis Dynamic SQL
   - 查询所有记录并按ID降序排序，保持与原有逻辑一致

6. **测试验证**
   - 创建专门的测试类 `BasicControllerApprovalLevelsTest`
   - 验证接口只接受分页参数，不再支持查询条件
   - 测试通过，确保修改正确

**影响范围：**
- 审批层级分页查询接口：`POST /api/v1/basic/approval-levels/list`

**向后兼容性：** 此修改会影响前端代码，前端需要移除所有查询条件参数，只传递分页参数。

**使用示例：**
```json
// 请求参数（简化后）
{
  "pageNum": 1,
  "pageSize": 10
}

// 响应结果（格式不变）
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 2,
        "approvalLevel": 2,
        "approvalLevelName": "二级审批",
        "selfApprovalAmount": 50000.00,
        "createTime": "2025-05-25T10:00:00",
        "createBy": "admin"
      },
      {
        "id": 1,
        "approvalLevel": 1,
        "approvalLevelName": "一级审批",
        "selfApprovalAmount": 10000.00,
        "createTime": "2025-05-25T09:00:00",
        "createBy": "admin"
      }
    ],
    "total": 2,
    "pageNum": 1,
    "pageSize": 10,
    "pages": 1
  }
}
```

### 2025-05-25 - 租户下拉列表接口返回类型优化
**功能描述：** 将租户下拉列表接口的返回类型从String改为Long，保持数据类型一致性。

**修改内容：**
1. **TenantController修改**
   - 修改 `getTenantCombo()` 方法返回类型：`Result<List<ComboVO<String>>>` → `Result<List<ComboVO<Long>>>`
   - 更新API文档注解：将"id为租户ID(String类型)"改为"id为租户ID(Long类型)"

2. **TenantService接口修改**
   - 修改 `getTenantCombo()` 方法返回类型：`List<ComboVO<String>>` → `List<ComboVO<Long>>`
   - 更新方法注释：将"ID类型为String"改为"ID类型为Long"

3. **TenantServiceImpl实现修改**
   - 移除了 `String.valueOf(tenant.getId())` 转换，直接使用 `tenant.getId()`
   - 更新注释：将"转换为ComboVO<String>"改为"转换为ComboVO<Long>"

4. **测试验证**
   - 添加了专门的单元测试验证返回的ID确实是Long类型
   - 测试通过，确保修改正确

**影响范围：**
- 租户下拉列表接口：`GET /api/v1/tenant/combo`

**向后兼容性：** 此修改可能影响前端代码，需要确保前端能正确处理Long类型的ID。

**使用示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "value": "租户1"
    },
    {
      "id": 2,
      "value": "租户2"
    }
  ]
}
```

### 2025-01-25 - 用户列表接口增加租户ID字段
**功能描述：** 在用户列表查询接口的返回结果中添加租户ID字段，支持多租户架构下的用户管理。

**修改内容：**
1. **UserVO类修改**
   - 在 `src/main/java/com/extracme/saas/autocare/model/vo/UserVO.java` 中添加 `tenantId` 字段
   - 字段类型：`Long`
   - 字段描述：用户所属的租户ID
   - 添加了相应的Swagger注解：`@ApiModelProperty(value = "租户ID", example = "1")`

2. **服务层实现**
   - `UserServiceImpl.getUserList()` 方法通过 `BeanUtils.copyProperties()` 自动映射租户ID字段
   - 由于 `SysUser` 实体类已包含 `tenantId` 字段，无需额外的查询逻辑

3. **API文档更新**
   - 更新了 `UserController.getUserList()` 接口的 `@ApiOperation` 注解
   - 明确说明返回结果包含租户ID字段

4. **测试验证**
   - 添加了专门的单元测试 `getUserList_VerifyTenantIdMapping()` 验证租户ID字段的正确映射
   - 修复了测试中缺失的Mock依赖注入问题

**影响范围：**
- 用户列表查询接口：`POST /api/v1/users/list`
- 用户详情查询接口：`GET /api/v1/users/detail/{id}`

**向后兼容性：** 此修改为新增字段，不影响现有功能，完全向后兼容。

**使用示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "tenantId": 100,
        "nickname": "张三",
        "mobile": "***********",
        "email": "<EMAIL>",
        "status": 1,
        "roleIds": [1, 2],
        "roleNames": ["管理员", "操作员"],
        "createdTime": "2025-01-25T10:00:00",
        "updatedTime": "2025-01-25T10:00:00"
      }
    ],
    "total": 1,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

### 审批层级管理功能修复和改进 (2024-12-19)

#### 问题修复
1. **解决NullPointerException错误**
   - 修复了审批层级列表查询时出现的空指针异常
   - 问题原因：多租户环境下PageHelper分页插件与TenantSchemaInterceptor冲突
   - 解决方案：在Service层正确设置租户上下文

2. **修复数据类型不匹配问题**
   - 解决了`getApprovalLevelName`方法参数类型错误
   - 将参数类型从`Byte`修改为`Integer`，匹配实体字段类型

#### 功能改进
1. **添加业务校验逻辑**
   - 新增审批层级重复性校验，确保同一租户下审批层级不重复
   - 在Repository层添加`existsByApprovalLevel`和`existsByApprovalLevelExcludeId`方法
   - 在Service层的创建和更新方法中添加校验逻辑

2. **多租户支持优化**
   - 在所有审批层级相关操作中正确设置和清理租户上下文
   - 确保数据隔离的正确性和安全性

#### 技术改进
- 添加详细的日志记录，便于问题排查
- 使用事务注解确保数据一致性
- 遵循分层架构原则，将业务逻辑放在Service层

## 核心功能

### 多租户架构
- 支持多个租户的数据隔离
- 动态Schema切换
- 租户上下文管理

### 审批层级管理
- 支持0-4级审批层级配置
- 每个租户独立的审批层级设置
- 防重复校验机制

### 工作流管理
- 灵活的工作流引擎
- 支持自定义审批流程
- 事件驱动的处理机制

## 异常处理规范
系统采用统一的异常处理机制，所有控制器不需要捕获异常，由全局异常处理器统一处理：
- 业务异常：使用BusinessException，返回具体业务错误码和信息
- 参数异常：抛出IllegalArgumentException，返回400状态码
- 用户异常：使用UserException，返回用户相关错误码
- 系统异常：其他未捕获的异常，统一返回500状态码和"服务器内部错误"提示

## 部署要求
- 应用服务器：Nginx + Tomcat
- 数据库：MySQL主从
- 缓存：Redis集群
- 消息队列：RabbitMQ集群
- 监控：Prometheus + Grafana
- 日志：ELK Stack

## 安全要求
- 所有接口使用HTTPS
- 敏感信息加密传输
- 实现防SQL注入
- XSS防护
- CSRF防护
- 操作日志审计
- 定期安全扫描
- 多租户数据隔离
- 权限访问控制

## 多租户数据库架构

### Schema设计
系统采用独立Schema的多租户架构，包含以下三个Schema：

1. **auto_care_saas**
   - 用途：存储公共账号信息
   - 包含：用户认证、权限配置等公共数据
   - 所有租户共享访问

2. **auto_care_extracme**
   - 用途：租户0（Extracme）的业务数据
   - 包含：车辆维修、保养、预约等业务数据
   - 仅租户0可访问

3. **auto_care_dzjt**
   - 用途：租户1（DZJT）的业务数据
   - 包含：车辆维修、保养、预约等业务数据
   - 仅租户1可访问

### 租户隔离机制
- 采用独立Schema方式实现租户数据隔离
- 通过`TenantContextHolder`进行租户上下文管理
- 系统根据租户ID自动路由到对应的Schema：
  - 租户ID = 0 → auto_care_extracme
  - 租户ID = 1 → auto_care_dzjt
  - 未指定租户ID → auto_care_saas（默认）

### 数据同步
- 所有租户Schema的表结构完全相同
- 通过数据库版本控制工具（如Flyway）统一管理Schema变更
- 新建租户时自动同步最新的表结构

### 安全控制
- 严格的租户数据隔离，确保租户只能访问自己的数据
- 所有SQL操作都经过租户过滤
- 定期审计跨租户访问日志
- 数据库备份按租户独立进行
