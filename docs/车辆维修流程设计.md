# 车辆维修全流程工作流文档

本文件描述了"车辆交接 → 进保预审 → 维修报价 → 核损核价 → 车辆维修 → 车辆验收"全流程工作流的表结构、样例数据以及使用说明。此工作流基于 Activity 模型的思想，实现流程模板的可定制化，同时记录流程实例及各节点执行情况，满足车辆维修业务的自动化管理需求。

## 1. 表结构说明

### 1.1 流程模板表（workflow_template）
- **id**：流程模板编号  
- **workflow_name**：流程模板名称（例如："车辆维修全流程"）  
- **description**：流程描述  
- **task_type**：任务类型（例如："全部"、"标准"、"快速"等）
- **repair_factory_type**：修理厂类型：-1表示全部，1表示合作，2表示非合作  
- **product_line**：产品线（例如："全部"、"乘用车"、"商用车"等）
- **is_active**：是否启用（true：启用，false：禁用）
- **tenant_id**：租户编号（用于多租户隔离）  
- **create_time**：创建时间
- **create_by**：创建人
- **update_time**：修改时间
- **update_by**：修改人

### 1.2 活动节点定义表（activity_definition）
- **id**：活动节点编号  
- **activity_code**：活动编码（例如："HANDOVER"）  
- **activity_name**：活动名称（例如："车辆交接"）  
- **description**：活动描述  
- **sequence**：节点顺序（数字越小表示越靠前）
- **is_enabled**：是否启用（true：启用，false：禁用）
- **create_time**：创建时间
- **create_by**：创建人
- **update_time**：修改时间
- **update_by**：修改人

### 1.3 节点状态定义表（activity_status）
- **id**：状态编号
- **status_code**：状态编码（例如："UNPROCESSED"、"COMPLETED"、"CLOSED"、"REJECTED"等）
- **status_name**：状态名称（例如："未处理"、"已完成"、"已关闭"、"被驳回"等）
- **description**：状态描述
- **create_time**：创建时间
- **create_by**：创建人
- **update_time**：修改时间
- **update_by**：修改人

### 1.4 流程模板活动节点关联表（workflow_activity）
- **id**：关联记录编号
- **workflow_id**：流程模板编号
- **activity_id**：活动节点编号
- **create_time**：创建时间
- **create_by**：创建人
- **update_time**：修改时间
- **update_by**：修改人

### 1.5 活动节点转换规则表（activity_transition）
- **id**：转换规则编号  
- **workflow_id**：所属流程模板编号  
- **from_activity_id**：转换起始节点编号  
- **to_activity_id**：目标节点编号（可与起始节点相同，表示自循环）  
- **trigger_event**：触发事件名称（例如："完成交接"、"提交预审"等）  
- **condition_handler**：条件判断处理器类名（例如："com.autocare.workflow.condition.AmountCheckCondition"）  
- **handler_class**：处理逻辑类名（例如："com.autocare.workflow.handler.CompleteHandoverHandler"）  
- **description**：规则说明
- **tenant_id**：租户编号
- **create_time**：创建时间
- **create_by**：创建人
- **update_time**：修改时间
- **update_by**：修改人

### 1.6 节点状态转换规则表（activity_status_transition）
- **id**：转换规则编号
- **activity_transition_id**：关联的活动节点转换规则编号
- **from_status_id**：起始状态编号
- **to_status_id**：目标状态编号
- **description**：规则说明
- **create_time**：创建时间
- **create_by**：创建人
- **update_time**：修改时间
- **update_by**：修改人

### 1.7 流程实例表（workflow_instance）
- **id**：流程实例编号  
- **workflow_id**：所采用的流程模板编号  
- **business_id**：业务对象编号（例如维修任务ID）  
- **tenant_id**：租户编号  
- **current_activity_id**：当前所在的活动节点编号  
- **status**：流程状态（例如："运行中"、"已完成"、"异常"）  
- **create_time**：创建时间
- **create_by**：创建人
- **update_time**：修改时间
- **update_by**：修改人

### 1.8 活动实例记录表（activity_instance）
- **id**：活动实例记录编号  
- **instance_id**：所属流程实例编号  
- **to_activity_id**：目标活动节点编号  
- **from_activity_id**：来源节点编号（冗余存储）
- **transition_id**：触发转换的规则编号
- **start_time**：活动开始时间  
- **end_time**：活动结束时间  
- **duration**：活动总耗时（单位：秒）
- **current_status_id**：当前状态编号
- **operator**：操作人（执行该环节的人员）  
- **remarks**：备注说明
- **create_time**：创建时间
- **create_by**：创建人
- **update_time**：修改时间
- **update_by**：修改人

### 1.9 流程实例进度表（workflow_instant_progress）
- **id**：进度记录编号，主键，自增。
- **instance_id**：所属流程实例编号，外键，关联 `workflow_instance` 表。
- **activity_id**：活动节点编号，外键，关联 `activity_definition` 表。
- **status**：节点状态，记录当前节点的状态。
- **remarks**：备注说明。
- **create_time**：记录创建时间。
- **create_by**：记录创建人。
- **update_time**：记录修改时间。
- **update_by**：记录修改人。

---

## 2. 样例数据

### 2.1 流程模板（workflow_template）
...

## 3. 使用说明

1. **流程模板配置**  
   - 系统管理员在 **workflow_template** 表中创建模板基本信息
   - 在 **workflow_task_type** 表中配置适用的任务类型：
     - 支持多选，可选值包括"全部"、"标准"、"快速"等
     - 选择"全部"时表示支持所有任务类型
   - 在 **workflow_template** 表中配置修理厂类型：
     - 固定选项：-1表示全部，1表示合作，2表示非合作
   - 在 **workflow_template** 表中配置产品线：
     - 支持多选，可选值包括"全部"及各具体产品线
     - 选择"全部"时表示支持所有产品线
   - 同一租户下，相同任务类型、修理厂类型和产品线组合只能配置一个流程模板
   - 当需要修改流程模板时：
     1. 创建新的流程模板记录
     2. 配置新的模板内容
     3. 启用新模板
     4. 禁用旧模板
     5. 新创建的流程实例将使用新模板
     6. 已存在的流程实例继续使用原模板

2. **活动节点定义**  
   - 在 **activity_definition** 表中定义通用的活动节点，包含：
     - 节点编码和名称
     - 节点描述
     - 节点顺序
     - 是否启用
   - 在 **workflow_activity** 表中配置每个流程模板使用的节点：
     - 设置节点顺序
     - 控制节点启用状态
   - 系统根据当前流程模板和节点编码自动匹配对应的节点定义

3. **配置转换规则**  
   - 在 **activity_transition** 表中为各环节之间定义转换规则，指定触发事件（例如"完成交接"、"提交预审"等），系统根据这些规则自动流转流程。

4. **启动流程实例**  
   - 当具体业务（如维修任务 repairTask001）触发时，系统在 **workflow_instance** 表中创建流程实例，并将当前活动节点设置为起始节点。  
   - 后续各环节完成时，系统自动更新当前节点并记录活动实例信息。

5. **记录活动执行情况**  
   - 每个环节执行时，系统在 **activity_instance** 表中记录活动的开始时间、结束时间、执行人、状态以及备注，方便追踪和审计。

---

## 4. 数据库 SQL 语句

下面提供生成上述表结构的 SQL 脚本（适用于 MySQL 数据库）：

```sql
-- 1. 创建流程模板表（workflow_template）
CREATE TABLE workflow_template (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '流程模板编号',
    workflow_name VARCHAR(100) NOT NULL COMMENT '流程模板名称',
    description TEXT COMMENT '流程描述',
    task_type VARCHAR(50) NOT NULL COMMENT '任务类型',
    repair_factory_type TINYINT NOT NULL COMMENT '修理厂类型：-1表示全部，1表示合作，2表示非合作',
    product_line VARCHAR(50) NOT NULL COMMENT '产品线',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    tenant_id INT NOT NULL COMMENT '租户编号',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    update_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人',
    UNIQUE KEY uk_template_unique (tenant_id, task_type, repair_factory_type, product_line)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2. 创建活动节点定义表（activity_definition）
CREATE TABLE activity_definition (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '活动节点编号',
    activity_code VARCHAR(50) NOT NULL COMMENT '活动编码',
    activity_name VARCHAR(100) NOT NULL COMMENT '活动名称',
    description TEXT COMMENT '活动描述',
    sequence INT NOT NULL COMMENT '节点顺序',
    is_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    update_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人',
    UNIQUE KEY uk_activity_code (activity_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 3. 创建节点状态定义表（activity_status）
CREATE TABLE activity_status (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '状态编号',
    status_code VARCHAR(50) NOT NULL COMMENT '状态编码',
    status_name VARCHAR(100) NOT NULL COMMENT '状态名称',
    description TEXT COMMENT '状态描述',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    update_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人',
    UNIQUE KEY uk_status_code (status_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 4. 创建节点状态转换规则表（activity_status_transition）
CREATE TABLE activity_status_transition (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '转换规则编号',
    activity_transition_id BIGINT NOT NULL COMMENT '关联的活动节点转换规则编号',
    from_status_id BIGINT NOT NULL COMMENT '起始状态编号',
    to_status_id BIGINT NOT NULL COMMENT '目标状态编号',
    description TEXT COMMENT '规则说明',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    update_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人',
    UNIQUE KEY uk_status_transition (activity_transition_id, from_status_id, to_status_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 5. 创建流程模板活动节点关联表（workflow_activity）
CREATE TABLE workflow_activity (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联记录编号',
    workflow_id BIGINT NOT NULL COMMENT '流程模板编号',
    activity_id BIGINT NOT NULL COMMENT '活动节点编号',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    update_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人',
    UNIQUE KEY uk_workflow_activity (workflow_id, activity_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 6. 创建活动节点转换规则表（activity_transition）
CREATE TABLE activity_transition (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '转换规则编号',
    workflow_id BIGINT NOT NULL COMMENT '所属流程模板编号',
    from_activity_id BIGINT NOT NULL COMMENT '转换起始节点编号',
    to_activity_id BIGINT NOT NULL COMMENT '目标节点编号',
    trigger_event VARCHAR(50) NOT NULL COMMENT '触发事件名称',
    condition_handler VARCHAR(255) COMMENT '条件判断处理器类名',
    handler_class VARCHAR(255) NOT NULL COMMENT '处理逻辑类名',
    description TEXT COMMENT '规则说明',
    tenant_id INT NOT NULL COMMENT '租户编号',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    update_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 7. 创建流程实例表（workflow_instance）
CREATE TABLE workflow_instance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '流程实例编号',
    workflow_id BIGINT NOT NULL COMMENT '所采用的流程模板编号',
    business_id VARCHAR(100) NOT NULL COMMENT '业务对象编号',
    tenant_id INT NOT NULL COMMENT '租户编号',
    current_activity_id BIGINT NOT NULL COMMENT '当前所在的活动节点编号',
    status VARCHAR(50) NOT NULL COMMENT '流程状态',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    update_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 8. 创建活动实例记录表（activity_instance）
CREATE TABLE activity_instance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '活动实例记录编号',
    instance_id BIGINT NOT NULL COMMENT '所属流程实例编号',
    to_activity_id BIGINT NOT NULL COMMENT '目标活动节点编号',
    from_activity_id BIGINT COMMENT '来源节点编号',
    transition_id BIGINT COMMENT '触发转换的规则编号',
    start_time DATETIME NOT NULL COMMENT '活动开始时间',
    end_time DATETIME COMMENT '活动结束时间',
    duration INT COMMENT '活动总耗时（单位：秒）',
    current_status_id BIGINT NOT NULL COMMENT '当前状态编号',
    operator VARCHAR(100) NOT NULL COMMENT '操作人',
    remarks TEXT COMMENT '备注说明',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    update_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 9. 创建流程实例进度表（workflow_instant_progress）
CREATE TABLE workflow_instant_progress (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '进度记录编号',
    instance_id BIGINT NOT NULL COMMENT '所属流程实例编号',
    activity_id BIGINT NOT NULL COMMENT '活动节点编号',
    status VARCHAR(50) NOT NULL COMMENT '节点状态',
    remarks TEXT COMMENT '备注说明',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    update_by VARCHAR(128) NOT NULL DEFAULT '' COMMENT '修改人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;