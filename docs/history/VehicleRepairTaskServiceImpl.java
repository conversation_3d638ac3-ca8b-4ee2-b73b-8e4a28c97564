import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.saicmobility.evcard.bfc.bfcinsuranceservice.api.*;
import com.saicmobility.evcard.md.mddataproxy.api.*;
import com.saicmobility.evcard.utils.LocalDateUtil;
import com.saicmobility.evcard.utils.PbConvertUtil;
import com.saicmobility.evcard.vlms.operation.constant.BusinessConstant;
import com.saicmobility.evcard.vlms.operation.database.TableAttachmentService;
import com.saicmobility.evcard.vlms.operation.database.TableVehicleRepairLogService;
import com.saicmobility.evcard.vlms.operation.database.TableVehicleRepairTaskService;
import com.saicmobility.evcard.vlms.operation.database.TableVehicleRepairWorkService;
import com.saicmobility.evcard.vlms.operation.dto.*;
import com.saicmobility.evcard.vlms.operation.enums.CurrentTacheEnum;
import com.saicmobility.evcard.vlms.operation.enums.DownLoadFileSourceEnum;
import com.saicmobility.evcard.vlms.operation.enums.FileSystemEnum;
import com.saicmobility.evcard.vlms.operation.enums.FileTypeEnum;
import com.saicmobility.evcard.vlms.operation.enums.repair.*;
import com.saicmobility.evcard.vlms.operation.error.ResultCode;
import com.saicmobility.evcard.vlms.operation.error.ServiceException;
import com.saicmobility.evcard.vlms.operation.javaconfig.Global;
import com.saicmobility.evcard.vlms.operation.model.Attachment;
import com.saicmobility.evcard.vlms.operation.model.VehicleRepairTask;
import com.saicmobility.evcard.vlms.operation.model.VehicleRepairWork;
import com.saicmobility.evcard.vlms.operation.service.ExportFileService;
import com.saicmobility.evcard.vlms.operation.service.VehicleRepairTaskService;
import com.saicmobility.evcard.vlms.operation.service.inner.RelationOrderInfoService;
import com.saicmobility.evcard.vlms.operation.utils.HttpClientUtils;
import com.saicmobility.evcard.vlms.operation.utils.OutDescIdUtil;
import com.saicmobility.evcard.vlms.operation.utils.VehicleRepairTaskUtil;
import com.saicmobility.evcard.vlms.vlmsassetsservice.api.*;
import com.saicmobility.evcard.vlms.vlmsoperationservice.api.AccidentInsuranceInfoDetail;
import com.saicmobility.evcard.vlms.vlmsoperationservice.api.AccidentOrderInfo;
import com.saicmobility.evcard.vlms.vlmsoperationservice.api.AttachmentInfo;
import com.saicmobility.evcard.vlms.vlmsoperationservice.api.CurrentUser;
import com.saicmobility.evcard.vlms.vlmsoperationservice.api.RepairDepotInfo;
import com.saicmobility.evcard.vlms.vlmsoperationservice.api.RepairTaskInfo;
import com.saicmobility.evcard.vlms.vlmsoperationservice.api.*;
import krpc.rpc.util.TypeSafe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.InputStream;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-05-11
 */
@Service
@Slf4j
public class VehicleRepairTaskServiceImpl implements VehicleRepairTaskService {

    @Resource
    private TableVehicleRepairTaskService tableVehicleRepairTaskService;

    @Resource
    private TableVehicleRepairWorkService tableVehicleRepairWorkService;

    @Resource
    private BfcInsuranceService bfcInsuranceService;

    @Resource
    private TableAttachmentService tableAttachmentService;

    @Resource
    private VlmsAssetsService vlmsAssetsService;

    @Resource
    private RelationOrderInfoService relationOrderInfoService;

    @Resource
    private MdDataProxy mdDataProxy;

    @Autowired
    public OutDescIdUtil outDescIdUtil;

    @Resource
    private ExportFileService exportFileService;

    @Resource
    private TableVehicleRepairLogService tableVehicleRepairLogService;

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    // 已完成的维修任务状态
    private static final List<Integer> END_TASK_LIST = Arrays.asList(RepairTaskStatusEnum.UNDER_COMPLETE.getCode(), RepairTaskStatusEnum.UNDER_CLOSE.getCode());


    @Override
    public SearchVehicleRepairTaskListRes searchVehicleRepairTaskList(SearchVehicleRepairTaskListReq req) {

        SearchVehicleRepairTaskListResponse response = new SearchVehicleRepairTaskListResponse();
        response.setList(tableVehicleRepairTaskService.selectList(req));
        response.setTotal(tableVehicleRepairTaskService.selectTotal(req));
        return PbConvertUtil.generateProtoBuffer(response, SearchVehicleRepairTaskListRes.class);
    }

    @Override
    public DetailVehicleRepairTaskRes detailVehicleRepairTask(DetailVehicleRepairTaskReq req) {
        DetailVehicleRepairTaskResponse response = new DetailVehicleRepairTaskResponse();
        // 基础信息 和车辆信息查询
        VehicleRepairTask vehicleRepairTask = tableVehicleRepairTaskService.selectById(req.getId());
        if (vehicleRepairTask == null){
            if (StrUtil.isNotBlank(req.getTaskNo())) {
                vehicleRepairTask = tableVehicleRepairTaskService.selectByTaskNo(req.getTaskNo());
                if (vehicleRepairTask == null){
                    throw new ServiceException("任务不存在");
                }
            } else {
                throw new ServiceException("任务不存在");
            }
        }
        BeanUtil.copyProperties(vehicleRepairTask, response);
        response.setRepairDepotGrade(vehicleRepairTask.getRepairGrade());
        response.setCreateTime(vehicleRepairTask.getCreateTime());
        // 查询车辆总里程
        response.setVehicleMileage(queryVehicleMile(vehicleRepairTask.getVin()));

        if (vehicleRepairTask.getRepairType().equals(RepairTypeEnum.INSURED_MAINTENANCE.getCode())){
            // 查询关联事故任务
            queryAndSetRelationAccident(vehicleRepairTask.getAccidentNo(), response);
            // 查询事故关联订单
            RepairMatchOrderInfoData repairMatchOrderInfoData = queryOrderInfoByOrderNoAndType(response.getOrderNo(), response.getOrderType(), vehicleRepairTask.getVin());
            response.setOrderStatus(repairMatchOrderInfoData.getOrderStatus());
            response.setStartTime(repairMatchOrderInfoData.getStartTime());
            response.setEndTime(repairMatchOrderInfoData.getEndTime());
            response.setOrderType(response.getOrderType());
            //长租订单属性
            response.setVehicleClientMaintenanceFee(repairMatchOrderInfoData.getVehicleClientMaintenanceFee());
            response.setClientUpkeepTag(repairMatchOrderInfoData.getClientUpkeepTag());
            response.setClientInspectTag(repairMatchOrderInfoData.getClientInspectTag());
            response.setCompanyName(repairMatchOrderInfoData.getCompanyName());
            response.setViolationName(repairMatchOrderInfoData.getViolationName());
            response.setViolationTelNo(repairMatchOrderInfoData.getViolationTelNo());

            //门店订单属性
            response.setServiceType(repairMatchOrderInfoData.getServiceType());
            response.setServiceContent(repairMatchOrderInfoData.getServiceContent());

        }else if (!vehicleRepairTask.getRelateType().equals(RelateTypeEnum.NO_RELATE_ORDER.getCode())){
            // 查询维修任务关联订单
            RepairMatchOrderInfoData repairMatchOrderInfoData = queryOrderInfoByOrderNoAndType(response.getOrderNo(), response.getOrderType(), vehicleRepairTask.getVin());
            response.setOrderStatus(repairMatchOrderInfoData.getOrderStatus());
            response.setStartTime(repairMatchOrderInfoData.getStartTime());
            response.setEndTime(repairMatchOrderInfoData.getEndTime());
            //长租订单属性
            response.setVehicleClientMaintenanceFee(repairMatchOrderInfoData.getVehicleClientMaintenanceFee());
            response.setClientUpkeepTag(repairMatchOrderInfoData.getClientUpkeepTag());
            response.setClientInspectTag(repairMatchOrderInfoData.getClientInspectTag());
            response.setCompanyName(repairMatchOrderInfoData.getCompanyName());
            response.setViolationName(repairMatchOrderInfoData.getViolationName());
            response.setViolationTelNo(repairMatchOrderInfoData.getViolationTelNo());

            //门店订单属性
            response.setServiceType(repairMatchOrderInfoData.getServiceType());
            response.setServiceContent(repairMatchOrderInfoData.getServiceContent());

        }
        // 查询关联工单
        queryRelationRepairWork(vehicleRepairTask.getTaskNo(), response);

        // 查询出厂登记
        queryLeavingFactoryInfo(vehicleRepairTask.getTaskNo(), response);

        DetailVehicleRepairTaskRes detailVehicleRepairTaskRes = PbConvertUtil.generateProtoBuffer(response, DetailVehicleRepairTaskRes.class);
        return detailVehicleRepairTaskRes;
    }


    /**
     * 维修系统任务详情
     * @return
     */
    @Override
    public DetailRepairTaskRes detailRepairTask(DetailRepairTaskReq detailRepairTaskReq) {
        String taskNo = detailRepairTaskReq.getTaskNo();
        String vin = detailRepairTaskReq.getVin();
        Integer repairType = detailRepairTaskReq.getRepairType();
        String accidentNo = detailRepairTaskReq.getAccidentNo();
        Integer relateType = detailRepairTaskReq.getRelateType();
        String orderNo = detailRepairTaskReq.getOrderNo();
        Integer orderType = detailRepairTaskReq.getOrderType();

        DetailRepairTaskRes.Builder builder = DetailRepairTaskRes.newBuilder();

        if (repairType.equals(RepairTypeEnum.INSURED_MAINTENANCE.getCode())){
            // 查询事故关联订单
            RepairMatchOrderInfoData repairMatchOrderInfoData = queryOrderInfoByOrderNoAndType(orderNo, orderType, vin);
            builder.setStartTime(repairMatchOrderInfoData.getStartTime());
            builder.setEndTime(repairMatchOrderInfoData.getEndTime());
        }else if (!relateType.equals(RelateTypeEnum.NO_RELATE_ORDER.getCode())){
            // 查询维修任务关联订单
            RepairMatchOrderInfoData repairMatchOrderInfoData = queryOrderInfoByOrderNoAndType(orderNo, orderType, vin);
            builder.setStartTime(repairMatchOrderInfoData.getStartTime());
            builder.setEndTime(repairMatchOrderInfoData.getEndTime());
        }
        return builder.build();
    }

    /**
     * 查询梧桐维修任务 上传的照片/视频
     * @param req
     * @return
     */
    @Override
    public GetWtRepairFileListRes getWtRepairFileList(GetWtRepairFileListReq req) {
        VehicleRepairTask vehicleRepairTask = tableVehicleRepairTaskService.selectByTaskNo(req.getTaskNo());
        if (vehicleRepairTask == null){
            throw new ServiceException(ResultCode.COMMON_FAIL, "维修任务不存在");
        }

        GetWtRepairFileListRes.Builder builder = GetWtRepairFileListRes.newBuilder();

        // 情况描述
        builder.setSituationDesc(vehicleRepairTask.getMiscDesc());
        // 查生效的
        List<Attachment> repairFiles = tableAttachmentService.selectByRelationAndStatus(req.getTaskNo(), FileTypeEnum.REPAIR.getType(), 0);
        if (CollectionUtils.isNotEmpty(repairFiles)){
            for (Attachment repairFile : repairFiles) {
                builder.addRepairFiles(
                        AttachmentInfo.newBuilder()
                                .setFileId(repairFile.getId())
                                .setFileName(repairFile.getFileName())
                                .setFilePath(repairFile.getFilePath())
                                .setContentType(repairFile.getContentType())
                );
            }
        }
        return builder.build();
    }

    /**
     * 根据维修任务号查询出厂登记信息
     * @param taskNo
     * @param response
     */
    private void queryLeavingFactoryInfo(String taskNo, DetailVehicleRepairTaskResponse response) {
        HashMap<String, Object> request = new HashMap<>();
        request.put("taskNo", taskNo);

        String requestParam = JSONObject.toJSONString(request);
        log.info("根据条件查询出厂登记信息 request：{}", requestParam);
        JSONObject jsonResponse = HttpClientUtils.sendRestHttp_post(Global.instance.getMtcUrl()+BusinessConstant.QUERY_VEHICLE_LEAVING_FACTORY_NEW, "POST", requestParam, null);
        log.info("根据条件查询出厂登记信息 response：{}", jsonResponse);
        String code = jsonResponse.getString("code");
        if (!"0".equals(code)){
            throw new ServiceException("维修系统返回异常:"+ jsonResponse.getString("message"));
        }
       if (jsonResponse.get("data") == null){
           response.setLeavingFactoryStatus(LeavingFactoryEnum.UNDER_NOT_LEAVING_FACTORY.getCode());
           return;
       }
        List<RepairTaskLeavingFactoryData> dataList = JSON.parseArray(jsonResponse.getString("data"), RepairTaskLeavingFactoryData.class);
        List<LeavingFactoryInfoData> factoryInfoList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dataList)){
            for (RepairTaskLeavingFactoryData data : dataList) {
                LeavingFactoryInfoData leavingFactoryInfo = new LeavingFactoryInfoData();
                leavingFactoryInfo.setLeavingFactoryId(data.getId());
                leavingFactoryInfo.setLeavingFactoryStatus(data.getLeavingStatus());
                leavingFactoryInfo.setTakeName(data.getName());
                leavingFactoryInfo.setTakePhone(data.getPhoneNumber());
                leavingFactoryInfo.setDeliveryTime(data.getDeliveryTime());
                leavingFactoryInfo.setRegister(data.getCreateOperName());
                leavingFactoryInfo.setRegisterTime(DateUtil.format(data.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                leavingFactoryInfo.setDeliveryPictures(data.getDeliveryPictureList());
                factoryInfoList.add(leavingFactoryInfo);
            }
        }
        response.setInfo(factoryInfoList);
    }

    /**
     * 查询车辆总里程
     * @param vin
     * @return
     */
    public Integer queryVehicleMile(String vin){
        // 查询车辆里程
        GetVehicleLabelByVinRes vehicleLabelByVin = mdDataProxy.getVehicleLabelByVin(GetVehicleLabelByVinReq.newBuilder().setVin(vin).build());
        return vehicleLabelByVin.getVehicleMileage();
    }

    /**
     * 查询关联事故
     * @param accidentNo
     * @param response
     */
    private void queryAndSetRelationAccident(String accidentNo, DetailVehicleRepairTaskResponse response) {
        QueryAccidentDamageByAccidentRes queryAccidentDamageByAccidentRes = bfcInsuranceService.queryAccidentDamageByAccidentNo(QueryAccidentDamageByAccidentReq
                .newBuilder()
                .setAccidentNo(accidentNo)
                .build());
        if (queryAccidentDamageByAccidentRes != null){
            response.setAccidentStatus(queryAccidentDamageByAccidentRes.getAccidentStatus());
            response.setAccidentType(queryAccidentDamageByAccidentRes.getAccidentType());
            response.setHurtTag(queryAccidentDamageByAccidentRes.getHurtTag());
            response.setResponseType(queryAccidentDamageByAccidentRes.getResponseType());
            response.setAccidentDate(queryAccidentDamageByAccidentRes.getAccidentDate());
            response.setTotalDamageAmount(queryAccidentDamageByAccidentRes.getTotalDamageAmount());
            response.setOwnerInsuranceAmount(queryAccidentDamageByAccidentRes.getOwnerInsuranceAmount());
            response.setOwnerExpenseAmount(queryAccidentDamageByAccidentRes.getOwnerExpenseAmount());
            response.setThirdExpenseAmount(queryAccidentDamageByAccidentRes.getThirdExpenseAmount());

            response.setOrderNo(queryAccidentDamageByAccidentRes.getRelateOrderNo());
            response.setOrderType(queryAccidentDamageByAccidentRes.getRelateOrderType());
        }
    }

    /**
     * 查询关联维修工单
     * @param taskNo
     * @param response
     */
    private void queryRelationRepairWork(String taskNo, DetailVehicleRepairTaskResponse response) {
        List<VehicleRepairWork> vehicleRepairWorks = tableVehicleRepairWorkService.selectByRepairTaskNo(taskNo);
        if (CollectionUtils.isNotEmpty(vehicleRepairWorks)){
            response.setWorkTaskInfos(BeanUtil.copyToList(vehicleRepairWorks, VehicleRepairWorkData.class));
        }
    }

    @Override
    public SearchVehicleRepairTaskListRes exportVehicleRepairTask(SearchVehicleRepairTaskListReq req) {
        CurrentUser currentUser = req.getCurrentUser();

        long total = tableVehicleRepairTaskService.selectTotal(req);
        if (total == 0){
            throw new ServiceException(ResultCode.COMMON_FAIL, "没有符合条件的数据不能导出");
        }
        //路径文件名
        String filePathName = "ExportVehicleRepairTask.xlsx";
        //导出标题
        String exportTile = "导出维修任务列表";
        //导出模板信息
        InputStream templateStream = this.getClass().getClassLoader().getResourceAsStream("template/维修任务列表.xlsx");
        //导出来源 - 运营管理系统
        Integer fileSource = FileSystemEnum.OPERATION_MANAGEMENT.getCode();
        //导出模块 - 维修任务列表
        DownLoadFileSourceEnum downLoadFileSourceEnum = DownLoadFileSourceEnum.REPAIR_TASK_LIST;
        taskExecutor.execute(()->{
            //导出数据
            exportFileService.export(filePathName, exportTile, fileSource, downLoadFileSourceEnum, templateStream,
                    currentUser.getNickName(), currentUser.getUserAccount(), currentUser.getOrgId(), currentUser.getOrgName()
                    , ((excelWriter, writeSheet) -> {
                        boolean hasResult = false;
                        // 每页条数
                        int pageSize = 5000;
                        int totalPageNum = ((int) total + pageSize - 1) / pageSize;
                        int pageNum = 1;
                        do {
                            SearchVehicleRepairTaskListReq build = req.toBuilder().setPageNum(pageNum).setPageSize(pageSize).build();
                            List<VehicleRepairTaskData> repairTaskDataList = tableVehicleRepairTaskService.selectList(build);
                            if(repairTaskDataList.isEmpty()){
                                break;
                            }
                            hasResult = true;
                            for (VehicleRepairTaskData vehicleRepairTaskData : repairTaskDataList) {
                                // 导出对象处理
                                formatVehicleRepairTaskData(vehicleRepairTaskData);
                            }
                            excelWriter.fill(repairTaskDataList, writeSheet);
                            pageNum ++;
                        }while (totalPageNum >= pageNum);
                        return hasResult;
                    }));
        });
        return SearchVehicleRepairTaskListRes.ok();
    }

    private void formatVehicleRepairTaskData(VehicleRepairTaskData vehicleRepairTaskData) {
        vehicleRepairTaskData.setRepairTypeDesc(RepairTypeEnum.getValueByCode(vehicleRepairTaskData.getRepairType()));
        vehicleRepairTaskData.setRepairTaskStatusDesc(RepairTaskStatusEnum.getValueByCode(vehicleRepairTaskData.getRepairTaskStatus()));
        vehicleRepairTaskData.setLeavingFactoryDesc(LeavingFactoryEnum.getValueByCode(vehicleRepairTaskData.getLeavingFactory()));
        vehicleRepairTaskData.setReviewToSelFeeFlagDesc(vehicleRepairTaskData.getReviewToSelFeeFlag() == 1 ? "是" : "否");
        vehicleRepairTaskData.setRepairDepotTypeDesc(RepairDepotTypeEnum.getValueByCode(vehicleRepairTaskData.getRepairDepotType()));
        vehicleRepairTaskData.setDeclareSettlementStatusDesc(DeclareSettlementStatusEnum.getDescByCode(vehicleRepairTaskData.getDeclareSettlementStatus()));
        vehicleRepairTaskData.setSettlementStatusDesc(SettlementStatusEnum.getDescByCode(vehicleRepairTaskData.getSettlementStatus()));
        vehicleRepairTaskData.setCreateTimeStr(DateUtil.format(vehicleRepairTaskData.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
    }

    @Override
    public QueryVehicleRepairInfoRes queryVehicleRepairInfo(QueryVehicleRepairInfoReq req) {
        QueryVehicleRepairInfoRes.Builder builder = QueryVehicleRepairInfoRes.newBuilder();

        if (StringUtils.isBlank(req.getVin()) && StringUtils.isBlank(req.getVehicleNo())){
            throw new ServiceException("车架号和车牌号不能同时为空");
        }
        if(StringUtils.isBlank(req.getSendRepairTime())){
            throw new ServiceException("送修时间缺失");
        }

        Date sendRepairTime = LocalDateUtil.parse(req.getSendRepairTime(), LocalDateUtil.DATE_PATTERN);

        String vin = req.getVin();
        // 如果车架号为空则根据车牌号查询车架号
        if (StringUtils.isBlank(vin)){
            GetVehicleInfoByVehicleNoRes vinInfo = mdDataProxy.getVehicleInfoByVehicleNo(GetVehicleInfoByVehicleNoReq.newBuilder().setVehicleNo(req.getVehicleNo()).build());
            if (vinInfo == null){
                throw new ServiceException("车辆信息不存在");
            }
            vin = vinInfo.getVin();
        }
        // 如果 送修时间是今天 则取车辆当前实时信息
        if (DateUtil.isSameDay(sendRepairTime, new Date())){
            queryVehicleBaseInfo(vin, builder);
        }else {
            //查询车辆历史基础信息
            queryVehicleHistoryInfo(vin, LocalDateUtil.format(sendRepairTime, LocalDateUtil.DATETIME_PATTERN), builder);
        }

        //  查询维修系统信息
        queryDoingRepairInfo(vin, builder);
        return builder.build();
    }

    /**
     * 查询进行中的维修信息
     * @param vin
     * @param builder
     */
    private void queryDoingRepairInfo(String vin, QueryVehicleRepairInfoRes.Builder builder) {

        // 先查梧桐的任务
        List<VehicleRepairTask> vehicleRepairTaskList = tableVehicleRepairTaskService.selectByVin(vin);

        Map<String, RepairTaskInfoData> wtRepairTaskMap = new HashMap<>();
        for (VehicleRepairTask vehicleRepairTask : vehicleRepairTaskList) {
            RepairTaskInfoData repairTaskInfoData = new RepairTaskInfoData();
            // 过滤已经完成 已结束的任务
            if (END_TASK_LIST.contains(vehicleRepairTask.getRepairTaskStatus())){
                continue;
            }
            BeanUtils.copyProperties(vehicleRepairTask, repairTaskInfoData);
            repairTaskInfoData.setRepairType(RepairTypeEnum.getValueByCode(vehicleRepairTask.getRepairType()));
            repairTaskInfoData.setRepairTaskStatus(vehicleRepairTask.getRepairTaskStatus());
            wtRepairTaskMap.put(vehicleRepairTask.getTaskNo(), repairTaskInfoData);
        }

        // 查维修系统任务
        getUnEndRepairTaskInfo(vin, wtRepairTaskMap);
        if (wtRepairTaskMap.isEmpty()){
            return;
        }

        // wtRepairTaskMap 循环处理
        for (Map.Entry<String, RepairTaskInfoData> repairTaskInfoDataEntry : wtRepairTaskMap.entrySet()) {
            RepairTaskInfoData repairTaskInfoData = repairTaskInfoDataEntry.getValue();

            builder.addRepairTaskInfos(RepairTaskInfo.newBuilder()
                    .setTaskNo(repairTaskInfoData.getTaskNo())
                    .setRepairType(repairTaskInfoData.getRepairType())
                    .setLeavingFactory(repairTaskInfoData.getLeavingFactory())
                    .setRepairDepotType(repairTaskInfoData.getRepairDepotType())
                    .setRepairDepotName(repairTaskInfoData.getRepairDepotName())
                    .setOldRepairTaskStatus(repairTaskInfoData.getOldRepairTaskStatus())
                    .setRepairTaskStatus(repairTaskInfoData.getRepairTaskStatus())
                    .setCurrentTache(CurrentTacheEnum.getEnumDesc(repairTaskInfoData.getCurrentTache()))
                    .setRepairDepotGrade(repairTaskInfoData.getRepairGrade())
                    .setReviewToSelFeeFlag(repairTaskInfoData.getReviewToSelFeeFlag())
            );
        }
    }

    /**
     * 根据车架号查询维修系统所有任务
     * @param vin
     * @return
     */
    public List<RepairTaskView> queryAllRepairTaskByVin(String vin) {
        List<RepairTaskView> repairTaskViewList = new ArrayList<>();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("vin", vin);
        JSONObject syncRepairResponse = HttpClientUtils.sendRestHttp_post(Global.instance.getMtcUrl()+BusinessConstant.QUERY_REPAIR_TASK_BY_VIN, "POST", JSON.toJSONString(paramMap), null);
        log.info("查询维修任务返回参数：{}", JSON.toJSONString(syncRepairResponse));
        String code = syncRepairResponse.getString("code");
        if (!"0".equals(code)){
            throw new ServiceException(syncRepairResponse.getString("message"));
        }
        String data = syncRepairResponse.getString("data");
        if (StringUtils.isBlank(data)){
            return repairTaskViewList;
        }
        return JSONArray.parseArray(data, RepairTaskView.class);
    }

    /**
     * 根据车架号查询维修系统任务
     * @param vin
     * @return
     */
    private void getUnEndRepairTaskInfo(String vin, Map<String, RepairTaskInfoData> wtRepairTaskMap) {
        List<RepairTaskInfoData> repairTaskInfoDataList = new ArrayList<>();
        // 查询所有维修任务
        List<RepairTaskView> repairTaskViewList = queryAllRepairTaskByVin(vin);
        for (RepairTaskView repairTaskView : repairTaskViewList) {
            // 如果验收完成 则忽略
            if (StringUtils.isNotBlank(repairTaskView.getVehicleCheckTaskSchedule()) && repairTaskView.getVehicleCheckTaskSchedule().equals("610") && !wtRepairTaskMap.containsKey(repairTaskView.getTaskNo())){
                continue;
            }
            if (wtRepairTaskMap.containsKey(repairTaskView.getTaskNo())){
              /*  RepairTaskInfoData repairTaskInfoData = wtRepairTaskMap.get(repairTaskView.getTaskNo());
                repairTaskInfoData.setCurrentTache(repairTaskView.getCurrentStatus());*/
                continue;
            }
            RepairTaskInfoData repairTaskInfoData = new RepairTaskInfoData();
            BeanUtil.copyProperties(repairTaskView, repairTaskInfoData);
            Integer leavingFactory = null;
            // 出厂登记判断
            if (StringUtils.isBlank(repairTaskView.getTakeUserName())){
                leavingFactory = LeavingFactoryEnum.UNDER_NOT_LEAVING_FACTORY.getCode();
            }else {
                leavingFactory = LeavingFactoryEnum.UNDER_LEAVING_FACTORY.getCode();
            }
            repairTaskInfoData.setLeavingFactory(leavingFactory);
            repairTaskInfoData.setOldRepairTaskStatus(repairTaskView.getCurrentStatusDesc());
            repairTaskInfoData.setRepairType(repairTaskView.getRepairTypeName());

            // 转成梧桐维修任务状态
            Integer repairTaskStatus = VehicleRepairTaskUtil.transMtcVehicleRepairTaskStatus(repairTaskView.getCurrentTache(), repairTaskView.getCurrentStatus(), StringUtils.isBlank(repairTaskView.getTakeUserName()) ? false : true);
            if (END_TASK_LIST.contains(repairTaskStatus)){
                continue;
            }
            repairTaskInfoData.setRepairTaskStatus(repairTaskStatus);
            // 当前维修任务阶段状态
            String oldRepairTaskStatus = VehicleRepairTaskUtil.isShowOldRepairTask(repairTaskView.getCurrentTache(), repairTaskView.getCurrentStatus());
            if (StringUtils.isBlank(oldRepairTaskStatus)){
                continue;
            }
            repairTaskInfoData.setCurrentTache(repairTaskView.getCurrentTache());
            repairTaskInfoData.setOldRepairTaskStatus(oldRepairTaskStatus);
            repairTaskInfoData.setTaskSchedule(repairTaskView.getCurrentStatus());
            wtRepairTaskMap.put(repairTaskView.getTaskNo(), repairTaskInfoData);
        }
    }

    /**
     * 查询车辆历史基础信息
     * @param vin 车架号
     * @param useTime 查询时间
     * @param builder 组装参数
     */
    private void queryVehicleHistoryInfo(String vin, String useTime, QueryVehicleRepairInfoRes.Builder builder){

        // 查询车辆基础信息
        GetVehicleInfoByVinRes vehicleInfoByVin = mdDataProxy.getVehicleInfoByVin(GetVehicleInfoByVinReq.newBuilder().setVin(vin).build());

        //车辆历史快照信息
        GetVehicleHistoryByTimeReq req = GetVehicleHistoryByTimeReq.newBuilder()
                        .setVin(vin)
                        .setUseTime(useTime)
                        .build();
        GetVehicleHistoryByTimeRes vehicleHistoryByTime = vlmsAssetsService.getVehicleHistoryByTime(req);

        if(!vehicleHistoryByTime.hasInfo() && StringUtils.isNotBlank(vehicleHistoryByTime.getInfo().getVin())){
            throw new ServiceException("车辆信息不存在");
        }
        VehicleHistoryInfo vehicleHistoryInfo = vehicleHistoryByTime.getInfo();

        // 查询车型信息
        GetVehicleModelBySeqRes vehicleModelBySeq = mdDataProxy.getVehicleModelBySeq(GetVehicleModelBySeqReq.newBuilder().setVehicleModelSeq(vehicleInfoByVin.getVehicleModelSeq()).build());

        // 封装数据
        builder.setVehicleNo(vehicleInfoByVin.getVehicleNo())
                .setVin(vin)
                .setVehicleModelSeq(vehicleInfoByVin.getVehicleModelSeq())
                .setVehicleModelName(vehicleModelBySeq.getVehicleModelInfo())
                .setVehicleOrgId(vehicleHistoryInfo.getPropertyOrgId())
                .setVehicleOrgName(Global.instance.configLoader.getOrgName(vehicleHistoryInfo.getPropertyOrgId()))
                .setVehicleOperateOrgId(vehicleHistoryInfo.getOperationOrgId())
                .setVehicleOperateOrgName(Global.instance.configLoader.getOrgName(vehicleHistoryInfo.getOperationOrgId()))
                .setFactOperateTag(vehicleHistoryInfo.getFactOperateTag())
                //车辆当前里程数
                .setVehicleMile(queryVehicleMile(vin))
                .setProductLine(vehicleHistoryInfo.getProductLine())
                .setSubProductLine(vehicleHistoryInfo.getSubProductLine());
    }

    /**
     * 查询车辆基础信息
     * @param vin
     * @param builder
     */
    private void queryVehicleBaseInfo(String vin, QueryVehicleRepairInfoRes.Builder builder) {
        // 查询车辆基础信息
        GetVehicleInfoByVinRes vehicleInfoByVin = mdDataProxy.getVehicleInfoByVin(GetVehicleInfoByVinReq.newBuilder().setVin(vin).build());

        // 查询车型信息
        //QueryVehicleModelInsuranceRes queryVehicleModelInsuranceRes = vlmsModelCenterService.queryVehicleModelInsurance(QueryVehicleModelInsuranceReq.newBuilder().setId(vehicleInfoByVin.getVehicleModelSeq()).build());
        GetVehicleModelBySeqRes vehicleModelBySeq = mdDataProxy.getVehicleModelBySeq(GetVehicleModelBySeqReq.newBuilder().setVehicleModelSeq(vehicleInfoByVin.getVehicleModelSeq()).build());
        // 查询产品线信息
        GetAssetsVehicleByVinRes assetRpcRes = vlmsAssetsService.getAssetsVehicleByVin(GetAssetsVehicleByVinReq.newBuilder().addVin(vin).build());
        List<AssetsVehicle> rpcResInfoList = assetRpcRes.getInfoList();
        if (CollectionUtils.isEmpty(rpcResInfoList)) {
            throw new ServiceException("车辆信息不存在");
        }
        if (rpcResInfoList.size() > 1) {
            throw new ServiceException(StrUtil.format("{}:存在多条信息", vin));
        }
        AssetsVehicle vehicle = rpcResInfoList.get(0);

        // 封装数据
        builder.setVehicleNo(vehicleInfoByVin.getVehicleNo())
                .setVin(vin)
                .setVehicleModelSeq(vehicleInfoByVin.getVehicleModelSeq())
                .setVehicleModelName(vehicleModelBySeq.getVehicleModelInfo())
                .setVehicleOrgId(vehicleInfoByVin.getOrgId())
                .setVehicleOrgName(vehicleInfoByVin.getOrgName())
                .setVehicleOperateOrgId(vehicleInfoByVin.getOperationOrgId())
                .setVehicleOperateOrgName(vehicleInfoByVin.getOperationOrgName())
                .setFactOperateTag(vehicleInfoByVin.getFactOperateTag())
                .setVehicleMile(queryVehicleMile(vin))
                .setProductLine(vehicle.getProductLine())
                .setSubProductLine(vehicle.getSubProductLine());
    }

    @Override
    public QueryRelationInsuranceListRes queryRelationInsuranceList(QueryRelationInsuranceListReq req) {
        //关联保单信息
        QueryVehicleInsuranceInfoListReq queryVehicleInsuranceInfoListReq = QueryVehicleInsuranceInfoListReq.newBuilder()
                .setVin(req.getVin())
                //当前保单是否生效  -1:全部 1:是 2:否
                .setIsEffect(-1)
                .setPageNum(1)
                .setPageSize(Integer.MAX_VALUE)
                .build();
        log.info("查询保单信息 queryVehicleInsuranceInfoList req: {}", queryVehicleInsuranceInfoListReq);
        ListInsuranceInfoRes listInsuranceInfoRes = bfcInsuranceService.queryVehicleInsuranceInfoList(queryVehicleInsuranceInfoListReq);
        log.info("查询保单信息 queryVehicleInsuranceInfoList res: {}", listInsuranceInfoRes);

        // 筛选符合送修时间的保单
        List<AccidentInsuranceInfoDetail> accidentInsuranceList = getAccidentInsuranceList(listInsuranceInfoRes.getListList(), req.getSendRepairTime());
        return QueryRelationInsuranceListRes.newBuilder().addAllInsuranceInfoDetails(accidentInsuranceList).build();
    }

    private List<AccidentInsuranceInfoDetail> getAccidentInsuranceList(List<InsuranceRealInfo> insuranceList, String accidentDateStr){

        DateTime accidentDate = DateUtil.parse(accidentDateStr);
        List<AccidentInsuranceInfoDetail> resultList = new ArrayList<>();
        insuranceList = insuranceList.stream().filter(f -> (DateUtil.parse(f.getInsuranceStartDate()).isBeforeOrEquals(accidentDate)
                && (DateUtil.parse(f.getInsuranceEndDate()).isAfterOrEquals(accidentDate)))).collect(Collectors.toList());
        //SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        for (InsuranceRealInfo data : insuranceList){
            if (StringUtils.isNotBlank(data.getInsuranceSuspendStartDate())  && StringUtils.isNotBlank(data.getInsuranceSuspendEndDate())) {
                if ((DateUtil.parse(data.getInsuranceSuspendStartDate())).isBeforeOrEquals(accidentDate)
                        && (DateUtil.parse(data.getInsuranceSuspendEndDate())).isAfter(accidentDate)) {
                    continue;
                }
            }
            AccidentInsuranceInfoDetail.Builder builder = AccidentInsuranceInfoDetail.newBuilder();
            builder.setId(TypeSafe.anyToInt(data.getId()));
            builder.setInsuranceNo(data.getInsuranceNo());
            builder.setInsuranceType(InsuranceTypeEnum.getValueByCode(data.getInsuranceType()));
            if (StringUtils.isNotBlank(data.getInsuranceStartDate())){
                builder.setInsuranceStartDate(data.getInsuranceStartDate());
            }
            if (StringUtils.isNotBlank(data.getInsuranceEndDate())){
                builder.setInsuranceEndDate(data.getInsuranceEndDate());
            }
            builder.setInsurerName(data.getInsurerName());
            builder.setInsurer(data.getInsurerShortName());
            builder.setTotalAmount(String.valueOf(data.getTotalAmount()));
            Map<String, SubInsuranceInfo> subInsuranceInfoMap = data.getSubInsuranceInfoMap();
            SubInsuranceInfo damageData = subInsuranceInfoMap.get(BusinessConstant.INSURANCE_DAMAGE_LOSS);
            if (null != damageData){
                builder.setDamageInsuranceDeduct(String.valueOf(damageData.getDeductible()));
            }
            SubInsuranceInfo thirdData = subInsuranceInfoMap.get(BusinessConstant.INSURANCE_THIRD_PARTY);
            if (null != thirdData){
                builder.setThirdInsuranceAmount(String.valueOf(thirdData.getSumInsured()));
            }
            SubInsuranceInfo driverData = subInsuranceInfoMap.get(BusinessConstant.INSURANCE_INCAR_DRIVER);
            if (null != driverData){
                builder.setDriverInsuranceAmount(String.valueOf(driverData.getSumInsured()));
            }
            SubInsuranceInfo passengerData = subInsuranceInfoMap.get(BusinessConstant.INSURANCE_INCAR_PASSENGER);
            if (null != passengerData){
                builder.setPassengerInsuranceAmount(String.valueOf(passengerData.getSumInsured()));
            }
            builder.setVin(data.getVin());
            builder.setVehicleNo(data.getVehicleNo());
            builder.setVehicleModelName(data.getVehicleModelName());
            builder.setPdfAddress(data.getPdfAddress());
            builder.setVehicleOrgName(data.getVehicleOrgName());
            builder.setVehicleOperationOrgName(data.getVehicleOperationOrgName());
            builder.setInsuringPerson(data.getInsuringPerson());
            builder.setInsuredPerson(data.getInsuredPerson());
            builder.setReceiptNo(data.getReceiptNo());
            builder.setVehicleOwner(data.getVehicleOwner());
            builder.setRoadRescueServices(data.getRoadRescueServices());
            resultList.add(builder.build());
        }

        return resultList;
    }

    @Override
    public QueryRelationAccidentListRes queryRelationAccidentList(QueryRelationAccidentListReq req) {

        QueryRelationAccidentListResponse response = new QueryRelationAccidentListResponse();
        QueryAccidentInfoForWtRes queryAccidentInfoListByVehicleRes = bfcInsuranceService.queryAccidentInfoForWt(QueryAccidentInfoForWtReq.newBuilder()
                .setVehiclePlate(req.getVehicleNo())
                .setVehicleVin(req.getVin())
                .setPageNum(1)
                .setPageSize(999)
                .build());
        if (null == queryAccidentInfoListByVehicleRes || CollectionUtils.isEmpty(queryAccidentInfoListByVehicleRes.getListList())){
            PbConvertUtil.generateProtoBuffer(response, QueryRelationAccidentListRes.class);
        }
        List<AccidentAndDamageInfoData> accidentAndDamageInfoDataList = queryAccidentInfoListByVehicleRes.getListList();
        List<RelationAccidentData> accidentDataList = new ArrayList<>();
        for (AccidentAndDamageInfoData accidentInfo : accidentAndDamageInfoDataList) {
            RelationAccidentData relationAccidentData = new RelationAccidentData();
            relationAccidentData.setAccidentNo(accidentInfo.getAccidentNo());
            relationAccidentData.setVehicleVin(accidentInfo.getVehicleVin());
            relationAccidentData.setVehiclePlate(accidentInfo.getVehiclePlate());
            relationAccidentData.setAccidentDate(accidentInfo.getAccidentDate());
            relationAccidentData.setAccidentStatus(accidentInfo.getAccidentStatus());
            relationAccidentData.setAccidentType(accidentInfo.getAccidentType());
            relationAccidentData.setRelationOrderNo(accidentInfo.getRelateOrderNo());
            relationAccidentData.setResponseType(accidentInfo.getResponseType());
            relationAccidentData.setHurtTag(TypeSafe.anyToInt(accidentInfo.getHurtTag()));
            accidentDataList.add(relationAccidentData);
        }
        response.setAccidentInfos(accidentDataList);
        return PbConvertUtil.generateProtoBuffer(response, QueryRelationAccidentListRes.class);
    }

    @Override
    public QueryRelationAccidentOrderInfoRes queryRelationAccidentOrderInfo(QueryRelationAccidentOrderInfoReq req) {
        QueryRelationAccidentOrderInfoRes.Builder builder = QueryRelationAccidentOrderInfoRes.newBuilder();
        if (StringUtils.isBlank(req.getAccidentNo())){
            throw new ServiceException("事故单号不能为空");
        }
        // 查询书事故关联订单号（事故可能会更新关联的订单）
        QueryAccidentDamageByAccidentRes accidentOrderInfo = bfcInsuranceService.queryAccidentDamageByAccidentNo(QueryAccidentDamageByAccidentReq.newBuilder().setAccidentNo(req.getAccidentNo()).build());
        if (null == accidentOrderInfo){
            return builder.build();
        }

        if (accidentOrderInfo.getRelateOrderFlag() == 3){
            builder.setAccidentOrderInfo(AccidentOrderInfo.newBuilder()
                    .setRelateType(3)
                    .setOrderRemark(accidentOrderInfo.getRemark())
                    .build());
        }
        String orderNo = accidentOrderInfo.getRelateOrderNo();
        int orderType = accidentOrderInfo.getRelateOrderType();
        if (StringUtils.isBlank(orderNo) || orderType == 0){
            return builder.build();
        }

        // 查询订单信息
        RepairMatchOrderInfoData repairMatchOrderInfoData = queryOrderInfoByOrderNoAndType(orderNo, orderType, accidentOrderInfo.getVin());
        if (null == repairMatchOrderInfoData){
            return builder.build();
        }
        builder.setAccidentOrderInfo(AccidentOrderInfo.newBuilder()
                .setOrderNo(orderNo)
                .setOrderType(orderType)
                .setStartTime(repairMatchOrderInfoData.getStartTime())
                .setEndTime(repairMatchOrderInfoData.getEndTime())
                .setVehicleVin(repairMatchOrderInfoData.getVehicleVin())
                .setOrderRemark(accidentOrderInfo.getRemark())
                .setRelateType(accidentOrderInfo.getRelateType())
                .setCompanyName(repairMatchOrderInfoData.getCompanyName())
                .setViolationName(repairMatchOrderInfoData.getViolationName())
                .setViolationTelNo(repairMatchOrderInfoData.getViolationTelNo())
                .setVehicleClientMaintenanceFee(repairMatchOrderInfoData.getVehicleClientMaintenanceFee())
                .setClientInspectTag(repairMatchOrderInfoData.getClientInspectTag())
                .setClientUpkeepTag(repairMatchOrderInfoData.getClientUpkeepTag())
                //门店订单信息
                .setServiceType(repairMatchOrderInfoData.getServiceType())
                .setServiceContent(repairMatchOrderInfoData.getServiceContent())
                .build());
        return builder.build();
    }

    /**
     * 根据订单号和订单类型查询订单信息
     * @param orderNo 订单编号
     * @param orderType 订单类型 1=长租订单 2=门店订单 3=渠道订单 4=内部订单 5=分时订单
     * @return
     */
    private RepairMatchOrderInfoData queryOrderInfoByOrderNoAndType(String orderNo, Integer orderType, String vin) {
        RepairMatchOrderInfoData repairMatchOrderInfoData = new RepairMatchOrderInfoData();
        RelationOrderTypeEnum orderTypeEnum = RelationOrderTypeEnum.getEnumByType(orderType);
        if (null == orderTypeEnum){
            return repairMatchOrderInfoData;
        }
        switch (orderTypeEnum){
            //长租订单
            case LONG_RENT_ORDER:
                List<RepairMatchOrderInfoData> repairMatchOrderInfoList = relationOrderInfoService.queryLongRentOrder(null, null, null, orderNo);
                if (CollectionUtils.isEmpty(repairMatchOrderInfoList)){
                    throw new ServiceException("未查询到长租订单信息");
                }
                if (repairMatchOrderInfoList.size() > 1){
                    repairMatchOrderInfoData =  handleLongRentOrder(repairMatchOrderInfoList, vin);
                }else {
                    repairMatchOrderInfoData = repairMatchOrderInfoList.get(0);
                }
                break;
            //门店订单
            case STORE_ORDER:
                repairMatchOrderInfoData = relationOrderInfoService.queryMdOrderByOrderNo(orderNo);
                break;
            case CHANNEL_ORDER:
                repairMatchOrderInfoData =  relationOrderInfoService.queryTongeYeOrderByOrderNo(orderNo);
                break;
            case INTERNAL_ORDER:
            case FREQUENCY_ORDER:
                repairMatchOrderInfoData =  relationOrderInfoService.queryInnerOrderByOrderNo(orderNo);
                break;
            }
        return repairMatchOrderInfoData;
    }

    /**
     * 长租返回多条订单处理
     * @param repairMatchOrderInfoList
     * @param vin
     * @return
     */
    public RepairMatchOrderInfoData handleLongRentOrder(List<RepairMatchOrderInfoData> repairMatchOrderInfoList, String vin){
        RepairMatchOrderInfoData matchOrderInfoData = repairMatchOrderInfoList.get(0);
        // 开始时间
        LocalDateTime minStartTime = LocalDateTime.parse(matchOrderInfoData.getStartTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        // 结束时间
        String endTime = matchOrderInfoData.getEndTime();
        LocalDateTime maxEndTime = null;
        if (StringUtils.isNotBlank(endTime)){
            maxEndTime = LocalDateTime.parse(matchOrderInfoData.getEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        for (RepairMatchOrderInfoData orderInfo : repairMatchOrderInfoList) {
            // 取最早的订单开始时间
            LocalDateTime currentStartTime = LocalDateTime.parse(orderInfo.getStartTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            // 如果当前时间比最小时间早
            if (currentStartTime.isBefore(minStartTime)) {
                matchOrderInfoData.setStartTime(orderInfo.getStartTime());
            }
            // 取最晚的订单结束时间/如果订单结束时间为空就取空
            if (StringUtils.isBlank(orderInfo.getEndTime())){
                matchOrderInfoData.setEndTime("");
            }else if (maxEndTime != null && LocalDateTime.parse(orderInfo.getEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).isAfter(maxEndTime)){
                matchOrderInfoData.setEndTime(orderInfo.getEndTime());
            }
            // 如果车架号一直则返回相同的车架号
            if (orderInfo.getVehicleVin().equals(vin)){
                matchOrderInfoData.setVehicleVin(vin);
                matchOrderInfoData.setVehicleClientMaintenanceFee(orderInfo.getVehicleClientMaintenanceFee());
                matchOrderInfoData.setClientInspectTag(orderInfo.getClientInspectTag());
                matchOrderInfoData.setClientUpkeepTag(orderInfo.getClientUpkeepTag());
            }
        }
        return matchOrderInfoData;
    }

    @Override
    public QueryRelationOrderInfoListRes queryRelationOrderInfoList(QueryRelationOrderInfoListReq req) {
        QueryRelationOrderInfoListResponse response = new QueryRelationOrderInfoListResponse();
        response.setOrderInfos(queryOrderInfoBySendRepairTime(req.getVin(), req.getVehicleNo(), req.getSendRepairTime()));
        return PbConvertUtil.generateProtoBuffer(response, QueryRelationOrderInfoListRes.class);
    }

    /**
     * 查询订单
     * @param vin
     * @param vehicleNo
     * @param sendRepairTime
     * @return
     */
    private List<RepairMatchOrderInfoData> queryOrderInfoBySendRepairTime(String vin, String vehicleNo, String sendRepairTime){
        List<RepairMatchOrderInfoData> orderInfoDataList = new ArrayList<>();
        // 查询长租订单
        List<RepairMatchOrderInfoData> longRentOrderList = relationOrderInfoService.queryLongRentOrder(vin, vehicleNo, sendRepairTime, StringUtils.EMPTY);
        if (CollectionUtils.isNotEmpty(longRentOrderList)){
            orderInfoDataList.addAll(longRentOrderList);
        }

        // 查询md订单
        List<RepairMatchOrderInfoData> repairMatchOrderInfoList = relationOrderInfoService.queryMdOrder(vin, vehicleNo, sendRepairTime);
        if (CollectionUtils.isNotEmpty(repairMatchOrderInfoList)){
            orderInfoDataList.addAll(repairMatchOrderInfoList);
        }

        // 查询桐叶订单
        List<RepairMatchOrderInfoData> tongyeOrderList = relationOrderInfoService.queryTongeYeOrder(vin, vehicleNo, sendRepairTime);
        if (CollectionUtils.isNotEmpty(tongyeOrderList)){
            orderInfoDataList.addAll(tongyeOrderList);
        }

        // 查询内部订单
        List<RepairMatchOrderInfoData> innerOrderList = relationOrderInfoService.queryInnerOrder(vin, vehicleNo, sendRepairTime);
        if (CollectionUtils.isNotEmpty(innerOrderList)){
            orderInfoDataList.addAll(innerOrderList);
        }
        return orderInfoDataList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateVehicleRepairTaskRes createVehicleRepairTask(CreateVehicleRepairTaskReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        String vin = req.getVin();
        if (StringUtils.isBlank(vin)){
            throw new ServiceException("车架号不能为空");
        }
        //车辆里程判断
        if (queryVehicleMile(vin) > req.getVehicleMile()){
            throw new ServiceException("最多输入10位数字，且须输入大于等于当前里程的数字");
        }
        // 构建车辆维修任务参数 - 一次可以创建多个维修任务
        List<VehicleRepairTask> vehicleRepairTaskList = buildSaveRepairTask(req);

        //失败原因
        StringBuilder reason = new StringBuilder(StringUtils.EMPTY);
        //遍历创建维修任务
        for (VehicleRepairTask vehicleRepairTask : vehicleRepairTaskList) {
            try{
                // 没有填维修厂不同步维修 不执行替代车逻辑
                if (StringUtils.isBlank(req.getRepairDepotId())){
                    vehicleRepairTask.setRepairTaskStatus(RepairTaskStatusEnum.UNDER_PROCESS.getCode());
                    //更新车辆公里数
                    mdDataProxy.updateVehicleLabelByVin(UpdateVehicleLabelByVinReq.newBuilder().setVin(vehicleRepairTask.getVin()).setVehicleMileage(req.getVehicleMile()).build());
                    // 新增维修任务
                    tableVehicleRepairTaskService.insert(vehicleRepairTask);
                }else {
                    vehicleRepairTask.setRepairTaskStatus(RepairTaskStatusEnum.UNDER_ACCEPT.getCode());
                    // 同步维修系统
                    syncRepairTaskToMtc(vehicleRepairTask);
                    // 新增维修任务
                    tableVehicleRepairTaskService.insert(vehicleRepairTask);
                    //更新车辆公里数
                    mdDataProxy.updateVehicleLabelByVin(UpdateVehicleLabelByVinReq.newBuilder().setVin(vehicleRepairTask.getVin()).setVehicleMileage(req.getVehicleMile()).build());
                    taskExecutor.execute(()->{
                        // 是否需要生成替代车逻
                        doReplaceTask(vehicleRepairTask, req.getUserName());
                    });

                    // 维修任务创建后 自动生成一条送修工单
                    createVehicleRepairWork(vehicleRepairTask, req.getCurrentUser());
                }

                // 附件处理
                List<AttachmentInfo> attachmentInfoList = req.getAttachmentInfosList();
                if (CollectionUtils.isNotEmpty(attachmentInfoList)){
                    for (AttachmentInfo attachmentInfo : attachmentInfoList) {
                        tableAttachmentService.insertAttachment(vehicleRepairTask.getTaskNo(), attachmentInfo.getFilePath(), attachmentInfo.getFileName(), FileTypeEnum.REPAIR.getType(), attachmentInfo.getContentType(), attachmentInfo.getFileSize(), req.getCurrentUser());
                    }
                }
                // 记录日志
                tableVehicleRepairLogService.saveLog(vehicleRepairTask.getTaskNo(), "维修创建", currentUser.getNickName(), currentUser.getUserAccount());
            }catch (Exception e){
                log.error("创建维修任务失败:"+ e.getLocalizedMessage(), e);
                reason.append("创建").append(RepairTypeEnum.getValueByCode(vehicleRepairTask.getRepairType())).append("任务失败，原因：").append(e.getLocalizedMessage());
            }
          }

        if(StringUtils.isNotBlank(reason.toString())){
            return CreateVehicleRepairTaskRes.failed(-2581001, reason.toString());
        }
        if (CollectionUtils.isNotEmpty(vehicleRepairTaskList)){
            List<String> taskNoList = vehicleRepairTaskList.stream().map(VehicleRepairTask::getTaskNo).collect(Collectors.toList());
            CreateVehicleRepairTaskRes.Builder builder = CreateVehicleRepairTaskRes.newBuilder();
            builder.addAllTaskNoList(taskNoList);
            return builder.build();
        }
        return CreateVehicleRepairTaskRes.ok();
    }

    /**
     * 创建送修工单
     * @param vehicleRepairTask
     */
    private void createVehicleRepairWork(VehicleRepairTask vehicleRepairTask, CurrentUser currentUser) {
        Date now = new Date();
        VehicleRepairWork vehicleRepairWork = new VehicleRepairWork();
        vehicleRepairWork.setRepairTaskNo(vehicleRepairTask.getTaskNo());
        vehicleRepairWork.setWorkTaskNo(outDescIdUtil.nextId("SX"));
        vehicleRepairWork.setSendRepairMethod(vehicleRepairTask.getSendRepairMethodType());
        // 送修工单类型 1：送修工单
        vehicleRepairWork.setTaskType(1);
        vehicleRepairWork.setTaskEndTime(now);
        vehicleRepairWork.setTaskStatus(SendRepairMethodEnum.NON_OUR_REPAIR.getCode().equals(vehicleRepairTask.getSendRepairMethodType()) ?  RepairWorkTaskStatusEnum.PROCESSED.getCode() : RepairWorkTaskStatusEnum.UNDER_PROCESS.getCode());
        vehicleRepairWork.setCreateOperAccount(currentUser.getUserAccount());
        vehicleRepairWork.setCreateOperName(currentUser.getNickName());
        vehicleRepairWork.setUpdateOperAccount(currentUser.getUserAccount());
        vehicleRepairWork.setUpdateOperName(currentUser.getNickName());
        tableVehicleRepairWorkService.insert(vehicleRepairWork);
    }

    /**
     *  替代车逻辑处理
     * @param vehicleRepairTask
     */
    private void doReplaceTask(VehicleRepairTask vehicleRepairTask, String userName) {
        DoLongRentTaskData longRentTask = new DoLongRentTaskData();
        longRentTask.setVehicleNo(vehicleRepairTask.getVehicleNo());
        longRentTask.setRepairTypeId(vehicleRepairTask.getRepairType());
        longRentTask.setMtcTaskNo(vehicleRepairTask.getTaskNo());
        longRentTask.setUserName(userName);
        log.info("执行替代车逻辑请求参数：{}", JSON.toJSONString(longRentTask));
        String url = Global.instance.getBdpUrl()+BusinessConstant.DO_LONG_RENT_TASK;
        JSONObject syncRepairResponse = HttpClientUtils.sendRestHttp_post(url, "POST", JSON.toJSONString(longRentTask), null);
        log.info("执行替代车逻辑返回参数：{}", JSON.toJSONString(syncRepairResponse));
        String code = syncRepairResponse.getString("code");
        if (!"0".equals(code)){
            throw new ServiceException(syncRepairResponse.getString("message"));
        }
    }

    /**
     * 构建车辆维修任务参数
     * @param req 创建任务信息
     * @return 组装后的维修任务列表
     */
    private List<VehicleRepairTask> buildSaveRepairTask(CreateVehicleRepairTaskReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        Date now = new Date();
        //送修时间
        Date sendSendRepairTime;
        try {
            sendSendRepairTime = DateUtils.parseDate(req.getSendRepairTime() + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
        } catch (ParseException e) {
           throw new ServiceException("送修时间格式不正确");
        }

        //增加判定车辆的资产状态/实际运营标签/产品线/子产品线 这些字段都有数据，没有的话要报错不能成功创建 (资产状态默认是0，这里不需要判断)
       if (req.getSubProductLine() <= 0 || req.getProductLine() <= 0 || req.getFactOperateTag() <= 0){
           throw new ServiceException("请先确认车辆的资产状态/实际运营标签/产品线/子产品线均有值！");
       }


        List<VehicleRepairTask> result = new ArrayList<>();
        List<Integer> repairTypeList= req.getRepairTypeListList();
        if(repairTypeList == null || repairTypeList.isEmpty()){
            repairTypeList.add(req.getRepairType());
        }
        for (Integer repairType : repairTypeList) {
            // 事故类型判断
            if (RepairTypeEnum.INSURED_MAINTENANCE.getCode().equals(repairType)){
                if (StringUtils.isBlank(req.getAccidentNo())){
                    throw new ServiceException("事故维修一定要关联事故任务!");
                }
                if (StringUtils.isBlank(req.getOrderNo()) && req.getRelateType() == 1){
                    throw new ServiceException("事故维修一定要有关联订单或无关联订单的说明!");
                }
            }


            //生成任务编号
            String taskNo = outDescIdUtil.nextId(RepairTypeEnum.getTaskNoByCode(repairType));

            VehicleRepairTask vehicleRepairTask = new VehicleRepairTask();
            try {
                PbConvertUtil.copyFieldsFromPbMessage(req, vehicleRepairTask);
            } catch (Exception e) {
                e.printStackTrace();
            }

            vehicleRepairTask.setTotalMileage(req.getVehicleMile());
            vehicleRepairTask.setTaskNo(taskNo);
            //维修类型
            vehicleRepairTask.setRepairType(repairType);
            vehicleRepairTask.setSendRepairTime(sendSendRepairTime);
            vehicleRepairTask.setRepairGrade(req.getRepairDepotGrade());
            vehicleRepairTask.setDamagedPartDescribe(req.getDamagedPartDescribe());
            //维修状态，若已选择修理厂，则初始状态为【待接车】，否则为【待分配】
            vehicleRepairTask.setRepairTaskStatus(StringUtils.isBlank(req.getRepairDepotId()) ? RepairTaskStatusEnum.UNDER_PROCESS.getCode() : RepairTaskStatusEnum.UNDER_ACCEPT.getCode());
            //操作人信息
            vehicleRepairTask.setCreateOperName(currentUser.getNickName());
            vehicleRepairTask.setCreateOperAccount(currentUser.getUserAccount());
            vehicleRepairTask.setUpdateOperName(currentUser.getNickName());
            vehicleRepairTask.setUpdateOperAccount(currentUser.getUserAccount());
            vehicleRepairTask.setCreateTime(now);
            vehicleRepairTask.setUpdateTime(now);
            result.add(vehicleRepairTask);
        }
        log.warn("创建维修任务-组装信息：{}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 同步维修任务到mtc
     * @param vehicleRepairTask
     */
    private void syncRepairTaskToMtc(VehicleRepairTask vehicleRepairTask) {
        SaveRepairTaskData saveRepairTaskData = buildMtcSaveRepairTaskRequest(vehicleRepairTask);
        log.info("执行同步维修任务到mtc请求参数：{}", JSON.toJSONString(saveRepairTaskData));
        String requestUrl = Global.instance.getMtcUrl()+BusinessConstant.SYNC_VEHICLE_REPAIR_TASK;
        JSONObject syncRepairResponse = HttpClientUtils.sendRestHttp_post(requestUrl, "POST", JSON.toJSONString(saveRepairTaskData), null);
        log.info("执行同步维修任务到mtc返回参数：{}", JSON.toJSONString(syncRepairResponse));
        String code = syncRepairResponse.getString("code");
        if (!"0".equals(code)){
            throw new ServiceException(syncRepairResponse.getString("message"));
        }
    }

    /**
     * 构建同步mtc维修任务请求参数
     * @param vehicleRepairTask
     * @return
     */
    private SaveRepairTaskData buildMtcSaveRepairTaskRequest(VehicleRepairTask vehicleRepairTask) {
        SaveRepairTaskData saveRepairTaskData = new SaveRepairTaskData();
        saveRepairTaskData.setDispatchTaskSeq(vehicleRepairTask.getTaskNo());
        saveRepairTaskData.setRepairTypeId(String.valueOf(vehicleRepairTask.getRepairType()));
        saveRepairTaskData.setRepairTypeName(RepairTypeEnum.getValueByCode(vehicleRepairTask.getRepairType()));
        saveRepairTaskData.setVehicleNo(vehicleRepairTask.getVehicleNo());
        saveRepairTaskData.setVin(vehicleRepairTask.getVin());
        saveRepairTaskData.setVehicleModelSeq(String.valueOf(vehicleRepairTask.getVehicleModelSeq()));
        saveRepairTaskData.setVehicleModelInfo(vehicleRepairTask.getVehicleModelName());
        saveRepairTaskData.setOrgId(vehicleRepairTask.getOrgId());
        saveRepairTaskData.setOrgName(vehicleRepairTask.getOrgName());
        saveRepairTaskData.setRepairDepotId(vehicleRepairTask.getRepairDepotId());
        saveRepairTaskData.setRepairDepotName(vehicleRepairTask.getRepairDepotName());
        saveRepairTaskData.setTotalMileage(String.valueOf(vehicleRepairTask.getTotalMileage()));
        saveRepairTaskData.setTaskCreateTime(DateUtil.format(vehicleRepairTask.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        saveRepairTaskData.setDamagedPartDescribe(vehicleRepairTask.getDamagedPartDescribe());
        saveRepairTaskData.setAccidentNo(vehicleRepairTask.getAccidentNo());
        saveRepairTaskData.setAccidentReportNumber(StringUtils.EMPTY);
        saveRepairTaskData.setRepairGrade(vehicleRepairTask.getRepairGrade());
        saveRepairTaskData.setSendRepairTime(DateUtil.format(vehicleRepairTask.getSendRepairTime(), "yyyy-MM-dd HH:mm:ss"));
        // 关联订单信息
        saveRepairTaskData.setAssociatedOrder(vehicleRepairTask.getOrderNo());
        saveRepairTaskData.setOrderType(vehicleRepairTask.getOrderType());
        saveRepairTaskData.setRelateType(vehicleRepairTask.getRelateType());
        saveRepairTaskData.setOrderRemark(vehicleRepairTask.getOrderRemark());
        return saveRepairTaskData;
    }


    @Override
    public QueryRepairDepotListRes queryRepairDepotList(QueryRepairDepotListReq req) {
        QueryRepairDepotListRes.Builder builder = QueryRepairDepotListRes.newBuilder();
        HashMap<String, Object> request = new HashMap<>();
        request.put("repairDepotGradeList",  req.getRepairDepotGradeList());
        request.put("repairDepotName", req.getRepairDepotName());
        if (StringUtils.isBlank(req.getOrgId())){
            request.put("orgId", "00");
        }else {
            request.put("orgId", req.getOrgId());
        }

        request.put("isAll", req.getIsAll());

        String requestParam = JSONObject.toJSONString(request);
        log.info("根据条件修理厂 request：{}", requestParam);
        JSONObject response = HttpClientUtils.sendRestHttp_post(Global.instance.getMtcUrl()+BusinessConstant.QUERY_REPAIR_DEPOT_LIST, "POST", requestParam, null);
        log.info("根据条件修理厂 response：{}", response);
        String code = response.getString("code");
        if (!"0".equals(code)){
            return builder.build();
        }
        List<RepairDepotInfoData> depotInfoList = JSONArray.parseArray(response.getString("data"), RepairDepotInfoData.class);
        if (CollectionUtils.isEmpty(depotInfoList)){
            return builder.build();
        }
        for (RepairDepotInfoData repairDepotInfoData : depotInfoList) {
            builder.addInfo(RepairDepotInfo.newBuilder()
                    .setRepairDepotGrade(repairDepotInfoData.getRepairDepotGrade())
                    .setRepairDepotType(repairDepotInfoData.getRepairDepotType())
                    .setRepairDepotId(repairDepotInfoData.getRepairDepotId())
                    .setRepairDepotName(repairDepotInfoData.getRepairDepotName())
                    .setTaxRate(repairDepotInfoData.getTaxRate())
                    .setRepairDepotSapCode(repairDepotInfoData.getRepairDepotSapCode())
                    .setCurrentRepairNum(repairDepotInfoData.getCurrentRepairNum())
                    .setFreePickupDeliveryService(repairDepotInfoData.getFreePickupDeliveryService())
                    .setMobileMaintenanceService(repairDepotInfoData.getMobileMaintenanceService())
                    .setIsProvidedTowingService(repairDepotInfoData.getIsProvidedTowingService())
                    .setIsFreeTowing(repairDepotInfoData.getIsFreeTowing())
                    .build());
        }
        return builder.build();
    }

    @Override
    public RelateVehicleRepairTaskDepotRes relateVehicleRepairTaskDepot(RelateVehicleRepairTaskDepotReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        VehicleRepairTask vehicleRepairTask;
        if (StringUtils.isNotBlank(req.getTaskNo())){
            vehicleRepairTask = tableVehicleRepairTaskService.selectByTaskNo(req.getTaskNo());
        }else {
            vehicleRepairTask = tableVehicleRepairTaskService.selectById(req.getId());
        }
        if (null == vehicleRepairTask){
            throw new ServiceException("维修任务不存在");
        }
        if (!vehicleRepairTask.getRepairTaskStatus().equals(RepairTaskStatusEnum.UNDER_PROCESS.getCode())){
            throw new ServiceException("当前任务状态不能关联维修厂");
        }
        // 组装参数
        vehicleRepairTask.setRepairGrade(req.getRepairDepotGrade());
        vehicleRepairTask.setRepairDepotId(req.getRepairDepotId());
        vehicleRepairTask.setRepairDepotName(req.getRepairDepotName());
        vehicleRepairTask.setRepairDepotType(req.getRepairDepotType());
        vehicleRepairTask.setRepairTaskStatus(RepairTaskStatusEnum.UNDER_ACCEPT.getCode());
        vehicleRepairTask.setUpdateOperAccount(currentUser.getUserAccount());
        vehicleRepairTask.setUpdateOperName(currentUser.getNickName());
        // 选择送修厂后，默认为未登记
        vehicleRepairTask.setLeavingFactory(LeavingFactoryEnum.UNDER_NOT_LEAVING_FACTORY.getCode());
        // 通知维修系统
        syncRepairTaskToMtc(vehicleRepairTask);

        // 更新
        tableVehicleRepairTaskService.updateById(vehicleRepairTask);

        // 维修任务创建后 自动生成一条送修工单
        createVehicleRepairWork(vehicleRepairTask, req.getCurrentUser());

        taskExecutor.execute(()->{
            doReplaceTask(vehicleRepairTask, req.getUserName());
        });
        // 记录操作日志
        tableVehicleRepairLogService.saveLog(vehicleRepairTask.getTaskNo(), "指定修理厂", StrUtil.format("指定{}修理厂维修车辆", vehicleRepairTask.getRepairDepotName()), currentUser.getNickName(), currentUser.getUserAccount());
        return RelateVehicleRepairTaskDepotRes.ok();
    }

    @Override
    public CloseVehicleRepairTaskRes closeVehicleRepairTask(CloseVehicleRepairTaskReq req) {
        VehicleRepairTask vehicleRepairTask = tableVehicleRepairTaskService.selectById(req.getId());
        if (null == vehicleRepairTask){
            throw new ServiceException("维修任务不存在");
        }
        // 状态校验 只有待分配和维修中可以关闭
        if (!vehicleRepairTask.getRepairTaskStatus().equals(RepairTaskStatusEnum.UNDER_PROCESS.getCode()) && !vehicleRepairTask.getRepairTaskStatus().equals(RepairTaskStatusEnum.UNDER_REPAIR.getCode())){
            throw new ServiceException("当前任务状态不能关闭");
        }
        taskExecutor.execute(() -> {
            // 替代车逻辑处理
            doCloseVehicleRepairTask(vehicleRepairTask.getTaskNo());
        });
        // 关闭任务
        vehicleRepairTask.setRepairTaskStatus(RepairTaskStatusEnum.UNDER_CLOSE.getCode());
        vehicleRepairTask.setLeavingFactory(LeavingFactoryEnum.CLOSE_LEAVING_FACTORY.getCode());
        tableVehicleRepairTaskService.updateById(vehicleRepairTask);

        // 关闭出厂登记
        doCloseLeavingFactory(vehicleRepairTask.getTaskNo());

        // 记录日志
        tableVehicleRepairLogService.saveLog(vehicleRepairTask.getTaskNo(), "关闭任务", req.getCurrentUser().getNickName(), req.getCurrentUser().getUserAccount());
        return CloseVehicleRepairTaskRes.ok();
    }

    /**
     * 关闭出厂登记逻辑
     * @param taskNo
     */
    private void doCloseLeavingFactory(String taskNo) {
        HashMap<String, String> requestMap = new HashMap<>();
        requestMap.put("taskNo", taskNo);
        log.info("执行关闭维修任务出厂登记到mtc请求参数：{}", JSON.toJSONString(requestMap));
        String requestUrl = Global.instance.getMtcUrl()+BusinessConstant.CLOSE_LEAVING_FACTORY;
        JSONObject syncRepairResponse = HttpClientUtils.sendRestHttp_post(requestUrl, "POST", JSON.toJSONString(requestMap), null);
        log.info("执行关闭维修任务出厂登记到mtc返回参数：{}", JSON.toJSONString(syncRepairResponse));
    }

    private void doCloseVehicleRepairTask(String taskNo) {
        HashMap<String, String> requestMap = new HashMap<>();
        requestMap.put("mtcTaskNo", taskNo);
        log.info("执行关闭维修任务替车逻辑请求参数：{}", JSON.toJSONString(requestMap));
        String url = Global.instance.getBdpUrl()+BusinessConstant.CANCEL_REPAIR_TASK;
        JSONObject syncRepairResponse = HttpClientUtils.sendRestHttp_post(url, "POST", JSON.toJSONString(requestMap), null);
        log.info("执行关闭维修任务替车逻辑返回参数：{}", JSON.toJSONString(syncRepairResponse));
        String code = syncRepairResponse.getString("code");
        if (!"0".equals(code)){
            throw new ServiceException(syncRepairResponse.getString("message"));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UnRelateOrderRes unRelateOrderAndRepair(UnRelateOrderReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        VehicleRepairTask vehicleRepairTask = tableVehicleRepairTaskService.selectById(req.getId());
        if (null == vehicleRepairTask){
            throw new ServiceException("维修任务不存在");
        }
        String orderNo = vehicleRepairTask.getOrderNo();
        vehicleRepairTask.setOrderNo(StringUtils.EMPTY);
        vehicleRepairTask.setOrderRemark(StringUtils.EMPTY);
        vehicleRepairTask.setOrderType(0);
        vehicleRepairTask.setRelateType(0);
        tableVehicleRepairTaskService.updateById(vehicleRepairTask);

        // 日志记录
        tableVehicleRepairLogService.saveLog(vehicleRepairTask.getTaskNo(), "解除关联订单", "解除关联订单, 订单号:"+ orderNo, currentUser.getNickName(), currentUser.getUserAccount());

        //同步维修系统
        try{
            SynVehicleRepairRelateOrderRequest request = new SynVehicleRepairRelateOrderRequest();
            request.setTaskNo(vehicleRepairTask.getTaskNo());
            //1-变更关联订单信息 2-解除关联
            request.setOperateType(2);
            log.info("执行同步维修关联订单到mtc请求参数：{}", JSON.toJSONString(request));
            String requestUrl = Global.instance.getMtcUrl()+BusinessConstant.SYN_VEHICLE_REPAIR_RELATE_ORDER;
            JSONObject syncRepairResponse = HttpClientUtils.sendRestHttp_post(requestUrl, "POST", JSON.toJSONString(request), null);
            log.info("执行同步维修关联订单到mtc返回参数：{}", JSON.toJSONString(syncRepairResponse));
        }catch (Exception e){
            log.info("执行同步维修关联订单到mtc失败：{}", e.getLocalizedMessage(), e);
        }
        return UnRelateOrderRes.ok();

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RelateOrderAndRepairRes relateOrderAndRepair(RelateOrderAndRepairReq req) {
        CurrentUser currentUser = req.getCurrentUser();
        VehicleRepairTask vehicleRepairTask = tableVehicleRepairTaskService.selectById(req.getId());
        if (null == vehicleRepairTask){
            throw new ServiceException("维修任务不存在");
        }
        vehicleRepairTask.setOrderNo(req.getOrderNo());
        vehicleRepairTask.setOrderRemark(req.getOrderRemark());
        vehicleRepairTask.setOrderType(req.getOrderType());
        vehicleRepairTask.setRelateType(req.getRelateType());

        // 更新
        tableVehicleRepairTaskService.updateById(vehicleRepairTask);

        // 日志记录
        String logContent = getReleOrderLog(vehicleRepairTask);
        tableVehicleRepairLogService.saveLog(vehicleRepairTask.getTaskNo(), "关联订单", logContent, currentUser.getNickName(), currentUser.getUserAccount());

        //同步维修系统
        try{
            SynVehicleRepairRelateOrderRequest request = new SynVehicleRepairRelateOrderRequest();
            request.setTaskNo(vehicleRepairTask.getTaskNo());
            //1-变更关联订单信息 2-解除关联
            request.setOperateType(1);
            request.setRelateType(req.getRelateType());
            request.setOrderType(req.getOrderType());
            request.setAssociatedOrder(req.getOrderNo());
            request.setOrderRemark(req.getOrderRemark());
            log.info("执行同步维修关联订单到mtc请求参数：{}", JSON.toJSONString(request));
            String requestUrl = Global.instance.getMtcUrl()+BusinessConstant.SYN_VEHICLE_REPAIR_RELATE_ORDER;
            JSONObject syncRepairResponse = HttpClientUtils.sendRestHttp_post(requestUrl, "POST", JSON.toJSONString(request), null);
            log.info("执行同步维修关联订单到mtc返回参数：{}", JSON.toJSONString(syncRepairResponse));
        }catch (Exception e){
            log.info("执行同步维修关联订单到mtc失败：{}", e.getLocalizedMessage(), e);
        }
        return RelateOrderAndRepairRes.ok();
    }

    /**
     * 判断订单是否存在
     * @param vehicleRepairTask
     */
    private void judgeOrderExist(VehicleRepairTask vehicleRepairTask) {

        RepairMatchOrderInfoData repairMatchOrderInfoData = queryOrderInfoByOrderNoAndType(vehicleRepairTask.getOrderNo(), vehicleRepairTask.getOrderType(), vehicleRepairTask.getVin());
        if (null == repairMatchOrderInfoData){
            throw new ServiceException("未查询到订单信息!");
        }
        // 去除事故任务
        if (vehicleRepairTask.getRepairType().equals(RepairTypeEnum.INSURED_MAINTENANCE.getCode())){
            return;
        }
        // 去除非手工关联
        if (!vehicleRepairTask.getRelateType().equals(RelateTypeEnum.HAND_RELATE.getCode())){
            return;
        }
        String sendRepairTime = DateUtil.format(vehicleRepairTask.getSendRepairTime(), "yyyy-MM-dd");
        List<RepairMatchOrderInfoData> repairMatchOrderInfoDataList = queryOrderInfoBySendRepairTime(vehicleRepairTask.getVin(), vehicleRepairTask.getVehicleNo(), sendRepairTime);
        // 非事故维修 手工输入的订单号如在【根据送修日期自动匹配到的订单】列表中，则报错提示用户去发生事故的订单号子表中操作关联
        if (CollectionUtils.isNotEmpty(repairMatchOrderInfoDataList)){
            Map<String, RepairMatchOrderInfoData> orderInfoDataMap = repairMatchOrderInfoDataList.stream()
                    .collect(Collectors.toMap(orderInfo -> orderInfo.getOrderNo(), Function.identity()));
            if (orderInfoDataMap.containsKey(vehicleRepairTask.getOrderNo())){
                throw new ServiceException("该订单在自动匹配订单页面，请去自动匹配订单页面操作关联");
            }
        }

       /* if (repairMatchOrderInfoData.getOrderType().equals(RelationOrderTypeEnum.CHANNEL_ORDER.getCode()) && !repairMatchOrderInfoData.getVehicleNo().equals(vehicleRepairTask.getVehicleNo())){
            throw new ServiceException(StrUtil.format("订单关联的车牌号:{}与维修任务车牌号:{}不一致，请检查", repairMatchOrderInfoData.getVehicleNo(), vehicleRepairTask.getVehicleNo()));
        }
        if (!repairMatchOrderInfoData.getOrderType().equals(RelationOrderTypeEnum.CHANNEL_ORDER.getCode()) && !repairMatchOrderInfoData.getVehicleVin().equals(vehicleRepairTask.getOrderRemark())){
            throw new ServiceException(StrUtil.format("订单关联的车架号:{}与维修任务车架号:{}不一致，请检查", repairMatchOrderInfoData.getVehicleVin(), vehicleRepairTask.getVin()));
        }*/
    }

    /**
     * 获取关联订单日志
     * @param vehicleRepairTask
     * @return
     */
    public String getReleOrderLog(VehicleRepairTask vehicleRepairTask){
        StringBuilder logContent = new StringBuilder();
        logContent.append("关联订单，关联方式：").append(RelateTypeEnum.getValueByCode(vehicleRepairTask.getRelateType()));
        switch (vehicleRepairTask.getRelateType()){
            case 1:
                logContent.append(",关联订单号").append(vehicleRepairTask.getOrderNo());
                break;
            case 2:
                logContent.append(",关联订单号").append(vehicleRepairTask.getOrderNo()).append(",手工指定订单原因:").append(vehicleRepairTask.getOrderRemark());
                break;
            case 3:
                logContent.append(",无关联订单原因").append(vehicleRepairTask.getOrderRemark());
                break;
            default:
                break;
        }
        return logContent.toString();
    }

    @Override
    public GetRepairFileListRes getRepairFileList(GetRepairFileListReq req) {

        VehicleRepairTask vehicleRepairTask = tableVehicleRepairTaskService.selectByTaskNo(req.getTaskNo());
        if (vehicleRepairTask == null){
            throw new ServiceException(ResultCode.COMMON_FAIL, "维修任务不存在");
        }

        GetRepairFileListRes.Builder builder = GetRepairFileListRes.newBuilder();
        // 情况说明
        builder.setSituationDesc(vehicleRepairTask.getMiscDesc());
        // 查生效的
        List<Attachment> repairFiles = tableAttachmentService.selectByRelationAndStatus(req.getTaskNo(), FileTypeEnum.REPAIR.getType(), 0);
        if (CollectionUtils.isNotEmpty(repairFiles)){
            for (Attachment repairFile : repairFiles) {
                builder.addRepairFiles(
                        AttachmentInfo.newBuilder()
                                .setFileId(repairFile.getId())
                                .setFileName(repairFile.getFileName())
                                .setFilePath(repairFile.getFilePath())
                                .setContentType(repairFile.getContentType())
                );
            }
        }

        // 查已删除的
        List<Attachment> deleteFiles = tableAttachmentService.selectByRelationAndStatus(req.getTaskNo(), FileTypeEnum.REPAIR.getType(), 1);
        if (CollectionUtils.isNotEmpty(deleteFiles)){
            for (Attachment repairFile : deleteFiles) {
                builder.addDeleteFiles(
                        AttachmentInfo.newBuilder()
                                .setFileId(repairFile.getId())
                                .setFileName(repairFile.getFileName())
                                .setFilePath(repairFile.getFilePath())
                                .setContentType(repairFile.getContentType())
                );
            }
        }
        // 查询维修系统获取照片信息
        HashMap<String, Object> request = new HashMap<>();
        request.put("taskNo",  req.getTaskNo());

        String requestParam = JSONObject.toJSONString(request);
        log.info("查询维修系统获取照片信息 request：{}", requestParam);
        JSONObject response = HttpClientUtils.sendRestHttp_post(Global.instance.getMtcUrl()+BusinessConstant.QUERY_REPAIR_FILE_LIST, "POST", requestParam, null);
        log.info("查询维修系统获取照片信息 response：{}", response);
        String code =  response.getString("code");
        if (!"0".equals(code)){
            return builder.build();
        }
        List<VehicleRepairPicData> vehicleRepairPicList = JSONArray.parseArray(response.getString("data"), VehicleRepairPicData.class);
        if (CollectionUtils.isNotEmpty(vehicleRepairPicList)){
            for (VehicleRepairPicData vehicleRepairPicData : vehicleRepairPicList) {
                PicTypeEnum enumByCode = PicTypeEnum.getEnumByCode(vehicleRepairPicData.getPicType());
                if (enumByCode == null){
                    continue;
                }
                String picUrl = vehicleRepairPicData.getPicUrl();
                switch (enumByCode){
                    case DAMAGE_PART:
                        // 损坏部位图片
                        builder.addDamageFiles(
                                AttachmentInfo.newBuilder()
                                        .setFilePath(picUrl)
                        );
                        break;
                    case ACCEPTANCE_PICTURE:
                        // 验收图片
                        builder.addCheckFiles(
                                AttachmentInfo.newBuilder()
                                        .setFilePath(picUrl)
                        );
                        break;
                    case ACCEPTANCE_VIDEO:
                        // 验收视频
                        builder.addCheckVideos(
                                AttachmentInfo.newBuilder()
                                        .setFilePath(picUrl)
                        );
                        break;
                    case RESPONSIBILITY_CERTIFICATE:
                        // 责任认定书
                        builder.addRepairDoc(
                                AttachmentInfo.newBuilder()
                                        .setFilePath(picUrl)
                        );
                        break;
                    case INSURANCE_LOSS_BILL:
                        // 保司定损单
                        builder.addLossDoc(
                                AttachmentInfo.newBuilder()
                                        .setFilePath(picUrl)
                        );
                        break;
                    case DRIVER_LICENSE_AND_DRIVING_LICENSE:
                        // 我方驾驶证和行驶证
                        builder.addVehicleDoc(
                                AttachmentInfo.newBuilder().setFilePath(picUrl)
                        );
                        break;
                    case DAMAGE_PART_VIDEO:
                        // 损坏部位视频
                        builder.addDamageVideos(
                                AttachmentInfo.newBuilder()
                                        .setFilePath(picUrl)
                        );
                    default:
                        break;
                }
            }
        }
        // 查询事故照片
        if (StringUtils.isNotBlank(vehicleRepairTask.getAccidentNo())){
            QueryAccidentDamageByAccidentRes accidentRes = bfcInsuranceService.queryAccidentDamageByAccidentNo(QueryAccidentDamageByAccidentReq.newBuilder()
                    .setAccidentNo(vehicleRepairTask.getAccidentNo())
                 .build());

            List<AccidentFileData> accidentFileListList = accidentRes.getAccidentFileListList();

            if (CollectionUtils.isNotEmpty(accidentFileListList)){
                for (AccidentFileData accidentFileData : accidentFileListList) {
                    builder.addAccidentFiles(
                            AttachmentInfo.newBuilder()
                                    .setFilePath(accidentFileData.getFilePath())
                                    .setFileType(accidentFileData.getFileType())
                                    .setFileName(getAccidentCaseFile(accidentFileData))
                    );
                }
            }
        }

        return builder.build();
    }

    public String getAccidentCaseFile(AccidentFileData accidentFileData){
        // 6=侧前方全景 7=侧后方全景 8=本车受损部位 9=本车车牌 10=物损 20=其他'
        String fileName = null;
        switch (accidentFileData.getFileType()){
            case 6 :
                fileName = "侧前方全景";
                break;
            case 7:
                fileName = "侧前方全景";
                break;
            case 8:
                fileName = "本车受损部位";
                break;
            case 9:
                fileName = "本车车牌";
                break;
            case 10:
                fileName = "物损";
                break;
            case 20:
                fileName = "其他";
                break;
            default:
                fileName = accidentFileData.getFileName();
                break;
        }
        return fileName;
    }

    @Override
    public DeleteRepairFileRes deleteRepairFile(DeleteRepairFileReq req) {

        Attachment attachment = tableAttachmentService.selectById(req.getFileId());
        if (null == attachment){
            throw new ServiceException("文件不存在");
        }
        // 更新
        attachment.setIsDeleted(1);
        tableAttachmentService.UpdateById(attachment);
        return DeleteRepairFileRes.ok();
    }

    @Override
    public RestoreRepairFileRes restoreRepairFile(RestoreRepairFileReq req) {
        Attachment attachment = tableAttachmentService.selectById(req.getFileId());
        if (null == attachment){
            throw new ServiceException("文件不存在");
        }
        // 更新
        attachment.setIsDeleted(0);
        tableAttachmentService.UpdateById(attachment);

        return RestoreRepairFileRes.ok();
    }

    @Override
    public AddRepairFileRes addRepairFile(AddRepairFileReq req) {

        List<AttachmentInfo> repairFilesList = req.getRepairFilesList();
        if (CollectionUtils.isEmpty(repairFilesList)){
            throw new ServiceException("请上传附件");
        }
        if (StringUtils.isBlank(req.getTaskNo())){
            throw new ServiceException("维修任务单号不能为空");
        }
        for (AttachmentInfo attachmentInfo : repairFilesList) {
            Attachment attachment = new Attachment();
            attachment.setFileName(attachmentInfo.getFileName());
            attachment.setFilePath(attachmentInfo.getFilePath());
            attachment.setContentType(attachmentInfo.getContentType());
            attachment.setFileSize(attachmentInfo.getFileSize());
            attachment.setRelationId(req.getTaskNo());
            attachment.setFileType(FileTypeEnum.REPAIR.getType());
            tableAttachmentService.insert(attachment);
        }
        return AddRepairFileRes.ok();
    }

    @Override
    public RejectRepairTaskRes rejectRepairTask(RejectRepairTaskReq req) {
        VehicleRepairTask vehicleRepairTask = tableVehicleRepairTaskService.selectByTaskNo(req.getTaskNo());
        if (null == vehicleRepairTask){
            throw new ServiceException("维修任务不存在");
        }
        // 修改
        vehicleRepairTask.setRepairTaskStatus(RepairTaskStatusEnum.UNDER_PROCESS.getCode());
        tableVehicleRepairTaskService.updateById(vehicleRepairTask);

        // 保存日志
        tableVehicleRepairLogService.saveLog(vehicleRepairTask.getTaskNo(), "修理厂驳回", req.getRejectReason(), req.getOperName(), req.getOperAccount());
        return RejectRepairTaskRes.ok();
    }

    @Override
    public QueryTaskInfoByTaskNoRes queryTaskInfoByTaskNo(QueryTaskInfoByTaskNoReq req) {

        VehicleRepairTask vehicleRepairTask = tableVehicleRepairTaskService.selectByTaskNo(req.getTaskNo());
        if (null == vehicleRepairTask){
            throw new ServiceException("维修任务不存在");
        }
        QueryTaskInfoByTaskNoRes.Builder builder = QueryTaskInfoByTaskNoRes.newBuilder()
                .setTaskNo(vehicleRepairTask.getTaskNo())
                .setVin(vehicleRepairTask.getVin())
                .setVehicleNo(vehicleRepairTask.getVehicleNo())
                .setRepairType(vehicleRepairTask.getRepairType())
                .setRepairTaskStatus(vehicleRepairTask.getRepairTaskStatus())
                .setCreateTime(DateUtil.format(vehicleRepairTask.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        return builder.build();
    }

    @Override
    public JudgeOrderVinRelationRes judgeOrderVinRelation(JudgeOrderVinRelationReq req) {
        JudgeOrderVinRelationRes.Builder builder = JudgeOrderVinRelationRes.newBuilder();
        RepairMatchOrderInfoData repairMatchOrderInfoData = queryOrderInfoByOrderNoAndType(req.getOrderNo(), req.getOrderType(), req.getVin());
        if (null == repairMatchOrderInfoData){
            throw new ServiceException(ResultCode.COMMON_FAIL, "未查询到订单信息!");
        }
        // 详情页面不用判断
        if (req.getOrignFlag() != 1){
            List<RepairMatchOrderInfoData> repairMatchOrderInfoDataList = queryOrderInfoBySendRepairTime(req.getVin(), req.getVehicleNo(), req.getSendRepairTime());
            // 非事故维修 手工输入的订单号如在【根据送修日期自动匹配到的订单】列表中，则报错提示用户去发生事故的订单号子表中操作关联
            if (CollectionUtils.isNotEmpty(repairMatchOrderInfoDataList)){
                Map<String, RepairMatchOrderInfoData> orderInfoDataMap = repairMatchOrderInfoDataList.stream()
                        .collect(Collectors.toMap(orderInfo -> orderInfo.getOrderNo(), Function.identity()));
                if (orderInfoDataMap.containsKey(req.getOrderNo())){
                    throw new ServiceException(ResultCode.COMMON_FAIL, "该订单在自动匹配订单页面，请去自动匹配订单页面操作关联");
                }
            }
        }

        if (repairMatchOrderInfoData.getOrderType().equals(RelationOrderTypeEnum.CHANNEL_ORDER.getCode()) && !repairMatchOrderInfoData.getVehicleNo().equals(req.getVehicleNo())){
            builder.setRepeatDataFlag(1);
            builder.setRetMsg(StrUtil.format("订单关联的车牌号:{}与维修任务车牌号:{}不一致，请检查", repairMatchOrderInfoData.getVehicleNo(), req.getVehicleNo()));
        }
        if (!repairMatchOrderInfoData.getOrderType().equals(RelationOrderTypeEnum.CHANNEL_ORDER.getCode()) && !repairMatchOrderInfoData.getVehicleVin().equals(req.getVin())){
            builder.setRepeatDataFlag(1);
            builder.setRetMsg(StrUtil.format("订单关联的车架号:{}与维修任务车架号:{}不一致，请检查", repairMatchOrderInfoData.getVehicleVin(), req.getVin()));
        }
        return builder.build();
    }

    @Override
    public RefreshOrderInfoByOrderNoRes refreshOrderInfoByOrderNo(RefreshOrderInfoByOrderNoReq req) {
        RepairMatchOrderInfoData repairMatchOrderInfoData = queryOrderInfoByOrderNoAndType(req.getOrderNo(), req.getOrderType(), req.getVin());
        if (null == repairMatchOrderInfoData){
            throw new ServiceException(ResultCode.COMMON_FAIL, "未查询到订单信息!");
        }
        RefreshOrderInfoByOrderNoRes.Builder builder = RefreshOrderInfoByOrderNoRes.newBuilder().setOrderNo(repairMatchOrderInfoData.getOrderNo())
                .setOrderStatus(repairMatchOrderInfoData.getOrderStatus())
                .setOrderType(repairMatchOrderInfoData.getOrderType())
                .setStartTime(repairMatchOrderInfoData.getStartTime())
                .setEndTime(repairMatchOrderInfoData.getEndTime())
                .setVehicleVin(repairMatchOrderInfoData.getVehicleVin())
                //长租订单信息
                .setVehicleClientMaintenanceFee(repairMatchOrderInfoData.getVehicleClientMaintenanceFee())
                .setClientUpkeepTag(repairMatchOrderInfoData.getClientUpkeepTag())
                .setClientInspectTag(repairMatchOrderInfoData.getClientInspectTag())
                .setCompanyName(repairMatchOrderInfoData.getCompanyName())
                .setViolationName(repairMatchOrderInfoData.getViolationName())
                .setViolationTelNo(repairMatchOrderInfoData.getViolationTelNo())
                //门店订单信息
                .setServiceType(repairMatchOrderInfoData.getServiceType())
                .setServiceContent(repairMatchOrderInfoData.getServiceContent())
                ;

        return builder.build();
    }

    @Override
    public UpdateLeavingFactoryStatusRes updateLeavingFactoryStatus(UpdateLeavingFactoryStatusReq req) {
        VehicleRepairTask vehicleRepairTask = tableVehicleRepairTaskService.selectByTaskNo(req.getTaskNo());
        if (null == vehicleRepairTask){
            return UpdateLeavingFactoryStatusRes.ok();
        }
        vehicleRepairTask.setLeavingFactory(req.getLeavingFactoryStatus());
        return UpdateLeavingFactoryStatusRes.ok();
    }
}
