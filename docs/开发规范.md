# Auto Care SaaS 开发规范

## 1. 项目结构规范

### 1.1 目录结构
```plaintext
src/
├── main/
│   ├── java/
│   │   └── com/extracme/saas/autocare/
│   │       ├── config/                 # 配置类目录
│   │       │   ├── SwaggerConfig       # Swagger配置
│   │       │   └── WebMvcConfig        # Web MVC配置
│   │       ├── controller/             # 控制器目录
│   │       │   └── api/                # API接口
│   │       ├── service/                # 服务层目录
│   │       │   ├── base/               # 服务接口
│   │       │   └── impl/               # 服务实现
│   │       ├── repository/             # 数据访问层目录
│   │       │   └── impl/               # 数据访问实现
│   │       ├── model/                  # 数据模型目录
│   │       │   ├── entity/             # 数据库实体
│   │       │   ├── dto/                # 数据传输对象
│   │       │   └── vo/                 # 视图对象
│   │       │       └── base/           # 基础视图对象
│   │       ├── mapper/                 # MyBatis映射目录
│   │       │   ├── base/               # 基础Mapper
│   │       │   └── extend/             # 扩展Mapper
│   │       └── common/                 # 公共组件目录
│   │           ├── exception/          # 异常类
│   │           ├── util/               # 工具类
│   │           ├── constant/           # 常量定义
│   │           └── enums/              # 枚举类
│   └── resources/
│       ├── mapper/                     # MyBatis映射文件
│       ├── db/                         # 数据库脚本
│       │   ├── schema-saas.sql         # 数据库结构
│       │   └── data.sql                # 初始化数据
│       ├── application.yml             # 应用配置
│       ├── application-dev.yml         # 开发环境配置
│       ├── application-test.yml        # 测试环境配置
│       └── application-prod.yml        # 生产环境配置
└── test/
    └── java/
        └── com/extracme/saas/autocare/
            ├── service/                # 服务测试
            └── controller/             # 控制器测试
```

### 1.2 包结构规范

#### 基础包结构
- **基础包名**：`com.extracme.saas.autocare`
- **模块划分**：按功能模块划分包结构
- **命名规范**：使用小写字母，多词用点号分隔

#### 各层规范

1. **配置层 (config)**
   - 存放所有配置类
   - 使用 `@Configuration` 注解
   - 配置类命名以 `Config` 结尾
   - 示例：`SwaggerConfig`、`WebMvcConfig`

2. **控制层 (controller)**
   - 统一使用 `@RestController` 注解
   - 路径使用 kebab-case 风格
   - 版本号在路径中体现，如 `/api/v1/users`
   - 使用 `@Api` 和 `@ApiOperation` 注解进行 Swagger 文档说明
   - 所有方法返回 `Result<T>` 类型
   - 所有创建和更新操作使用 POST 方法
   - 控制器类按功能模块划分，如 `UserController`、`RoleController`、`PermissionController`

3. **服务层 (service)**
   - 接口命名：`{Domain}Service`
   - 实现类命名：`{Domain}ServiceImpl`
   - 使用 `@Service` 注解
   - 使用 `@Transactional` 注解管理事务，指定 `rollbackFor = Exception.class`
   - 示例：`UserService`、`UserServiceImpl`

4. **数据访问层 (repository)**
   - 接口命名：`Table{EntityName}Service`
   - 实现类命名：`Table{EntityName}ServiceImpl`
   - 继承 `DefaultTableService<T, K>` 接口
   - 使用 `@Repository` 注解
   - 使用 MyBatis Dynamic SQL 构建查询
   - 空集合返回应使用 `Collections.emptyList()`
   - 计数查询应使用 `count()` 方法而不是实体查询
   - 查询方法必须处理空结果情况
   - 示例：`TableUserService`、`TableUserServiceImpl`

5. **模型层 (model)**
   - **实体类 (entity)**
     - 必须实现 `Serializable` 接口
     - 使用 `@Table` 注解指定表名
     - 字段名与数据库列名保持一致
     - 示例：`SysUser`、`SysRole`
   - **DTO (dto)**
     - 用于数据传输，只接收必要参数
     - 命名以 `DTO` 结尾
     - 创建操作使用 `{Entity}DTO`
     - 更新操作使用 `{Entity}UpdateDTO`
     - 查询操作使用 `{Entity}QueryDTO`
     - 分页查询DTO应继承 `BasePageDTO`
     - 示例：`UserDTO`、`UserUpdateDTO`、`UserQueryDTO`
   - **VO (vo)**
     - 用于视图展示
     - 命名以 `VO` 结尾
     - 字段名应与实体类保持一致，确保兼容 `BeanUtils.copyProperties`
     - 分页查询结果使用 `BasePageVO<T>` 而非 `Result<PageInfo<T>>`
     - 示例：`UserVO`、`UserDetailVO`

6. **Mapper层 (mapper)**
   - **基础Mapper (base)**
     - 由 MyBatis Generator 生成
     - 禁止手动修改
   - **扩展Mapper (extend)**
     - 必须继承基础Mapper
     - 命名以 `ExtendMapper` 结尾
     - 示例：`SysUserExtendMapper`

7. **公共组件 (common)**
   - **异常类 (exception)**
     - 命名以 `Exception` 结尾
     - 业务异常统一使用 `BusinessException`
     - 错误码定义在 `ErrorCode` 枚举类中
     - 示例：`BusinessException`、`UnauthorizedException`
   - **工具类 (util)**
     - 命名以 `Utils` 或 `Util` 结尾
     - 示例：`StringUtils`、`JwtUtil`
   - **常量定义 (constant)**
     - 命名以 `Constants` 结尾
     - 示例：`SystemConstants`
   - **枚举类 (enums)**
     - 命名表示其分类
     - 示例：`ErrorCode`、`PermissionType`

## 2. Git提交规范

提交信息格式：
```
feat: 新功能
fix: 修复问题
docs: 文档更新
style: 代码格式
refactor: 重构
test: 测试相关
chore: 构建相关
```

## 3. 代码规范

### 3.1 Controller层规范
```java
@Api(tags = "用户管理")
@RestController
@RequestMapping("/api/v1/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    @ApiOperation(value = "获取用户信息", notes = "根据用户ID获取用户详细信息")
    @GetMapping("/detail/{id}")
    public Result<UserVO> getUser(
            @ApiParam(value = "用户ID", required = true) @PathVariable Long id) {
        return Result.success(userService.getUser(id));
    }

    @ApiOperation(value = "创建用户", notes = "创建新用户")
    @PostMapping("/create")
    public Result<Long> createUser(
            @ApiParam(value = "用户信息", required = true) @Validated @RequestBody UserDTO userDTO) {
        return Result.success(userService.createUser(userDTO));
    }

    @ApiOperation(value = "更新用户", notes = "更新用户信息")
    @PostMapping("/update")
    public Result<Void> updateUser(
            @ApiParam(value = "用户信息", required = true) @Validated @RequestBody UserUpdateDTO userUpdateDTO) {
        userService.updateUser(userUpdateDTO);
        return Result.success();
    }

    @ApiOperation(value = "分页查询用户列表", notes = "根据条件分页查询用户列表")
    @PostMapping("/list")
    public Result<BasePageVO<UserVO>> getUserList(
            @ApiParam(value = "查询条件", required = true) @RequestBody UserQueryDTO queryDTO) {
        return Result.success(userService.getUserList(queryDTO));
    }
}

// 统一响应格式
@Data
public class Result<T> {
    private Integer code;      // 状态码
    private String message;    // 消息
    private T data;            // 数据

    public static <T> Result<T> success() {
        return success(null);
    }

    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setCode(200);
        result.setMessage("success");
        result.setData(data);
        return result;
    }

    public static <T> Result<T> error(Integer code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }
}
```

### 3.2 Service层规范
```java
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final TableUserService tableUserService;
    private final TableRoleService tableRoleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createUser(UserDTO userDTO) {
        // 检查用户名是否已存在
        if (tableUserService.existsByUsername(userDTO.getUsername())) {
            throw new BusinessException(ErrorCode.USER_ALREADY_EXISTS);
        }

        // 创建用户实体
        SysUser user = new SysUser();
        BeanUtils.copyProperties(userDTO, user);

        // 设置默认值
        if (user.getStatus() == null) {
            user.setStatus(1); // 默认启用
        }

        // 保存用户
        SysUser savedUser = tableUserService.insert(user);
        return savedUser.getId();
    }

    @Override
    public UserVO getUser(Long id) {
        SysUser user = tableUserService.selectById(id);
        if (user == null) {
            return null;
        }

        UserVO userVO = new UserVO();
        BeanUtils.copyProperties(user, userVO);
        return userVO;
    }

    @Override
    public BasePageVO<UserVO> getUserList(UserQueryDTO queryDTO) {
        // 开启分页
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 查询用户列表
        List<SysUser> users = tableUserService.findByCondition(
            queryDTO.getUsername(),
            queryDTO.getMobile(),
            queryDTO.getStatus()
        );

        // 转换为VO
        List<UserVO> userVOs = users.stream().map(user -> {
            UserVO userVO = new UserVO();
            BeanUtils.copyProperties(user, userVO);
            return userVO;
        }).collect(Collectors.toList());

        // 构建分页结果
        PageInfo<SysUser> pageInfo = new PageInfo<>(users);
        return BasePageVO.of(userVOs, pageInfo);
    }
}
```

### 3.3 Repository层规范
```java
/**
 * 用户表数据访问服务接口
 */
public interface TableUserService extends DefaultTableService<SysUser, Long> {

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    Optional<SysUser> findByUsername(String username);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 根据条件查询用户列表
     *
     * @param username 用户名
     * @param mobile 手机号
     * @param status 状态
     * @return 用户列表
     */
    List<SysUser> findByCondition(String username, String mobile, Integer status);
}

/**
 * 用户表数据访问服务实现类
 */
@Repository
public class TableUserServiceImpl implements TableUserService {

    @Autowired
    private SysUserExtendMapper userExtendMapper;

    @Override
    public Optional<SysUser> findByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return Optional.empty();
        }
        return userExtendMapper.selectOne(c -> c.where(sysUser.username, isEqualTo(username)));
    }

    @Override
    public boolean existsByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return false;
        }
        return userExtendMapper.count(c -> c.where(sysUser.username, isEqualTo(username))) > 0;
    }

    @Override
    public List<SysUser> findByCondition(String username, String mobile, Integer status) {
        // 如果所有条件都为空，则返回所有记录
        if (!StringUtils.hasText(username) && !StringUtils.hasText(mobile) && status == null) {
            return userExtendMapper.select(c -> c.orderBy(sysUser.id.descending()));
        }

        // 构建查询条件
        WhereApplier whereApplier = c -> {
            if (StringUtils.hasText(username)) {
                c.and(sysUser.username, isLike(transFuzzyQueryParam(username)));
            }
            if (StringUtils.hasText(mobile)) {
                c.and(sysUser.mobile, isLike(transFuzzyQueryParam(mobile)));
            }
            if (status != null) {
                c.and(sysUser.status, isEqualTo(status));
            }
        };

        return userExtendMapper.select(c -> c.where(whereApplier).orderBy(sysUser.id.descending()));
    }
}
```

### 3.4 Mybatis Dynamic SQL规范

#### 3.4.1 查询标准模式

在使用 MyBatis Dynamic SQL 进行查询操作时，应遵循以下标准模式，特别是对于包含可选条件和模糊查询的场景：

```java
@Override
public List<SysUser> findMerchantAdmins(String merchantName, String adminName, String mobile, Integer status) {
    // 构建查询条件
    List<SysUser> users = userExtendMapper.select(c -> {
        // 设置必要的基础条件
        c.where(sysUser.accountType, isEqualTo((byte) 1)); // 账号类型为1表示商户管理员
        
        // 使用isLikeWhenPresent处理可选的模糊查询条件
        // 当参数不为空时才会添加此条件，避免了大量的if判断
        c.and(sysUser.nickname, isLikeWhenPresent(transFuzzyQueryParam(adminName)));
        c.and(sysUser.mobile, isLikeWhenPresent(transFuzzyQueryParam(mobile)));
        
        // 使用isEqualToWhenPresent处理可选的精确匹配条件
        // 当参数不为null时才会添加此条件
        c.and(sysUser.status, isEqualToWhenPresent(status));
        
        // 设置排序
        c.orderBy(sysUser.id.descending());
        return c;
    });
    
    // 如果需要进一步处理结果，可以在这里进行
    if (StringUtils.hasText(merchantName)) {
        return users.stream()
            .filter(user -> {
                if (user.getTenantId() == null) {
                    return false;
                }
                
                // 处理关联查询
                SysTenant tenant = tableTenantService.selectById(user.getTenantId());
                if (tenant == null || tenant.getTenantName() == null) {
                    return false;
                }
                return tenant.getTenantName().contains(merchantName);
            })
            .collect(Collectors.toList());
    }
    
    return users;
}

/**
 * 转换模糊查询参数，添加前后百分号
 * 
 * @param param 原始参数
 * @return 转换后的参数，如果原始参数为空则返回null
 */
private String transFuzzyQueryParam(String param) {
    return StringUtils.hasText(param) ? "%" + param + "%" : null;
}
```

#### 3.4.2 最佳实践说明

1. **使用 `isLikeWhenPresent` 和 `isEqualToWhenPresent`**：
   - 这些方法会自动处理参数为null或空的情况，只有当参数有值时才会添加查询条件
   - 避免了大量的if判断，使代码更加简洁清晰
   - 减少了出错的可能性

2. **使用 `transFuzzyQueryParam` 方法处理模糊查询参数**：
   - 封装了添加前后百分号的逻辑，使代码更加统一
   - 当参数为空时返回null，配合 `isLikeWhenPresent` 使用，避免添加无效条件

3. **分离SQL查询和内存处理**：
   - 对于无法通过SQL直接查询的条件（如关联查询），可以先获取基础数据，再在内存中进行过滤
   - 这种方式在数据量不大的情况下效率更高，避免了复杂的多表连接查询

4. **使用方法链式调用**：
   - 使用 `c.where().and().and()` 的链式调用方式，使代码结构清晰
   - 便于阅读和维护

5. **明确的排序条件**：
   - 始终添加明确的排序条件，避免结果顺序不确定

#### 3.4.3 常用查询示例

```java
// 单条件查询
return userMapper.selectOne(c -> c.where(sysUser.username, isEqualTo(username)));

// 多条件查询
return userMapper.select(c -> c
    .where(sysUser.status, isEqualTo(status))
    .and(sysUser.username, isLike(transFuzzyQueryParam(username)))
    .orderBy(sysUser.id.descending())
);

// 使用WhereApplier构建动态条件
WhereApplier whereApplier = c -> {
    if (StringUtils.hasText(username)) {
        c.and(sysUser.username, isLike(transFuzzyQueryParam(username)));
    }
    if (status != null) {
        c.and(sysUser.status, isEqualTo(status));
    }
};

return userMapper.select(c -> c.where(whereApplier).orderBy(sysUser.id.descending()));

// 计数查询
return userMapper.count(c -> c.where(sysUser.username, isEqualTo(username))) > 0;

// 批量插入
BatchInsert<SysUser> batchInsert = insert(userList)
    .into(sysUser)
    .map(sysUser.username).toProperty("username")
    .map(sysUser.mobile).toProperty("mobile")
    .map(sysUser.status).toProperty("status")
    .build()
    .render(RenderingStrategies.MYBATIS3);
```

## 4. 数据库规范

### 4.1 基础字段
```sql
`id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`create_by` varchar(64) NOT NULL COMMENT '创建人',
`update_by` varchar(64) NOT NULL COMMENT '更新人',
`is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除'
```

### 4.2 SQL规范
```sql
-- 分页查询（必须带软删除条件）
SELECT * FROM table
WHERE is_deleted = 0
  AND condition
ORDER BY id DESC
LIMIT #{offset}, #{size}

-- 更新（必须带软删除条件）
UPDATE table
SET status = #{status},
    update_time = NOW(),
    update_by = #{operator}
WHERE id = #{id}
  AND is_deleted = 0
```

## 5. 异常处理规范

### 5.1 全局异常处理
```java
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 业务异常处理
     */
    @ExceptionHandler(BusinessException.class)
    public Result<?> handleBusinessException(BusinessException e) {
        log.error("业务异常: code={}, message={}", e.getCode(), e.getMessage());
        return Result.error(e.getCode(), e.getMessage());
    }

    /**
     * 参数校验异常处理
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        StringBuilder sb = new StringBuilder();
        for (FieldError fieldError : bindingResult.getFieldErrors()) {
            sb.append(fieldError.getField()).append(": ").append(fieldError.getDefaultMessage()).append(", ");
        }
        String msg = sb.toString();
        if (msg.length() > 2) {
            msg = msg.substring(0, msg.length() - 2);
        }
        log.error("参数校验异常: {}", msg);
        return Result.error(ErrorCode.PARAM_VALID_ERROR.getCode(), msg);
    }

    /**
     * 未知异常处理
     */
    @ExceptionHandler(Exception.class)
    public Result<?> handleException(Exception e) {
        log.error("系统异常", e);
        return Result.error(ErrorCode.INTERNAL_SERVER_ERROR.getCode(), "系统异常，请联系管理员");
    }
}
```

### 5.2 业务异常定义
```java
/**
 * 业务异常基类
 */
@Getter
public class BusinessException extends RuntimeException {

    /**
     * 错误码
     */
    private final int code;

    /**
     * 构造函数
     *
     * @param errorCode 错误码枚举
     */
    public BusinessException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.code = errorCode.getCode();
    }

    /**
     * 构造函数
     *
     * @param message 错误信息，使用默认错误码 INTERNAL_SERVER_ERROR
     */
    public BusinessException(String message) {
        super(message);
        this.code = ErrorCode.INTERNAL_SERVER_ERROR.getCode();
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误码枚举
     * @param message 自定义错误信息
     */
    public BusinessException(ErrorCode errorCode, String message) {
        super(message);
        this.code = errorCode.getCode();
    }
}
```

### 5.3 错误码定义
```java
/**
 * 系统错误码枚举
 *
 * 错误码规则：
 * 1. HTTP状态码作为基础
 * 2. 业务错误码格式：模块代码(2位) + 错误类型(2位) + 序号(2位)
 *    例如：100101 表示用户模块(10)的参数错误(01)的第一个错误(01)
 */
public enum ErrorCode {

    /**
     * 通用错误码
     */
    SUCCESS(HttpServletResponse.SC_OK, "操作成功"),
    PARAM_MISSING(HttpServletResponse.SC_BAD_REQUEST, "参数缺失"),
    PARAM_TYPE_ERROR(HttpServletResponse.SC_BAD_REQUEST, "参数类型错误"),
    PARAM_VALID_ERROR(HttpServletResponse.SC_BAD_REQUEST, "参数校验错误"),
    UNAUTHORIZED(HttpServletResponse.SC_UNAUTHORIZED, "未授权"),
    FORBIDDEN(HttpServletResponse.SC_FORBIDDEN, "禁止访问"),
    NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "资源不存在"),
    INTERNAL_SERVER_ERROR(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "服务器内部错误"),

    /**
     * 用户模块错误码 (22)
     */
    USER_NOT_FOUND(220001, "用户不存在"),
    USER_ALREADY_EXISTS(220002, "用户已存在"),
    USERNAME_OR_PASSWORD_ERROR(220003, "用户名或密码错误"),

    /**
     * 租户模块错误码 (24)
     */
    TENANT_NOT_FOUND(240001, "租户不存在"),
    TENANT_CODE_EXISTS(240002, "租户编码已存在");

    /**
     * 错误码
     */
    private final int code;

    /**
     * 错误信息
     */
    private final String message;

    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
```

## 6. API设计规范

### 6.1 接口命名规范
- 所有接口统一使用 POST 方法，包括查询操作
- 创建操作使用 `/create` 后缀
- 更新操作使用 `/update` 后缀
- 详情查询使用 `/detail/{id}` 格式
- 列表查询使用 `/list` 后缀
- 删除操作使用 `/delete/{id}` 或 `/delete` 后缀

### 6.2 接口参数规范
- 创建操作使用 `{Entity}DTO` 作为请求体
- 更新操作使用 `{Entity}UpdateDTO` 作为请求体
- 查询操作使用 `{Entity}QueryDTO` 作为请求体
- 分页查询参数继承 `BasePageDTO`
- 状态字段默认值为启用(1)，不需要作为必填参数
- 所有请求参数使用 `@Validated` 进行校验
- 所有参数使用 `@ApiParam` 注解进行说明

### 6.3 接口返回规范
- 所有接口返回 `Result<T>` 类型
- 创建操作返回 `Result<Long>` 类型，包含新创建记录的ID
- 更新操作返回 `Result<Void>` 类型
- 详情查询返回 `Result<{Entity}VO>` 类型
- 分页查询返回 `Result<BasePageVO<{Entity}VO>>` 类型
- 列表查询返回 `Result<List<{Entity}VO>>` 类型

### 6.4 接口文档规范
- 所有接口使用 `@Api` 和 `@ApiOperation` 注解进行说明
- 所有参数使用 `@ApiParam` 注解进行说明
- 所有DTO和VO使用 `@ApiModel` 和 `@ApiModelProperty` 注解进行说明
- 接口说明包含功能描述、参数说明、返回值说明、错误码说明

## 7. 多租户设计规范

### 7.1 数据隔离
- 使用独立Schema的多租户架构
- 公共数据存储在 `auto_care_saas` 库
- 租户业务数据存储在各自的库中，如 `auto_care_tenant1`
- 租户ID通过请求头 `X-Tenant-Code` 传递

### 7.2 权限控制
- 基于RBAC模型实现权限控制
- 权限类型分为：菜单、按钮、数据、隐藏菜单
- 权限编码使用UUID格式
- 权限树结构使用父子关系表示

### 7.3 数据字典
- 数据字典值使用 `List<DataDictDTO<T>>` 类型，而非JSON字符串
- 数据字典查询接口返回 `dataName`、`dataCode` 和 `codeType` 字段

## 8. 测试规范

### 8.1 单元测试
- 使用JUnit 5编写单元测试
- 使用Mockito模拟依赖
- 测试类命名为 `{Class}Test`
- 测试方法命名为 `test{Method}_{Scenario}_{ExpectedResult}`
- 每个测试方法只测试一个场景

### 8.2 集成测试
- 使用Spring Boot Test编写集成测试
- 测试类命名为 `{Class}IT`
- 使用 `@SpringBootTest` 注解
- 使用内存数据库H2进行测试
- 使用 `@Transactional` 注解回滚测试数据