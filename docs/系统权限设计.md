# 优化的权限控制系统设计

## 一、系统概述
### 1.1 设计目标
- 构建多租户隔离的权限控制系统
- 实现基于RBAC模型的权限管理
- 支持功能权限与数据权限的精细化控制
- 确保系统安全性和性能

### 1.2 核心角色
- **超级管理员**：平台提供商的系统管理员，拥有最高权限
- **公司管理员**：由超级管理员创建，管理公司内部用户和权限
- **普通用户**：由公司管理员创建，根据被分配的角色使用系统功能

### 1.3 多租户架构
- 完全隔离的租户数据
- 企业级多层级组织架构支持
- 跨租户数据共享控制机制

## 二、权限模型设计
### 2.1 权限资源分类
#### 2.1.1 功能权限
- **页面访问权限**：控制用户可访问的系统页面和模块
- **功能操作权限**：控制用户在页面上可执行的操作（如查看、新增、编辑、删除等）
- **接口访问权限**：控制用户可调用的后端API接口

#### 2.1.2 数据权限
- **全部数据**：可查看和操作系统内所有数据
- **部门数据**：仅可查看和操作本部门及下级部门的数据
- **个人数据**：仅可查看和操作与自己相关的数据

### 2.2 RBAC模型实现
#### 2.2.1 基本要素
- **用户(User)**：系统的使用者，与具体角色关联
- **角色(Role)**：权限的集合，可分配给多个用户
- **权限(Permission)**：对系统资源的操作权限
- **资源(Resource)**：系统中可被访问的对象（如页面、按钮、API等）

#### 2.2.2 角色权限管理
- **角色独立原则**：每个角色拥有独立的权限集合
- **权限叠加机制**：用户拥有的所有角色权限将合并生效
- **用户多角色**：单个用户可同时拥有多个角色

#### 2.2.3 数据权限控制
- **多级机构**：支持复杂的组织架构层级管理
- **向下访问**：上级可访问下级的数据
- **权限过滤**：查询结果自动根据数据权限过滤

## 三、用户权限层级与规则
### 3.1 超级管理员权限
- 系统最高权限持有者
- 创建和管理租户及公司管理员
- 查看全系统操作日志
- 管理全局系统配置

### 3.2 公司管理员权限
- 创建和管理本公司普通用户
- 创建和管理本公司角色与权限
- 为普通用户分配角色
- 查看本公司操作日志

### 3.3 普通用户权限
- 使用被分配角色的权限
- 管理个人信息
- 查看个人操作记录

### 3.4 权限分配与控制规则
#### 3.4.1 权限范围控制
- **超级管理员**：全局权限，可管理所有租户
- **公司管理员**：租户内权限，只能管理本公司
- **普通用户**：个人权限，基于被分配的角色

#### 3.4.2 权限变更管理
- 角色权限变更记录审计
- 用户角色变更记录审计
- 重要权限变更审批流程
- 权限变更实时通知相关用户

#### 3.4.3 权限验证规则
- 实时权限校验机制
- 权限变更即时生效
- 多角色权限合并验证
- 支持权限继承关系

## 四、权限验证与安全机制
### 4.1 前端权限控制
- **菜单显示控制**：根据用户权限动态渲染菜单
- **按钮显示控制**：根据用户权限控制操作按钮的可见性
- **路由访问控制**：拦截非授权路由访问

### 4.2 API接口权限控制
- **基于token的身份验证**：JWT认证机制
- **基于角色的接口访问控制**：API级别的权限校验
- **统一权限拦截器**：集中处理权限验证逻辑

### 4.3 数据权限控制
- **查询结果自动过滤**：根据用户数据权限过滤查询结果
- **基于角色的数据范围控制**：不同角色可访问不同范围的数据

### 4.4 安全审计机制
- **权限使用审计**：记录关键权限使用情况
- **异常操作预警**：监控并预警异常权限使用行为
- **定期权限审计**：系统定期生成权限使用报告

## 五、权限缓存机制
### 5.1 缓存架构
- **Redis缓存存储**：使用Redis作为权限信息缓存
- **分布式缓存**：支持多节点部署的分布式缓存架构
- **高可用架构**：主从复制和哨兵模式确保缓存服务高可用

### 5.2 缓存内容
- **用户权限缓存**：缓存用户的完整权限信息
- **角色权限缓存**：缓存角色与权限的映射关系
- **API权限缓存**：缓存API与权限的对应关系

### 5.3 缓存更新策略
- **即时更新**：权限变更时立即更新缓存
- **定时更新**：定期全量刷新缓存数据
- **懒加载更新**：按需加载并更新缓存

## 六、核心业务流程
### 6.1 用户管理流程
#### 6.1.1 用户创建流程
- **手动创建流程**
  1. 填写用户基本信息（用户名、手机号等）
  2. 选择所属组织机构
  3. 分配系统角色
  4. 设置用户状态（启用/禁用）
  5. 系统校验并创建用户
  6. 记录操作日志

- **批量导入流程**
  1. 下载用户导入模板
  2. 按模板格式填写用户信息
  3. 上传Excel文件
  4. 系统校验数据有效性
  5. 执行批量创建用户操作
  6. 生成详细的导入结果报告

#### 6.1.2 用户管理功能
- **用户查询**：支持多条件组合查询（姓名、手机号、组织等）
- **用户编辑**：修改用户基本信息和组织归属
- **用户状态管理**：启用/禁用用户账号
- **手机号重置**：通过短信验证码修改关联手机号
- **角色分配**：为用户分配或移除系统角色

### 6.2 登录认证流程
#### 6.2.1 短信验证码登录流程
- **发送验证码**
  1. 用户输入手机号并请求验证码
  2. 系统校验手机号格式和发送频率限制
  3. 生成6位随机数字验证码
  4. 调用短信服务发送验证码
  5. 验证码存入系统并设置5分钟有效期

- **验证码登录**
  1. 用户输入手机号和收到的验证码
  2. 系统校验验证码正确性和有效期
  3. 验证通过后查询用户信息
  4. 生成JWT token并返回给客户端
  5. 标记验证码为已使用状态

#### 6.2.2 安全控制措施
- **验证码发送限制**
  - 同一手机号60秒内只能请求一次验证码
  - 同一手机号每天最多请求10次验证码
  - 同一IP地址每天最多请求20次验证码

- **验证码校验规则**
  - 验证码有效期为5分钟
  - 验证码错误累计3次后锁定手机号15分钟
  - 验证码使用后立即失效

## 七、数据库设计
### 7.1 核心数据表
- **租户表(sys_tenant)**：存储租户基本信息
- **用户表(sys_user)**：存储用户基本信息
- **角色表(sys_role)**：存储角色定义
- **权限表(sys_permission)**：存储系统权限定义
- **用户角色关联表(sys_user_role)**：存储用户与角色的关联关系
- **角色权限关联表(sys_role_permission)**：存储角色与权限的关联关系
- **验证码表(sys_verification_code)**：存储短信验证码信息
- **短信发送记录表(sms_send_record)**：记录短信发送历史

### 7.2 表结构设计
#### 7.2.1 租户表(sys_tenant)
```sql
CREATE TABLE IF NOT EXISTS `sys_tenant` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_name` VARCHAR(100) NOT NULL COMMENT '租户名称',
    `tenant_code` VARCHAR(50) NOT NULL COMMENT '租户编码',
    `contact_name` VARCHAR(50) DEFAULT NULL COMMENT '联系人姓名',
    `contact_phone` VARCHAR(20) DEFAULT NULL COMMENT '联系人电话',
    `contact_email` VARCHAR(100) DEFAULT NULL COMMENT '联系人邮箱',
    `expire_time` DATETIME DEFAULT NULL COMMENT '租约过期时间',
    `max_user_count` INT DEFAULT 0 COMMENT '最大用户数量',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `created_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_tenant_code` (`tenant_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户表';
```

#### 7.2.2 用户表(sys_user)
```sql
CREATE TABLE IF NOT EXISTS `sys_user` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `mobile` VARCHAR(11) NOT NULL COMMENT '手机号',
    `nickname` VARCHAR(50) DEFAULT NULL COMMENT '昵称',
    `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像',
    `email` VARCHAR(50) DEFAULT NULL COMMENT '邮箱',
    `status` TINYINT NOT NULL DEFAULT '1' COMMENT '状态:0-禁用,1-启用',
    `org_id` BIGINT DEFAULT NULL COMMENT '组织ID',
    `created_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username_tenant` (`username`, `tenant_id`),
    UNIQUE KEY `uk_mobile_tenant` (`mobile`, `tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统用户表';
```

#### 7.2.3 角色表(sys_role)
```sql
CREATE TABLE IF NOT EXISTS `sys_role` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `role_name` VARCHAR(50) NOT NULL COMMENT '角色名称',
    `role_code` VARCHAR(50) NOT NULL COMMENT '角色编码',
    `role_type` TINYINT NOT NULL DEFAULT 3 COMMENT '角色类型：1-超级管理员，2-普通管理员，3-普通用户',
    `description` VARCHAR(200) DEFAULT NULL COMMENT '角色描述',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
    `created_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_code_tenant` (`role_code`, `tenant_id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统角色表';
```

#### 7.2.4 权限表(sys_permission)
```sql
CREATE TABLE IF NOT EXISTS `sys_permission` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `permission_name` VARCHAR(50) NOT NULL COMMENT '权限名称',
    `permission_code` VARCHAR(50) NOT NULL COMMENT '权限编码',
    `permission_type` VARCHAR(20) NOT NULL COMMENT '权限类型：menu-菜单，button-按钮，api-接口',
    `parent_id` BIGINT DEFAULT NULL COMMENT '父级权限ID',
    `path` VARCHAR(200) DEFAULT NULL COMMENT '路由地址',
    `component` VARCHAR(200) DEFAULT NULL COMMENT '组件路径',
    `icon` VARCHAR(50) DEFAULT NULL COMMENT '图标',
    `sort` INT DEFAULT 0 COMMENT '排序',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `created_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '更新人',
    `updated_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_permission_code` (`permission_code`),
    KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统权限表';
```

#### 7.2.5 用户角色关联表(sys_user_role)
```sql
CREATE TABLE IF NOT EXISTS `sys_user_role` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `role_id` BIGINT NOT NULL COMMENT '角色ID',
    `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
    `created_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';
```

#### 7.2.6 角色权限关联表(sys_role_permission)
```sql
CREATE TABLE IF NOT EXISTS `sys_role_permission` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `role_id` BIGINT NOT NULL COMMENT '角色ID',
    `permission_id` BIGINT NOT NULL COMMENT '权限ID',
    `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
    `created_by` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '创建人',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
    KEY `idx_permission_id` (`permission_id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';
```

#### 7.2.7 验证码表(sys_verification_code)
```sql
CREATE TABLE `sys_verification_code` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `mobile` VARCHAR(11) NOT NULL COMMENT '手机号',
    `code` VARCHAR(6) NOT NULL COMMENT '验证码',
    `type` VARCHAR(20) NOT NULL COMMENT '验证码类型:LOGIN-登录,REGISTER-注册',
    `status` TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0-未使用，1-已使用',
    `expire_time` DATETIME NOT NULL COMMENT '过期时间',
    `created_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_mobile_type` (`mobile`, `type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='验证码表';
```

#### 7.2.8 短信发送记录表(sms_send_record)
```sql
CREATE TABLE IF NOT EXISTS `sms_send_record` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `phone_number` VARCHAR(20) NOT NULL COMMENT '手机号',
    `ip_address` VARCHAR(50) NOT NULL COMMENT 'IP地址',
    `type` VARCHAR(20) NOT NULL COMMENT '验证码类型：LOGIN-登录',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_phone_type_time` (`phone_number`, `type`, `create_time`),
    KEY `idx_ip_type_time` (`ip_address`, `type`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信发送记录表';
```