spring:
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;MODE=MySQL;DATABASE_TO_LOWER=TRUE;CASE_INSENSITIVE_IDENTIFIERS=TRUE;DB_CLOSE_DELAY=-1
    username: sa
    password: 
    schema: classpath:db/schema-h2.sql
    data: classpath:db/data-h2.sql
    initialization-mode: always
    sql-script-encoding: UTF-8
  sql:
    init:
      mode: always
      schema-locations: classpath:db/schema-h2.sql
      data-locations: classpath:db/data-h2.sql
      encoding: UTF-8
  h2:
    console:
      enabled: true
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: none
    show-sql: true
  redis:
    database: 0
    host: localhost
    port: 6379
    password: 
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms

# 多租户配置
tenant:
  default-schema: auto_care_saas
  schema-map:
    0: auto_care_extracme
    1: auto_care_dzjt

# 工作流配置
workflow:
  template:
    default-active: 1

# 测试配置
test:
  tenant-id: 1
  username: testuser

logging:
  level:
    com.extracme.saas.autocare: debug
    org.springframework: info
    org.hibernate: info
