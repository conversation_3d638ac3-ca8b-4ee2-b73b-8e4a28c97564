# Auto Care SaaS 冒烟测试用例

## 业务场景描述

### 场景一：维修顾问接单流程
维修顾问通过系统处理新到店的车辆维修需求。完整流程如下：

1. 维修顾问登录系统
   - 获取短信验证码
   - 使用验证码登录
   - 获取个人权限和菜单信息
2. 创建维修工单
   - 录入车辆信息
   - 记录维修需求
   - 分配维修技师
3. 执行维修流程
   - 车辆交接
   - 维修预审
   - 维修报价
   - 客户确认
   - 开始维修
   - 完工检验
   - 交付结算

### 场景二：管理员权限管理流程
系统管理员需要为新入职的维修顾问配置相应的系统权限：

1. 管理员登录系统
2. 创建新用户
3. 分配角色权限
4. 验证权限生效

## 1. 用户认证流程测试
### 1.1 短信验证码登录流程
#### 测试用例 1.1.1：获取短信验证码
```
接口：POST /api/auth/sms/code
请求参数：
{
    "mobile": "13800138000"
}

预期结果：
1. 返回状态码：200
2. 返回数据：
{
    "code": 0,
    "message": "验证码发送成功",
    "data": null
}
3. 验证码记录已保存到sys_verification_code表
```

#### 测试用例 1.1.1a：验证码发送频率限制（同一手机号）
```
测试步骤：
1. 发送第一次验证码请求
2. 立即发送第二次验证码请求（间隔小于60秒）

接口：POST /api/auth/sms/code
请求参数：
{
    "mobile": "13800138000"
}

预期结果：
1. 返回状态码：429
2. 返回数据：
{
    "code": 1001,
    "message": "请求过于频繁，请60秒后重试",
    "data": null
}
```

#### 测试用例 1.1.1b：验证码发送次数限制（同一手机号）
```
测试步骤：
1. 在24小时内发送11次验证码请求

接口：POST /api/auth/sms/code
请求参数：
{
    "mobile": "13800138000"
}

预期结果：
1. 返回状态码：429
2. 返回数据：
{
    "code": 1002,
    "message": "当日验证码获取次数已达上限",
    "data": null
}
```

#### 测试用例 1.1.1c：验证码发送IP限制
```
测试步骤：
1. 使用同一IP地址在24小时内发送21次验证码请求（使用不同手机号）

接口：POST /api/auth/sms/code
请求参数：
{
    "mobile": "13800138xxx"
}

预期结果：
1. 返回状态码：429
2. 返回数据：
{
    "code": 1003,
    "message": "当前IP请求次数已达上限",
    "data": null
}
```

#### 测试用例 1.1.2：使用验证码登录
```
接口：POST /api/auth/login
请求参数：
{
    "mobile": "13800138000",
    "code": "123456",
    "loginType": "SMS"
}

预期结果：
1. 返回状态码：200
2. 返回数据：
{
    "code": 0,
    "message": "登录成功",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiJ9...",
        "userInfo": {
            "userId": "12345",
            "username": "张三",
            "mobile": "13800138000",
            "roles": ["ROLE_REPAIR_ADVISOR"]
        }
    }
}
```

#### 测试用例 1.1.2a：验证码过期测试
```
测试步骤：
1. 获取验证码
2. 等待6分钟
3. 使用过期验证码登录

接口：POST /api/auth/login
请求参数：
{
    "mobile": "13800138000",
    "code": "123456",
    "loginType": "SMS"
}

预期结果：
1. 返回状态码：400
2. 返回数据：
{
    "code": 1004,
    "message": "验证码已过期",
    "data": null
}
```

#### 测试用例 1.1.2b：验证码错误次数限制
```
测试步骤：
1. 获取验证码
2. 连续3次输入错误验证码

接口：POST /api/auth/login
请求参数：
{
    "mobile": "13800138000",
    "code": "000000",
    "loginType": "SMS"
}

预期结果：
1. 第三次错误后返回状态码：403
2. 返回数据：
{
    "code": 1005,
    "message": "验证码错误次数过多，请15分钟后重试",
    "data": null
}
```

#### 测试用例 1.1.2c：验证码重复使用测试
```
测试步骤：
1. 使用正确验证码完成首次登录
2. 使用相同验证码尝试第二次登录

接口：POST /api/auth/login
请求参数：
{
    "mobile": "13800138000",
    "code": "123456",
    "loginType": "SMS"
}

预期结果：
1. 返回状态码：400
2. 返回数据：
{
    "code": 1006,
    "message": "验证码已失效",
    "data": null
}
```

#### 测试用例 1.1.3：获取用户权限菜单
```
接口：GET /api/auth/user/permissions
请求头：
Authorization: Bearer {token}

预期结果：
1. 返回状态码：200
2. 返回数据包含：
   - 可访问的菜单列表
   - 功能权限列表
   - 数据权限范围
```

## 2. 维修工单流程测试
### 2.1 创建维修工单
#### 测试用例 2.1.1：创建新工单
```
接口：POST /api/repair/order
请求参数：
{
    "vehicleInfo": {
        "plateNumber": "京A12345",
        "vin": "LVSHCAMB2CE054249",
        "brand": "丰田",
        "model": "凯美瑞",
        "mileage": 15000
    },
    "customerInfo": {
        "name": "李四",
        "mobile": "13900139000"
    },
    "repairType": "REGULAR",
    "description": "定期保养"
}

预期结果：
1. 返回状态码：200
2. 创建工单成功，返回工单号
3. 工作流实例被创建
4. 当前节点状态为"车辆交接"
```

### 2.2 维修流程节点测试
#### 测试用例 2.2.1：车辆交接确认
```
接口：POST /api/repair/order/{orderId}/handover
请求参数：
{
    "checkItems": [
        {
            "itemCode": "EXTERIOR",
            "status": "NORMAL",
            "remarks": "车身无明显划痕"
        }
    ],
    "handoverPhotos": ["photo1.jpg", "photo2.jpg"]
}

预期结果：
1. 返回状态码：200
2. 交接记录保存成功
3. 工作流节点推进到"维修预审"
```

#### 测试用例 2.2.2：维修预审
```
接口：POST /api/repair/order/{orderId}/pre-check
请求参数：
{
    "checkResult": {
        "engineStatus": "NORMAL",
        "brakeStatus": "NEED_REPAIR",
        "diagnosis": "刹车片磨损需更换"
    },
    "estimatedCost": 800.00
}

预期结果：
1. 返回状态码：200
2. 预审结果保存成功
3. 工作流节点推进到"维修报价"
```

## 3. 权限管理测试
### 3.1 用户管理
#### 测试用例 3.1.1：创建新用户
```
接口：POST /api/system/user
请求参数：
{
    "username": "wangwu",
    "mobile": "13700137000",
    "nickname": "王五",
    "orgId": 1001,
    "roles": ["ROLE_REPAIR_ADVISOR"]
}

预期结果：
1. 返回状态码：200
2. 用户创建成功
3. 角色关联成功
```

#### 测试用例 3.1.2：分配用户角色
```
接口：POST /api/system/user/{userId}/roles
请求参数：
{
    "roleIds": [1001, 1002]
}

预期结果：
1. 返回状态码：200
2. 角色分配成功
3. 权限立即生效
```

### 3.2 权限验证
#### 测试用例 3.2.1：越权访问测试
```
前置条件：
- 使用普通维修顾问账号登录

测试步骤：
1. 访问管理员专属接口：POST /api/system/role
2. 请求参数：
{
    "roleName": "测试角色",
    "roleCode": "TEST_ROLE"
}

预期结果：
1. 返回状态码：403
2. 返回错误信息：
{
    "code": 403,
    "message": "无权限执行此操作"
}
```

## 注意事项
1. 所有测试用例执行前需确保测试数据库已正确初始化
2. 测试用例执行顺序按照实际业务流程进行
3. 每个测试步骤都需要验证返回结果
4. 异常场景也需要覆盖测试
5. 测试完成后需要清理测试数据
6. 所有接口调用需要携带有效的认证信息
7. 需要验证数据一致性，特别是涉及金额计算的场景
8. 对于异步操作，需要等待合适的时间再验证结果

## 测试环境要求
1. 测试数据库：MySQL 8.0
2. Redis服务：用于存储验证码和token
3. RabbitMQ服务：用于消息通知
4. 测试环境配置文件：application-test.yml

## 执行方式
1. 使用Maven执行：`mvn test`
2. 使用IDE直接运行测试类
3. 使用Jenkins执行自动化测试

## 测试报告
测试执行完成后，可在以下位置查看测试报告：
- JUnit报告：`target/surefire-reports/`
- 测试覆盖率报告：`target/site/jacoco/` 