package com.extracme.saas.autocare.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.model.dto.ApprovalLevelsCreateDTO;
import com.extracme.saas.autocare.model.dto.ApprovalLevelsQueryDTO;
import com.extracme.saas.autocare.model.dto.ApprovalLevelsUpdateDTO;
import com.extracme.saas.autocare.model.entity.MtcApprovalLevels;
import com.extracme.saas.autocare.model.vo.ApprovalLevelsVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TableApprovalLevelsService;
import com.extracme.saas.autocare.service.impl.ApprovalLevelsServiceImpl;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 审批层级服务测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("审批层级服务测试")
class ApprovalLevelsServiceTest {

    @Mock
    private TableApprovalLevelsService tableApprovalLevelsService;

    @InjectMocks
    private ApprovalLevelsServiceImpl approvalLevelsService;

    private MtcApprovalLevels mockEntity;
    private ApprovalLevelsCreateDTO createDTO;
    private ApprovalLevelsUpdateDTO updateDTO;
    private ApprovalLevelsQueryDTO queryDTO;

    @BeforeEach
    void setUp() {
        // 创建模拟实体
        mockEntity = new MtcApprovalLevels();
        mockEntity.setId(1L);
        mockEntity.setApprovalLevel(1);
        mockEntity.setSelfApprovalAmount(new BigDecimal("10000.00"));

        // 创建DTO
        createDTO = new ApprovalLevelsCreateDTO();
        createDTO.setApprovalLevel(1);
        createDTO.setSelfApprovalAmount(new BigDecimal("10000.00"));

        updateDTO = new ApprovalLevelsUpdateDTO();
        updateDTO.setId(1L);
        updateDTO.setApprovalLevel(2);
        updateDTO.setSelfApprovalAmount(new BigDecimal("20000.00"));

        queryDTO = new ApprovalLevelsQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
    }

    @Test
    @DisplayName("创建审批层级 - 成功")
    void createApprovalLevels_Success() {
        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            // 设置模拟行为
            mockedSessionUtils.when(SessionUtils::getUsername).thenReturn("testUser");
            when(tableApprovalLevelsService.insert(any(MtcApprovalLevels.class), eq("testUser")))
                .thenReturn(mockEntity);

            // 执行测试
            assertDoesNotThrow(() -> approvalLevelsService.createApprovalLevels(createDTO));

            // 验证调用
            verify(tableApprovalLevelsService).insert(any(MtcApprovalLevels.class), eq("testUser"));
        }
    }

    @Test
    @DisplayName("更新审批层级 - 成功")
    void updateApprovalLevels_Success() {
        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            // 设置模拟行为
            mockedSessionUtils.when(SessionUtils::getUsername).thenReturn("testUser");
            when(tableApprovalLevelsService.selectById(1L)).thenReturn(mockEntity);
            when(tableApprovalLevelsService.updateSelectiveById(any(MtcApprovalLevels.class)))
                .thenReturn(1);

            // 执行测试
            assertDoesNotThrow(() -> approvalLevelsService.updateApprovalLevels(updateDTO));

            // 验证调用
            verify(tableApprovalLevelsService).selectById(1L);
            verify(tableApprovalLevelsService).updateSelectiveById(any(MtcApprovalLevels.class));
        }
    }

    @Test
    @DisplayName("删除审批层级 - 成功")
    void deleteApprovalLevels_Success() {
        // 设置模拟行为
        when(tableApprovalLevelsService.selectById(1L)).thenReturn(mockEntity);
        when(tableApprovalLevelsService.deleteById(1L)).thenReturn(1);

        // 执行测试
        assertDoesNotThrow(() -> approvalLevelsService.deleteApprovalLevels(1L));

        // 验证调用
        verify(tableApprovalLevelsService).selectById(1L);
        verify(tableApprovalLevelsService).deleteById(1L);
    }

    @Test
    @DisplayName("查询审批层级列表 - 成功")
    void getApprovalLevelsList_Success() {
        // 设置模拟行为
        when(tableApprovalLevelsService.findAll())
            .thenReturn(Arrays.asList(mockEntity));

        // 执行测试
        BasePageVO<ApprovalLevelsVO> result = approvalLevelsService.getApprovalLevelsList(queryDTO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(1, result.getList().size());

        ApprovalLevelsVO vo = result.getList().get(0);
        assertEquals(1L, vo.getId());
        assertEquals(Integer.valueOf(1), vo.getApprovalLevel());
        assertEquals("一级审批", vo.getApprovalLevelName());
        assertEquals(new BigDecimal("10000.00"), vo.getSelfApprovalAmount());

        // 验证调用
        verify(tableApprovalLevelsService).findAll();
    }

    @Test
    @DisplayName("获取审批层级下拉框选项 - 成功")
    void getApprovalLevelsCombo_Success() {
        // 创建模拟下拉框选项
        ComboVO<Long> comboVO = new ComboVO<>();
        comboVO.setId(1L);
        comboVO.setValue("一级审批 (≤10000.00元)");

        // 设置模拟行为
        when(tableApprovalLevelsService.findComboByTenant(100L))
            .thenReturn(Arrays.asList(comboVO));

        // 执行测试
        List<ComboVO<Long>> result = approvalLevelsService.getApprovalLevelsCombo(100L);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getId());
        assertEquals("一级审批 (≤10000.00元)", result.get(0).getValue());

        // 验证调用
        verify(tableApprovalLevelsService).findComboByTenant(100L);
    }

    @Test
    @DisplayName("获取审批层级下拉框选项 - 使用当前用户租户ID")
    void getApprovalLevelsCombo_UseCurrentTenantId() {
        try (MockedStatic<SessionUtils> mockedSessionUtils = mockStatic(SessionUtils.class)) {
            // 设置模拟行为
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(200L);
            when(tableApprovalLevelsService.findComboByTenant(200L))
                .thenReturn(Collections.emptyList());

            // 执行测试 - 传入null租户ID
            List<ComboVO<Long>> result = approvalLevelsService.getApprovalLevelsCombo(null);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());

            // 验证调用
            verify(tableApprovalLevelsService).findComboByTenant(200L);
        }
    }
}
