package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.extracme.saas.autocare.enums.ErrorCode;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.TokenDTO;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.service.SmsService;
import com.extracme.saas.autocare.util.JwtUtil;

/**
 * AuthServiceImpl 多租户登录测试
 */
@ExtendWith(MockitoExtension.class)
class AuthServiceImplMultiTenantTest {

    @Mock
    private SmsService smsService;

    @Mock
    private TableUserService userService;

    @Mock
    private TableTenantService tenantService;

    @Mock
    private JwtUtil jwtUtil;

    @InjectMocks
    private AuthServiceImpl authService;

    private String testMobile = "***********";
    private String testCode = "123456";

    @BeforeEach
    void setUp() {
        // JWT过期时间通过@Value注解设置，测试中不需要手动设置
    }

    @Test
    void testLoginByMobileWithTenant_SingleTenant_Success() {
        // 准备测试数据
        List<Long> tenantIds = Collections.singletonList(1L);
        SysUser user = createTestUser(1L, testMobile);

        // Mock 方法调用
        when(smsService.verifyCode(testMobile, testCode, "LOGIN")).thenReturn(true);
        when(userService.findTenantIdsByMobile(testMobile)).thenReturn(tenantIds);
        when(userService.findByMobileAndTenantId(testMobile, 1L)).thenReturn(Optional.of(user));
        when(jwtUtil.generateToken("1")).thenReturn("test-token");

        // 执行测试
        TokenDTO result = authService.loginByMobileWithTenant(testMobile, testCode, null);

        // 验证结果
        assertNotNull(result);
        assertEquals("test-token", result.getAccessToken());
        assertEquals(Long.valueOf(1), result.getUserId());
        assertEquals(testMobile, result.getMobile());

        // 验证方法调用
        verify(smsService).markCodeAsUsed(testMobile, testCode, "LOGIN");
    }

    @Test
    void testLoginByMobileWithTenant_MultipleTenants_ThrowsException() {
        // 准备测试数据
        List<Long> tenantIds = Arrays.asList(1L, 2L);

        // Mock 方法调用
        when(smsService.verifyCode(testMobile, testCode, "LOGIN")).thenReturn(true);
        when(userService.findTenantIdsByMobile(testMobile)).thenReturn(tenantIds);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            authService.loginByMobileWithTenant(testMobile, testCode, null);
        });

        assertEquals(ErrorCode.MULTIPLE_TENANT_ACCOUNTS.getCode(), exception.getCode());
    }

    @Test
    void testLoginByMobileWithTenant_SpecificTenant_Success() {
        // 准备测试数据
        List<Long> tenantIds = Arrays.asList(1L, 2L);
        SysUser user = createTestUser(2L, testMobile);

        // Mock 方法调用
        when(smsService.verifyCode(testMobile, testCode, "LOGIN")).thenReturn(true);
        when(userService.findTenantIdsByMobile(testMobile)).thenReturn(tenantIds);
        when(userService.findByMobileAndTenantId(testMobile, 2L)).thenReturn(Optional.of(user));
        when(jwtUtil.generateToken("1")).thenReturn("test-token");

        // 执行测试
        TokenDTO result = authService.loginByMobileWithTenant(testMobile, testCode, 2L);

        // 验证结果
        assertNotNull(result);
        assertEquals("test-token", result.getAccessToken());
        assertEquals(Long.valueOf(1), result.getUserId());
    }

    @Test
    void testLoginByMobileWithTenant_NoTenantAccounts_ThrowsException() {
        // Mock 方法调用
        when(smsService.verifyCode(testMobile, testCode, "LOGIN")).thenReturn(true);
        when(userService.findTenantIdsByMobile(testMobile)).thenReturn(Collections.emptyList());

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            authService.loginByMobileWithTenant(testMobile, testCode, null);
        });

        assertEquals(ErrorCode.NO_TENANT_ACCOUNTS.getCode(), exception.getCode());
    }

    @Test
    void testGetUserTenantAccounts_Success() {
        // 准备测试数据
        List<Long> tenantIds = Arrays.asList(1L, 2L);
        SysTenant tenant1 = createTestTenant(1L, "租户1");
        SysTenant tenant2 = createTestTenant(2L, "租户2");

        // Mock 方法调用
        when(userService.findTenantIdsByMobile(testMobile)).thenReturn(tenantIds);
        when(tenantService.selectById(1L)).thenReturn(tenant1);
        when(tenantService.selectById(2L)).thenReturn(tenant2);

        // 执行测试
        List<ComboVO<String>> result = authService.getUserTenantAccounts(testMobile);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("1", result.get(0).getId());
        assertEquals("租户1", result.get(0).getValue());
        assertEquals("2", result.get(1).getId());
        assertEquals("租户2", result.get(1).getValue());
    }

    private SysUser createTestUser(Long tenantId, String mobile) {
        SysUser user = new SysUser();
        user.setId(1L);
        user.setTenantId(tenantId);
        user.setMobile(mobile);
        user.setUsername("testuser");
        user.setStatus(1);
        return user;
    }

    private SysTenant createTestTenant(Long id, String name) {
        SysTenant tenant = new SysTenant();
        tenant.setId(id);
        tenant.setTenantName(name);
        tenant.setStatus(1);
        return tenant;
    }
}
