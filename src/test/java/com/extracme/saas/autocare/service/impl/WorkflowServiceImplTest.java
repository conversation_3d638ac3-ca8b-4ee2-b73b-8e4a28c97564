package com.extracme.saas.autocare.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.extracme.saas.autocare.enums.ActivityDefinitionEnum;
import com.extracme.saas.autocare.enums.ActivityStatusEnum;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowStartDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowTemplateQueryDTO;
import com.extracme.saas.autocare.model.entity.ActivityDefinition;
import com.extracme.saas.autocare.model.entity.ActivityTransition;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.model.entity.WorkflowTemplate;
import com.extracme.saas.autocare.model.vo.workflow.ActivityTransitionVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowTemplateVO;
import com.extracme.saas.autocare.repository.TableActivityDefinitionService;
import com.extracme.saas.autocare.repository.TableActivityInstanceService;
import com.extracme.saas.autocare.repository.TableActivityStatusTransitionService;
import com.extracme.saas.autocare.repository.TableActivityTransitionService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableWorkflowActivityService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.repository.TableWorkflowTemplateService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 工作流服务实现类测试
 */
public class WorkflowServiceImplTest {

    @InjectMocks
    private WorkflowServiceImpl workflowService;

    @Mock
    private TableWorkflowTemplateService tableWorkflowTemplateService;

    @Mock
    private TableWorkflowActivityService tableWorkflowActivityService;

    @Mock
    private TableActivityDefinitionService tableActivityDefinitionService;

    @Mock
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Mock
    private TableActivityInstanceService tableActivityInstanceService;

    @Mock
    private TableActivityTransitionService tableActivityTransitionService;

    @Mock
    private TableActivityStatusTransitionService tableActivityStatusTransitionService;

    @Mock
    private TableTenantService tableTenantService;

    private static final Integer TASK_TYPE = 1; // 事故维修
    private static final Integer REPAIR_FACTORY_TYPE = 1; // 合作修理厂
    private static final Integer SUB_PRODUCT_LINE = 1; // 子产品线1

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试根据条件自动匹配工作流模板并启动工作流
     */
    @Test
    @DisplayName("测试根据条件自动匹配工作流模板并启动工作流")
    public void testStartWorkflow_AutoMatchTemplate() {
        // 1. 准备测试数据
        WorkflowStartDTO startDTO = createWorkflowStartDTO();
        WorkflowTemplate template = createWorkflowTemplate();
        ActivityDefinition firstActivity = createActivityDefinition();
        createWorkflowInstance();

        // 2. 设置模拟行为
        // 模拟查询匹配的工作流模板
        List<WorkflowTemplate> templates = new ArrayList<>();
        templates.add(template);
        when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class))).thenReturn(templates);

        // 模拟查询活动节点
        List<ActivityDefinition> activityDefinitions = new ArrayList<>();
        activityDefinitions.add(firstActivity);
        when(tableActivityDefinitionService.selectByWorkflowId(anyLong())).thenReturn(activityDefinitions);

        // 模拟检查业务对象唯一性
        when(tableWorkflowInstanceService.selectByBusinessId(anyString())).thenReturn(null);

        // 模拟创建工作流实例
        when(tableWorkflowInstanceService.insert(any(WorkflowInstance.class), anyString())).thenAnswer(invocation -> {
            WorkflowInstance savedInstance = invocation.getArgument(0);
            savedInstance.setId(1L);
            return savedInstance;
        });

        // 模拟租户ID
        try (MockedStatic<SessionUtils> mockedSessionUtils = Mockito.mockStatic(SessionUtils.class)) {
            mockedSessionUtils.when(SessionUtils::getTenantId).thenReturn(1L);
            mockedSessionUtils.when(SessionUtils::getUsername).thenReturn("testuser");

            // 3. 执行测试
            Long instanceId = workflowService.startWorkflow(startDTO);

            // 4. 验证结果
            assertNotNull(instanceId);
            assertEquals(1L, instanceId);
        }
    }

    /**
     * 测试找不到匹配的工作流模板
     */
    @Test
    @DisplayName("测试找不到匹配的工作流模板")
    public void testStartWorkflow_NoMatchingTemplate() {
        // 1. 准备测试数据
        WorkflowStartDTO startDTO = createWorkflowStartDTO();

        // 2. 设置模拟行为 - 返回空列表表示没有匹配的模板
        when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class))).thenReturn(new ArrayList<>());

        // 3. 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            workflowService.startWorkflow(startDTO);
        });

        // 4. 验证异常消息
        assertTrue(exception.getMessage().contains("未找到匹配的工作流模板"));
    }

    /**
     * 测试找到多个匹配的工作流模板，应抛出业务异常
     */
    @Test
    @DisplayName("测试找到多个匹配的工作流模板，应抛出业务异常")
    public void testStartWorkflow_MultipleMatchingTemplates() {
        // 1. 准备测试数据
        WorkflowStartDTO startDTO = createWorkflowStartDTO();

        // 创建两个模板，一个较旧，一个较新
        WorkflowTemplate olderTemplate = createWorkflowTemplate();
        olderTemplate.setId(1L);
        olderTemplate.setCreateTime(new Date(System.currentTimeMillis() - 86400000)); // 1天前

        WorkflowTemplate newerTemplate = createWorkflowTemplate();
        newerTemplate.setId(2L);
        newerTemplate.setCreateTime(new Date()); // 现在

        // 2. 设置模拟行为
        // 模拟查询匹配的工作流模板 - 返回两个模板
        List<WorkflowTemplate> templates = Arrays.asList(olderTemplate, newerTemplate);
        when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class))).thenReturn(templates);

        // 3. 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            workflowService.startWorkflow(startDTO);
        });

        // 4. 验证异常消息
        assertTrue(exception.getMessage().contains("找到多个匹配的工作流模板"));
    }

    /**
     * 测试活动节点转换规则列表排序功能
     */
    @Test
    @DisplayName("测试活动节点转换规则列表按sequence排序")
    public void testListActivityTransitionsByWorkflowId_Sorting() {
        // 1. 准备测试数据
        Long workflowId = 1L;

        // 创建转换规则，故意打乱顺序
        List<ActivityTransition> transitions = Arrays.asList(
            createActivityTransition(1L, ActivityDefinitionEnum.REPAIR_QUOTATION.getCode(), ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode()), // sequence 4->5
            createActivityTransition(2L, ActivityDefinitionEnum.PENDING_ASSIGNMENT.getCode(), ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode()), // sequence 1->2
            createActivityTransition(3L, ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode(), ActivityDefinitionEnum.PRE_INSPECTION_REVIEW.getCode()), // sequence 2->3
            createActivityTransition(4L, ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode(), ActivityDefinitionEnum.IN_REPAIR.getCode()) // sequence 5->6
        );

        // 2. 设置模拟行为
        when(tableActivityTransitionService.selectByWorkflowId(workflowId)).thenReturn(transitions);
        when(tableActivityStatusTransitionService.selectByActivityTransitionIds(any())).thenReturn(Collections.emptyList());

        // 3. 执行测试
        List<ActivityTransitionVO> result = workflowService.listActivityTransitionsByWorkflowId(workflowId);

        // 4. 验证结果
        assertNotNull(result);
        assertEquals(4, result.size());

        // 验证排序是否正确：应该按照from_activity_code的sequence升序排列
        assertEquals(ActivityDefinitionEnum.PENDING_ASSIGNMENT.getCode(), result.get(0).getFromActivityCode()); // sequence 1
        assertEquals(ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode(), result.get(1).getFromActivityCode()); // sequence 2
        assertEquals(ActivityDefinitionEnum.REPAIR_QUOTATION.getCode(), result.get(2).getFromActivityCode()); // sequence 4
        assertEquals(ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode(), result.get(3).getFromActivityCode()); // sequence 5
    }

    /**
     * 测试工作流模板列表查询时正确设置tenantCode字段
     */
    @Test
    @DisplayName("测试工作流模板列表查询时正确设置tenantCode字段")
    public void testListTemplates_WithTenantCode() {
        // 1. 准备测试数据
        WorkflowTemplateQueryDTO queryDTO = new WorkflowTemplateQueryDTO();

        // 创建工作流模板
        WorkflowTemplate template1 = createWorkflowTemplate();
        template1.setId(1L);
        template1.setTenantId(1);

        WorkflowTemplate template2 = createWorkflowTemplate();
        template2.setId(2L);
        template2.setTenantId(2);

        List<WorkflowTemplate> templates = Arrays.asList(template1, template2);

        // 创建租户信息
        SysTenant tenant1 = createTenant(1L, "TENANT_001");
        SysTenant tenant2 = createTenant(2L, "TENANT_002");

        // 创建活动节点定义
        ActivityDefinition activityDefinition = createActivityDefinition();
        List<ActivityDefinition> activityDefinitions = Arrays.asList(activityDefinition);

        // 2. 设置模拟行为
        when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class))).thenReturn(templates);
        // 模拟一次性查询所有租户数据
        when(tableTenantService.findByCondition(null, null, null)).thenReturn(Arrays.asList(tenant1, tenant2));
        when(tableActivityDefinitionService.selectByWorkflowId(anyLong())).thenReturn(activityDefinitions);

        // 3. 执行测试
        List<WorkflowTemplateVO> result = workflowService.listTemplates(queryDTO);

        // 4. 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证第一个模板的tenantCode
        WorkflowTemplateVO vo1 = result.get(0);
        assertEquals("TENANT_001", vo1.getTenantCode());

        // 验证第二个模板的tenantCode
        WorkflowTemplateVO vo2 = result.get(1);
        assertEquals("TENANT_002", vo2.getTenantCode());
    }

    /**
     * 测试工作流模板列表查询时租户信息为空的情况
     */
    @Test
    @DisplayName("测试工作流模板列表查询时租户信息为空的情况")
    public void testListTemplates_WithNullTenant() {
        // 1. 准备测试数据
        WorkflowTemplateQueryDTO queryDTO = new WorkflowTemplateQueryDTO();

        // 创建工作流模板
        WorkflowTemplate template = createWorkflowTemplate();
        template.setId(1L);
        template.setTenantId(999); // 不存在的租户ID

        List<WorkflowTemplate> templates = Arrays.asList(template);

        // 创建活动节点定义
        ActivityDefinition activityDefinition = createActivityDefinition();
        List<ActivityDefinition> activityDefinitions = Arrays.asList(activityDefinition);

        // 2. 设置模拟行为
        when(tableWorkflowTemplateService.findByCondition(any(WorkflowTemplateQueryDTO.class))).thenReturn(templates);
        // 模拟查询所有租户数据，但不包含ID为999的租户（模拟租户不存在的情况）
        when(tableTenantService.findByCondition(null, null, null)).thenReturn(Collections.emptyList());
        when(tableActivityDefinitionService.selectByWorkflowId(anyLong())).thenReturn(activityDefinitions);

        // 3. 执行测试
        List<WorkflowTemplateVO> result = workflowService.listTemplates(queryDTO);

        // 4. 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());

        // 验证tenantCode为null
        WorkflowTemplateVO vo = result.get(0);
        assertEquals(null, vo.getTenantCode());
    }

    /**
     * 测试活动节点转换规则列表排序功能 - 包含相同from_activity_code的情况
     */
    @Test
    @DisplayName("测试相同from_activity_code时按to_activity_code的sequence排序")
    public void testListActivityTransitionsByWorkflowId_SortingWithSameFromActivity() {
        // 1. 准备测试数据
        Long workflowId = 1L;

        // 创建转换规则，包含相同的from_activity_code
        List<ActivityTransition> transitions = Arrays.asList(
            createActivityTransition(1L, ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode(), ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode()), // sequence 2->5
            createActivityTransition(2L, ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode(), ActivityDefinitionEnum.PRE_INSPECTION_REVIEW.getCode()), // sequence 2->3
            createActivityTransition(3L, ActivityDefinitionEnum.PENDING_ASSIGNMENT.getCode(), ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode()) // sequence 1->2
        );

        // 2. 设置模拟行为
        when(tableActivityTransitionService.selectByWorkflowId(workflowId)).thenReturn(transitions);
        when(tableActivityStatusTransitionService.selectByActivityTransitionIds(any())).thenReturn(Collections.emptyList());

        // 3. 执行测试
        List<ActivityTransitionVO> result = workflowService.listActivityTransitionsByWorkflowId(workflowId);

        // 4. 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());

        // 验证排序是否正确
        // 第一个应该是PENDING_ASSIGNMENT -> VEHICLE_TRANSFER (1->2)
        assertEquals(ActivityDefinitionEnum.PENDING_ASSIGNMENT.getCode(), result.get(0).getFromActivityCode());
        assertEquals(ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode(), result.get(0).getToActivityCode());

        // 接下来两个都是VEHICLE_TRANSFER开始，应该按to_activity_code的sequence排序
        // VEHICLE_TRANSFER -> PRE_INSPECTION_REVIEW (2->3) 应该在前面
        assertEquals(ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode(), result.get(1).getFromActivityCode());
        assertEquals(ActivityDefinitionEnum.PRE_INSPECTION_REVIEW.getCode(), result.get(1).getToActivityCode());

        // VEHICLE_TRANSFER -> DAMAGE_ASSESSMENT (2->5) 应该在后面
        assertEquals(ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode(), result.get(2).getFromActivityCode());
        assertEquals(ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode(), result.get(2).getToActivityCode());
    }

    // 辅助方法：创建活动转换规则
    private ActivityTransition createActivityTransition(Long id, String fromActivityCode, String toActivityCode) {
        ActivityTransition transition = new ActivityTransition();
        transition.setId(id);
        transition.setWorkflowId(1L);
        transition.setFromActivityCode(fromActivityCode);
        transition.setToActivityCode(toActivityCode);
        transition.setTriggerEvent("TEST_EVENT");
        transition.setHandlerClass("com.test.Handler");
        transition.setTenantId(1);
        transition.setDescription("测试转换规则");
        return transition;
    }

    // 辅助方法：创建工作流启动DTO
    private WorkflowStartDTO createWorkflowStartDTO() {
        WorkflowStartDTO startDTO = new WorkflowStartDTO();
        startDTO.setTaskType(TASK_TYPE);
        startDTO.setRepairFactoryType(REPAIR_FACTORY_TYPE);
        startDTO.setSubProductLine(SUB_PRODUCT_LINE);
        startDTO.setBusinessId("TEST_BUSINESS_001");
        startDTO.setOperator("testuser");
        startDTO.setRemarks("测试启动工作流");
        return startDTO;
    }

    // 辅助方法：创建工作流模板
    private WorkflowTemplate createWorkflowTemplate() {
        WorkflowTemplate template = new WorkflowTemplate();
        template.setId(1L);
        template.setWorkflowName("测试工作流模板");
        template.setTaskType(TASK_TYPE);
        template.setRepairFactoryType(REPAIR_FACTORY_TYPE);
        template.setSubProductLine(SUB_PRODUCT_LINE);
        template.setIsActive(1);
        template.setTenantId(1);
        template.setCreateTime(new Date());
        template.setCreateBy("testuser");
        template.setUpdateTime(new Date());
        template.setUpdateBy("testuser");
        template.setDescription("测试工作流模板描述");
        return template;
    }

    // 辅助方法：创建活动节点定义
    private ActivityDefinition createActivityDefinition() {
        ActivityDefinition activityDefinition = new ActivityDefinition();
        activityDefinition.setId(1L);
        activityDefinition.setActivityCode("TEST_ACTIVITY");
        activityDefinition.setActivityName("测试活动节点");
        activityDefinition.setDescription("测试活动节点描述");
        activityDefinition.setSequence(1);
        activityDefinition.setIsEnabled(1);
        return activityDefinition;
    }

    // 辅助方法：创建工作流实例
    private WorkflowInstance createWorkflowInstance() {
        WorkflowInstance instance = new WorkflowInstance();
        instance.setId(1L);
        instance.setWorkflowId(1L);
        instance.setBusinessId("TEST_BUSINESS_001");
        instance.setCurrentActivityCode(ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode());
        instance.setStatusCode(ActivityStatusEnum.UNPROCESSED.getCode());
        instance.setTenantId(1);
        return instance;
    }

    // 辅助方法：创建租户
    private SysTenant createTenant(Long id, String tenantCode) {
        SysTenant tenant = new SysTenant();
        tenant.setId(id);
        tenant.setTenantCode(tenantCode);
        tenant.setTenantName("测试租户" + id);
        tenant.setStatus(1);
        return tenant;
    }
}
