package com.extracme.saas.autocare.repository;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;

import com.extracme.saas.autocare.mapper.extend.SysUserExtendMapper;
import com.extracme.saas.autocare.mapper.extend.SysUserRoleExtendMapper;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.entity.SysUserRole;
import com.extracme.saas.autocare.repository.impl.TableUserServiceImpl;

/**
 * TableUserService findByCondition方法测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("TableUserService findByCondition方法测试")
class TableUserServiceFindByConditionTest {

    @Mock
    private SysUserExtendMapper userExtendMapper;

    @Mock
    private SysUserRoleExtendMapper userRoleMapper;

    @InjectMocks
    private TableUserServiceImpl tableUserService;

    private SysUser mockUser1;
    private SysUser mockUser2;
    private SysUserRole mockUserRole;

    @BeforeEach
    void setUp() {
        // 创建模拟用户1
        mockUser1 = new SysUser();
        mockUser1.setId(1L);
        mockUser1.setNickname("张三");
        mockUser1.setMobile("13800138000");

        // 创建模拟用户2
        mockUser2 = new SysUser();
        mockUser2.setId(2L);
        mockUser2.setNickname("李四");
        mockUser2.setMobile("13900139000");

        // 创建模拟用户角色关联
        mockUserRole = new SysUserRole();
        mockUserRole.setUserId(1L);
        mockUserRole.setRoleId(100L);
    }

    @Test
    @DisplayName("测试无角色ID时的基础查询 - 支持nickname和mobile模糊查询")
    void testFindByCondition_WithoutRoleId_SupportsFuzzySearch() {
        // 设置模拟行为
        when(userExtendMapper.selectMany(any(SelectStatementProvider.class)))
            .thenReturn(Arrays.asList(mockUser1));

        // 执行测试 - 只传入nickname
        List<SysUser> result1 = tableUserService.findByCondition("张", null, null, null);

        // 验证结果
        assertNotNull(result1);
        assertEquals(1, result1.size());
        assertEquals("张三", result1.get(0).getNickname());

        // 验证调用了selectMany方法
        verify(userExtendMapper, times(1)).selectMany(any(SelectStatementProvider.class));

        // 重置mock
        reset(userExtendMapper);

        // 设置模拟行为
        when(userExtendMapper.selectMany(any(SelectStatementProvider.class)))
            .thenReturn(Arrays.asList(mockUser2));

        // 执行测试 - 只传入mobile
        List<SysUser> result2 = tableUserService.findByCondition(null, "139", null, null);

        // 验证结果
        assertNotNull(result2);
        assertEquals(1, result2.size());
        assertEquals("13900139000", result2.get(0).getMobile());

        // 验证调用了selectMany方法
        verify(userExtendMapper, times(1)).selectMany(any(SelectStatementProvider.class));
    }

    @Test
    @DisplayName("测试无角色ID时的基础查询 - 同时使用nickname和mobile")
    void testFindByCondition_WithoutRoleId_BothNicknameAndMobile() {
        // 设置模拟行为
        when(userExtendMapper.selectMany(any(SelectStatementProvider.class)))
            .thenReturn(Arrays.asList(mockUser1));

        // 执行测试 - 同时传入nickname和mobile
        List<SysUser> result = tableUserService.findByCondition("张", "138", null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("张三", result.get(0).getNickname());
        assertEquals("13800138000", result.get(0).getMobile());

        // 验证调用了selectMany方法
        verify(userExtendMapper, times(1)).selectMany(any(SelectStatementProvider.class));
    }

    @Test
    @DisplayName("测试无角色ID时的基础查询 - 所有参数为空")
    void testFindByCondition_WithoutRoleId_AllParametersEmpty() {
        // 设置模拟行为
        when(userExtendMapper.selectMany(any(SelectStatementProvider.class)))
            .thenReturn(Arrays.asList(mockUser1, mockUser2));

        // 执行测试 - 所有参数为空
        List<SysUser> result = tableUserService.findByCondition(null, null, null, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证调用了selectMany方法
        verify(userExtendMapper, times(1)).selectMany(any(SelectStatementProvider.class));
    }

    @Test
    @DisplayName("测试有角色ID时的查询 - 支持nickname和mobile模糊查询")
    void testFindByCondition_WithRoleId_SupportsFuzzySearch() {
        // 设置模拟行为
        when(userRoleMapper.select(any())).thenReturn(Arrays.asList(mockUserRole));
        when(userExtendMapper.selectMany(any(SelectStatementProvider.class)))
            .thenReturn(Arrays.asList(mockUser1));

        // 执行测试 - 传入roleId和nickname
        List<SysUser> result = tableUserService.findByCondition("张", null, 100L, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("张三", result.get(0).getNickname());

        // 验证调用了相关方法
        verify(userRoleMapper, times(1)).select(any());
        verify(userExtendMapper, times(1)).selectMany(any(SelectStatementProvider.class));
    }

    @Test
    @DisplayName("测试有角色ID时的查询 - 角色下无用户")
    void testFindByCondition_WithRoleId_NoUsersInRole() {
        // 设置模拟行为 - 角色下无用户
        when(userRoleMapper.select(any())).thenReturn(Collections.emptyList());

        // 执行测试
        List<SysUser> result = tableUserService.findByCondition("张", "138", 100L, null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证调用了userRoleMapper但没有调用userExtendMapper
        verify(userRoleMapper, times(1)).select(any());
        verify(userExtendMapper, never()).selectMany(any(SelectStatementProvider.class));
    }

    @Test
    @DisplayName("测试有角色ID时的查询 - 同时使用nickname和mobile")
    void testFindByCondition_WithRoleId_BothNicknameAndMobile() {
        // 设置模拟行为
        when(userRoleMapper.select(any())).thenReturn(Arrays.asList(mockUserRole));
        when(userExtendMapper.selectMany(any(SelectStatementProvider.class)))
            .thenReturn(Arrays.asList(mockUser1));

        // 执行测试 - 传入roleId、nickname和mobile
        List<SysUser> result = tableUserService.findByCondition("张", "138", 100L, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("张三", result.get(0).getNickname());
        assertEquals("13800138000", result.get(0).getMobile());

        // 验证调用了相关方法
        verify(userRoleMapper, times(1)).select(any());
        verify(userExtendMapper, times(1)).selectMany(any(SelectStatementProvider.class));
    }

    @Test
    @DisplayName("测试空字符串参数处理")
    void testFindByCondition_EmptyStringParameters() {
        // 设置模拟行为
        when(userExtendMapper.selectMany(any(SelectStatementProvider.class)))
            .thenReturn(Arrays.asList(mockUser1, mockUser2));

        // 执行测试 - 传入空字符串
        List<SysUser> result = tableUserService.findByCondition("", "", null, null);

        // 验证结果 - 空字符串应该被当作null处理
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证调用了selectMany方法
        verify(userExtendMapper, times(1)).selectMany(any(SelectStatementProvider.class));
    }

    @Test
    @DisplayName("测试查询结果为空的情况")
    void testFindByCondition_EmptyResult() {
        // 设置模拟行为 - 返回空列表
        when(userExtendMapper.selectMany(any(SelectStatementProvider.class)))
            .thenReturn(Collections.emptyList());

        // 执行测试
        List<SysUser> result = tableUserService.findByCondition("不存在的用户", null, null, null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证调用了selectMany方法
        verify(userExtendMapper, times(1)).selectMany(any(SelectStatementProvider.class));
    }
}
