package com.extracme.saas.autocare.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import com.extracme.saas.autocare.model.dto.workflow.ActivityStatusTransitionDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionCreateDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionDeleteDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionQueryDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionUpdateDTO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.workflow.ActivityStatusTransitionVO;
import com.extracme.saas.autocare.model.vo.workflow.ActivityTransitionDetailVO;
import com.extracme.saas.autocare.model.vo.workflow.ActivityTransitionVO;
import com.extracme.saas.autocare.service.WorkflowService;
import com.fasterxml.jackson.databind.ObjectMapper;

@WebMvcTest(WorkflowController.class)
public class WorkflowControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private WorkflowService workflowService;

    @Autowired
    private ObjectMapper objectMapper;

    private ActivityTransitionCreateDTO createDTO;
    private ActivityTransitionUpdateDTO updateDTO;
    private ActivityTransitionDeleteDTO deleteDTO;
    private ActivityTransitionQueryDTO queryDTO;
    private ActivityTransitionDetailVO detailVO;
    private BasePageVO<ActivityTransitionVO> pageVO;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        ActivityTransitionDTO transitionDTO = new ActivityTransitionDTO();
        transitionDTO.setWorkflowId(1L);
        transitionDTO.setFromActivityCode("HANDOVER");
        transitionDTO.setToActivityCode("QUALITY_INSPECTION");
        transitionDTO.setTriggerEvent("COMPLETE_HANDOVER");
        transitionDTO.setHandlerClass("com.extracme.saas.autocare.workflow.handler.DefaultTransitionHandler");
        transitionDTO.setDescription("车辆交接完成转入进保预审");

        ActivityStatusTransitionDTO statusTransitionDTO = new ActivityStatusTransitionDTO();
        // 不再需要设置activityTransitionId，将由Service层自动设置
        statusTransitionDTO.setActivityCode("HANDOVER"); // 设置活动编码
        statusTransitionDTO.setFromStatusCode("1");
        statusTransitionDTO.setToStatusCode("2");
        statusTransitionDTO.setDescription("开始处理车辆交接");

        List<ActivityStatusTransitionDTO> statusTransitions = new ArrayList<>();
        statusTransitions.add(statusTransitionDTO);

        // 创建DTO
        createDTO = new ActivityTransitionCreateDTO();
        createDTO.setActivityTransition(transitionDTO);
        createDTO.setStatusTransitions(statusTransitions);

        // 更新DTO
        updateDTO = new ActivityTransitionUpdateDTO();
        updateDTO.setId(1L);
        updateDTO.setActivityTransition(transitionDTO);
        updateDTO.setStatusTransitions(statusTransitions);

        // 删除DTO
        deleteDTO = new ActivityTransitionDeleteDTO();
        deleteDTO.setId(1L);
        deleteDTO.setCascadeDelete(true);

        // 查询DTO
        queryDTO = new ActivityTransitionQueryDTO();
        queryDTO.setWorkflowId(1L);
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);

        // 详情VO
        ActivityTransitionVO transitionVO = new ActivityTransitionVO();
        transitionVO.setId(1L);
        transitionVO.setWorkflowId(1L);
        transitionVO.setFromActivityCode("HANDOVER");
        transitionVO.setToActivityCode("INSPECTION");
        transitionVO.setTriggerEvent("COMPLETE_HANDOVER");
        transitionVO.setHandlerClass("com.extracme.saas.autocare.workflow.handler.DefaultTransitionHandler");
        transitionVO.setTenantId(1);
        transitionVO.setDescription("车辆交接完成转入进保预审");

        ActivityStatusTransitionVO statusTransitionVO = new ActivityStatusTransitionVO();
        statusTransitionVO.setId(1L);
        statusTransitionVO.setActivityTransitionId(1L);
        statusTransitionVO.setActivityCode("HANDOVER"); // 设置活动编码
        statusTransitionVO.setFromStatusCode("1");
        statusTransitionVO.setToStatusCode("2");
        statusTransitionVO.setDescription("开始处理车辆交接");

        List<ActivityStatusTransitionVO> statusTransitionVOs = new ArrayList<>();
        statusTransitionVOs.add(statusTransitionVO);

        detailVO = new ActivityTransitionDetailVO();
        detailVO.setActivityTransition(transitionVO);

        // 分页VO
        List<ActivityTransitionVO> transitionVOs = new ArrayList<>();
        transitionVOs.add(transitionVO);

        pageVO = new BasePageVO<>();
        pageVO.setList(transitionVOs);
        pageVO.setTotal(1L);
        pageVO.setPageNum(1);
        pageVO.setPageSize(10);
        pageVO.setPages(1);
    }

    @Test
    void testCreateActivityTransition() throws Exception {
        when(workflowService.createActivityTransition(any(ActivityTransitionCreateDTO.class))).thenReturn(1L);

        mockMvc.perform(post("/api/v1/workflow/transition/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(1));
    }

    @Test
    void testUpdateActivityTransition() throws Exception {
        doNothing().when(workflowService).updateActivityTransition(any(ActivityTransitionUpdateDTO.class));

        mockMvc.perform(post("/api/v1/workflow/transition/update")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testDeleteActivityTransition() throws Exception {
        doNothing().when(workflowService).deleteActivityTransition(any(ActivityTransitionDeleteDTO.class));

        mockMvc.perform(post("/api/v1/workflow/transition/delete")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(deleteDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testGetActivityTransitionDetail() throws Exception {
        when(workflowService.getActivityTransitionDetail(1L)).thenReturn(detailVO);

        mockMvc.perform(get("/api/v1/workflow/transition/detail/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.activityTransition.id").value(1))
                .andExpect(jsonPath("$.data.activityTransition.statusTransitions[0].id").value(1));
    }

    @Test
    void testGetActivityTransitionList() throws Exception {
        List<ActivityTransitionVO> transitionVOs = new ArrayList<>();
        transitionVOs.add(pageVO.getList().get(0));

        when(workflowService.listActivityTransitionsByWorkflowId(1L)).thenReturn(transitionVOs);

        mockMvc.perform(get("/api/v1/workflow/transition/list/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data[0].id").value(1));
    }
}
