package com.extracme.saas.autocare.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.extracme.saas.autocare.model.dto.UserCreateDTO;
import com.extracme.saas.autocare.service.UserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Arrays;

/**
 * 用户管理控制器测试类
 */
public class UserControllerTest {

    private MockMvc mockMvc;

    @Mock
    private UserService userService;

    @InjectMocks
    private UserController userController;

    private ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(userController).build();
    }

    /**
     * 测试创建用户 - 成功场景
     */
    @Test
    public void testCreateUser_Success() throws Exception {
        // 准备测试数据
        UserCreateDTO createDTO = new UserCreateDTO();
        createDTO.setMobile("***********");
        createDTO.setNickname("Test User");
        createDTO.setOrgId("ORG001");
        createDTO.setStatus(1);
        createDTO.setApprovalLevel(2);
        createDTO.setRepairDepotId("DEPOT001");
        createDTO.setAccountType(0);
        createDTO.setRoleIds(Arrays.asList(1L));

        String requestBody = objectMapper.writeValueAsString(createDTO);

        // 模拟创建用户成功
        when(userService.createUser(any(UserCreateDTO.class))).thenReturn(1L);

        // 执行测试
        mockMvc.perform(post("/api/v1/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data").value(1));

        // 验证调用
        verify(userService, times(1)).createUser(any(UserCreateDTO.class));
    }

    /**
     * 测试创建用户 - 参数验证失败
     */
    @Test
    public void testCreateUser_ValidationFail() throws Exception {
        // 准备测试数据
        UserCreateDTO createDTO = new UserCreateDTO();
        // 不设置必填字段

        String requestBody = objectMapper.writeValueAsString(createDTO);

        // 执行测试
        mockMvc.perform(post("/api/v1/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isBadRequest());

        // 验证调用
        verify(userService, never()).createUser(any(UserCreateDTO.class));
    }
}