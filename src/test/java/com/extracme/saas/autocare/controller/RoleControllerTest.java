package com.extracme.saas.autocare.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.extracme.saas.autocare.model.dto.RoleDTO;
import com.extracme.saas.autocare.model.dto.RolePermissionDTO;
import com.extracme.saas.autocare.model.dto.RoleQueryDTO;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.model.vo.RoleVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.service.RoleService;
import com.fasterxml.jackson.databind.ObjectMapper;

@DisplayName("角色控制器测试")
class RoleControllerTest {

    private MockMvc mockMvc;

    @Mock
    private RoleService roleService;

    @InjectMocks
    private RoleController roleController;

    private ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(roleController).build();
    }

    @Test
    @DisplayName("为角色分配权限 - 成功场景")
    void assignPermissions_Success() throws Exception {
        // 准备测试数据
        RolePermissionDTO rolePermissionDTO = new RolePermissionDTO();
        rolePermissionDTO.setRoleId(1L);
        rolePermissionDTO.setPermissionIds(Arrays.asList(1L, 2L, 3L));

        String requestBody = objectMapper.writeValueAsString(rolePermissionDTO);

        // 执行测试
        mockMvc.perform(post("/api/v1/roles/assign/permissions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        // 验证调用
        verify(roleService).assignPermissions(eq(1L), eq(Arrays.asList(1L, 2L, 3L)));
    }

    @Test
    @DisplayName("获取角色的权限ID列表 - 成功场景")
    void getRolePermissions_Success() throws Exception {
        // 准备测试数据
        Long roleId = 1L;
        List<Long> permissionIds = Arrays.asList(1L, 2L, 3L);

        // 设置模拟行为
        when(roleService.findPermissionIdsByRoleId(roleId)).thenReturn(permissionIds);

        // 执行测试
        mockMvc.perform(get("/api/v1/roles/permissions/{id}", roleId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(3))
                .andExpect(jsonPath("$.data[0]").value(1))
                .andExpect(jsonPath("$.data[1]").value(2))
                .andExpect(jsonPath("$.data[2]").value(3));

        // 验证调用
        verify(roleService).findPermissionIdsByRoleId(roleId);
    }

    @Test
    @DisplayName("创建角色 - 成功场景")
    void createRole_Success() throws Exception {
        // 准备测试数据
        RoleDTO roleDTO = new RoleDTO();
        roleDTO.setRoleName("测试角色");
        roleDTO.setRoleCode("TEST_ROLE");
        roleDTO.setStatus(1);

        String requestBody = objectMapper.writeValueAsString(roleDTO);

        // 设置模拟行为
        when(roleService.createRole(any(SysRole.class))).thenReturn(1L);

        // 执行测试
        mockMvc.perform(post("/api/v1/roles")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data").value(1));

        // 验证调用
        verify(roleService).createRole(any(SysRole.class));
    }

    @Test
    @DisplayName("更新角色 - 成功场景")
    void updateRole_Success() throws Exception {
        // 准备测试数据
        RoleDTO roleDTO = new RoleDTO();
        roleDTO.setId(1L);
        roleDTO.setRoleName("更新角色");
        roleDTO.setRoleCode("UPDATE_ROLE");
        roleDTO.setStatus(1);

        String requestBody = objectMapper.writeValueAsString(roleDTO);

        // 执行测试
        mockMvc.perform(put("/api/v1/roles")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        // 验证调用
        verify(roleService).updateRole(any(SysRole.class));
    }

    @Test
    @DisplayName("删除角色 - 成功场景")
    void deleteRole_Success() throws Exception {
        // 准备测试数据
        Long roleId = 1L;

        // 执行测试
        mockMvc.perform(delete("/api/v1/roles/{id}", roleId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));

        // 验证调用
        verify(roleService).deleteRole(roleId);
    }

    @Test
    @DisplayName("获取角色详情 - 成功场景")
    void getRole_Success() throws Exception {
        // 准备测试数据
        Long roleId = 1L;
        RoleVO roleVO = new RoleVO();
        roleVO.setId(roleId);
        roleVO.setRoleName("测试角色");
        roleVO.setRoleCode("TEST_ROLE");
        roleVO.setPermissionIds(Arrays.asList(1L, 2L, 3L));

        // 设置模拟行为
        when(roleService.getRole(roleId)).thenReturn(roleVO);

        // 执行测试
        mockMvc.perform(get("/api/v1/roles/{id}", roleId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.roleName").value("测试角色"))
                .andExpect(jsonPath("$.data.roleCode").value("TEST_ROLE"))
                .andExpect(jsonPath("$.data.permissionIds").isArray())
                .andExpect(jsonPath("$.data.permissionIds.length()").value(3));

        // 验证调用
        verify(roleService).getRole(roleId);
    }

    @Test
    @DisplayName("分页查询角色列表 - 成功场景")
    void getRoleList_Success() throws Exception {
        // 准备测试数据
        RoleQueryDTO queryDTO = new RoleQueryDTO();
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(10);
        queryDTO.setRoleName("测试");

        String requestBody = objectMapper.writeValueAsString(queryDTO);

        RoleVO roleVO = new RoleVO();
        roleVO.setId(1L);
        roleVO.setRoleName("测试角色");
        roleVO.setRoleCode("TEST_ROLE");

        BasePageVO<RoleVO> pageVO = new BasePageVO<>();
        pageVO.setList(Collections.singletonList(roleVO));
        pageVO.setTotal(1L);
        pageVO.setPages(1);
        pageVO.setPageNum(1);
        pageVO.setPageSize(10);

        // 设置模拟行为
        when(roleService.getRoleList(any(RoleQueryDTO.class))).thenReturn(pageVO);

        // 执行测试
        mockMvc.perform(post("/api/v1/roles/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.list").isArray())
                .andExpect(jsonPath("$.data.list.length()").value(1))
                .andExpect(jsonPath("$.data.list[0].id").value(1))
                .andExpect(jsonPath("$.data.list[0].roleName").value("测试角色"));

        // 验证调用
        verify(roleService).getRoleList(any(RoleQueryDTO.class));
    }
}
