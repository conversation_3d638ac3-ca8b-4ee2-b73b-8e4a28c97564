server:
  port: 30030
  servlet:
    context-path: /auto-care-saas

spring:
  application:
    name: auto-care-saas
  profiles:
    active: dev
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
  redis:
    database: 0
    host: localhost
    port: 6379
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER

mybatis:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.extracme.saas.autocare.model.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true

jwt:
  secret: your-256-bit-secret-key-here
  expiration: 86400  # 24小时，单位：秒
  header: Authorization
  token-start-with: Bearer

logging:
  level:
    com.extracme.saas.autocare: debug
    org.springframework: info
    org.apache.ibatis: debug
    org.mybatis: debug
    com.extracme.saas.autocare.mapper: debug
    druid.sql: debug