spring:
  datasource:
    druid:
      url: ***********************************************************************************************************************************************************************************
      username: auto_care_user
      password: 8@C#f6gD%p3!
  redis:
    host: localhost
    port: 6379
    password: 

management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always

logging:
  level:
    com.extracme.saas.autocare: debug
    org.springframework: info 