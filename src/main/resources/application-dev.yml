spring:
  profiles: dev
  datasource:
    druid:
      url: *************************************************************************************************************************************************************************************************************
      username: devuser
      password: blk2ZsEB
  redis:
    host: test-redis
    port: 6379
    password: test_redis_password

# SMS配置
sms:
  code:
    expiration: 300  # 验证码有效期（秒）
  send:
    interval: 60     # 发送间隔（秒）
  daily:
    limit:
      mobile: 10     # 每个手机号每天最大发送次数
      ip: 20         # 每个IP每天最大发送次数

logging:
  level:
    com.extracme.saas.autocare: debug
    org.springframework: info
    org.apache.ibatis: debug
    org.mybatis: debug
    com.extracme.saas.autocare.mapper: debug
    druid.sql: debug
