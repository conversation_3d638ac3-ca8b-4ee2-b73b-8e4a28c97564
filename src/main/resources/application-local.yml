spring:
  datasource:
    druid:
      url: *************************************************************************************************************************************************************************************
      username: root
      password: 
  redis:
    host: localhost
    port: 6379
    password: 

management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always

# SMS配置
sms:
  code:
    expiration: 300  # 验证码有效期（秒）
  send:
    interval: 10     # 发送间隔（秒）
  daily:
    limit:
      mobile: 50     # 每个手机号每天最大发送次数
      ip: 100         # 每个IP每天最大发送次数

logging:
  level:
    com.extracme.saas.autocare: debug
    org.springframework: info
    org.apache.ibatis: debug
    org.mybatis: debug
    com.extracme.saas.autocare.mapper: debug
    druid.sql: debug
