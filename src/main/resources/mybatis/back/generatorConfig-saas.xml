<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>


    <!-- MySQL驱动jar包位置 -->
    <classPathEntry location="src/main/resources/mybatis/jdbc/mysql-connector-java-8.0.25.jar"/>


    <context id="MySqlContext" targetRuntime="MyBatis3DynamicSql" defaultModelType="flat">
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <property name="javaFileEncoding" value="UTF-8"/>

        <!-- 为模型生成序列化方法 -->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>
        <!-- 为生成的Java模型创建toString方法 -->
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>

        <!-- 注释 -->
        <commentGenerator>
            <property name="suppressAllComments" value="false"/>
            <property name="suppressDate" value="true"/>
            <property name="addRemarkComments" value="true"/>
            <property name="remarkColumnName" value="remarks"/>
        </commentGenerator>

        <!-- 数据库连接 -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="***********************************************************************************************************************************************"
                        userId="tester" password="dXtG3ISsnX6y">
            <property name="nullCatalogMeansCurrent" value="true"/>
        </jdbcConnection>

        <!-- 生成实体类的位置 -->
        <javaModelGenerator targetPackage="com.extracme.saas.autocare.model.entity"
                          targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!-- 生成Mapper接口的位置 -->
        <javaClientGenerator type="ANNOTATEDMAPPER"
                           targetPackage="com.extracme.saas.autocare.mapper.base"
                           targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>



        <!-- 租户表 -->
        <table tableName="sys_tenant" domainObjectName="SysTenant">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 用户表 -->
        <table tableName="sys_user" domainObjectName="SysUser">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 角色表 -->
        <table tableName="sys_role" domainObjectName="SysRole">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 操作日志表 -->
        <table tableName="sys_operate_log" domainObjectName="SysOperateLog">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>


        <!-- 权限表 -->
        <table tableName="sys_permission" domainObjectName="SysPermission">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 用户角色关联表 -->
        <table tableName="sys_user_role" domainObjectName="SysUserRole">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 角色权限关联表 -->
        <table tableName="sys_role_permission" domainObjectName="SysRolePermission">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 组织表 -->
        <table tableName="sys_organization" domainObjectName="SysOrganization">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 操作日志表 -->
        <table tableName="sys_operation_log" domainObjectName="SysOperationLog">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 验证码表 -->
        <table tableName="sys_verification_code" domainObjectName="SysVerificationCode">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 短信发送记录表 -->
        <table tableName="sms_send_record" domainObjectName="SmsSendRecord">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 工作流模板表 -->
        <table tableName="workflow_template" domainObjectName="WorkflowTemplate">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 活动定义表 -->
        <table tableName="activity_definition" domainObjectName="ActivityDefinition">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 活动状态表 -->
        <table tableName="activity_status" domainObjectName="ActivityStatus">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 活动状态转换表 -->
        <table tableName="activity_status_transition" domainObjectName="ActivityStatusTransition">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 工作流活动表 -->
        <table tableName="workflow_activity" domainObjectName="WorkflowActivity">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 活动转换表 -->
        <table tableName="activity_transition" domainObjectName="ActivityTransition">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 工作流实例表 -->
        <table tableName="workflow_instance" domainObjectName="WorkflowInstance">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 活动实例表 -->
        <table tableName="activity_instance" domainObjectName="ActivityInstance">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 工作流即时进度表 -->
        <table tableName="workflow_instant_progress" domainObjectName="WorkflowInstantProgress">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <!-- 数据字典表 -->
        <table tableName="data_dict_info" domainObjectName="DataDictInfo">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

    </context>
</generatorConfiguration>