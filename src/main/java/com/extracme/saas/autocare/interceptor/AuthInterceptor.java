package com.extracme.saas.autocare.interceptor;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;


import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import com.extracme.saas.autocare.annotation.RequireLogin;
import com.extracme.saas.autocare.annotation.RequirePermission;
import com.extracme.saas.autocare.exception.ForbiddenException;
import com.extracme.saas.autocare.exception.UnauthorizedException;
import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;
import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.service.PermissionService;
import com.extracme.saas.autocare.util.JwtUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class AuthInterceptor implements HandlerInterceptor {

    private static final String SESSION_USER_KEY = "LOGIN_USER";
    private static final String SESSION_PERMISSIONS_KEY = "USER_PERMISSIONS";
    private static final String SESSION_TOKEN_KEY = "USER_TOKEN";

    private final JwtUtil jwtUtil;
    private final PermissionService permissionService;
    private final TableUserService userService;
    private HttpServletRequest request;

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
        this.request = request;

        // 如果不是方法处理器，直接放行
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();

        // 获取token
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
        }

        // 如果有token，就验证并设置用户信息
        if (token != null) {
            if (!jwtUtil.validateToken(token)) {
                throw new UnauthorizedException("无效的token");
            }
            setSecurityContext(token);
        }

        // 检查是否需要登录
        RequireLogin requireLogin = method.getAnnotation(RequireLogin.class);
        if (requireLogin != null && token == null) {
            throw new UnauthorizedException("请先登录");
        }

        // 检查是否需要特定权限
        RequirePermission requirePermission = method.getAnnotation(RequirePermission.class);
        if (requirePermission != null) {
            if (token == null) {
                throw new UnauthorizedException("请先登录");
            }

            Long userId = Long.parseLong(jwtUtil.getUserIdFromToken(token));
            String permissionCode = requirePermission.value();

            if (!permissionService.hasPermission(userId, permissionCode)) {
                throw new ForbiddenException("没有操作权限");
            }
        }

        return true;
    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler, @Nullable Exception ex) {
        // 清理租户上下文
        TenantContextHolder.clear();
    }

    /**
     * 设置 Security 上下文信息
     * @param token JWT token
     */
    private void setSecurityContext(String token) {
        HttpSession session = request.getSession();

        // 1. 尝试从 Session 中获取用户信息
        LoginUser loginUser = (LoginUser) session.getAttribute(SESSION_USER_KEY);
        String sessionToken = (String) session.getAttribute(SESSION_TOKEN_KEY);

        // 2. 如果 Session 中没有用户信息，或者 token 已更新，则重新获取
        if (loginUser == null || !token.equals(sessionToken)) {
            // 从token中获取用户ID
            Long userId = Long.parseLong(jwtUtil.getUserIdFromToken(token));

            // 获取用户信息
            SysUser sysUser = userService.selectById(userId);
            if (sysUser == null) {
                throw new UnauthorizedException("用户不存在");
            }

            // 获取用户权限
            List<SysPermission> permissionList = permissionService.getUserPermissions(userId);
            Set<String> permissions = permissionList.stream()
                .map(SysPermission::getPermissionCode)
                .collect(Collectors.toSet());

            // 创建LoginUser对象
            loginUser = new LoginUser();
            loginUser.setUser(sysUser);
            loginUser.setPermissions(permissions);
            loginUser.setToken(token);
            loginUser.setLoginTime(System.currentTimeMillis());

            // 从JWT中获取过期时间并设置到LoginUser中
            Date expirationDate = jwtUtil.getExpirationDateFromToken(token);
            if (expirationDate != null) {
                loginUser.setExpireTime(expirationDate.getTime());
            }

            loginUser.setIpaddr(request.getRemoteAddr());
            loginUser.setTenantId(sysUser.getTenantId()); // 直接使用 Long 类型

            // 将信息存储到 Session 中
            session.setAttribute(SESSION_USER_KEY, loginUser);
            session.setAttribute(SESSION_PERMISSIONS_KEY, permissions);
            session.setAttribute(SESSION_TOKEN_KEY, token);

            // 设置租户上下文
            TenantContextHolder.setTenant(sysUser.getTenantId());
        } else {
            // 如果session中有用户信息，直接设置租户上下文
            TenantContextHolder.setTenant(loginUser.getTenantId());
        }

        // 创建认证信息
        UsernamePasswordAuthenticationToken authentication =
            new UsernamePasswordAuthenticationToken(loginUser, null, Collections.emptyList());

        // 设置认证信息到上下文
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }
}