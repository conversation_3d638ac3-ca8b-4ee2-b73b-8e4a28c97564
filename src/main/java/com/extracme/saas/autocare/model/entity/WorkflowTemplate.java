package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table workflow_template
 */
public class WorkflowTemplate implements Serializable {
    /**
     * Database Column Remarks:
     *   流程模板编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   流程模板名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.workflow_name")
    private String workflowName;

    /**
     * Database Column Remarks:
     *   任务类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.task_type")
    private Integer taskType;

    /**
     * Database Column Remarks:
     *   修理厂类型：-1表示全部，1表示合作，2表示非合作
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.repair_factory_type")
    private Integer repairFactoryType;

    /**
     * Database Column Remarks:
     *   产品线
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.sub_product_line")
    private Integer subProductLine;

    /**
     * Database Column Remarks:
     *   是否启用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.is_active")
    private Integer isActive;

    /**
     * Database Column Remarks:
     *   租户编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.tenant_id")
    private Integer tenantId;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   流程描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.description")
    private String description;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.workflow_name")
    public String getWorkflowName() {
        return workflowName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.workflow_name")
    public void setWorkflowName(String workflowName) {
        this.workflowName = workflowName == null ? null : workflowName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.task_type")
    public Integer getTaskType() {
        return taskType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.task_type")
    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.repair_factory_type")
    public Integer getRepairFactoryType() {
        return repairFactoryType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.repair_factory_type")
    public void setRepairFactoryType(Integer repairFactoryType) {
        this.repairFactoryType = repairFactoryType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.sub_product_line")
    public Integer getSubProductLine() {
        return subProductLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.sub_product_line")
    public void setSubProductLine(Integer subProductLine) {
        this.subProductLine = subProductLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.is_active")
    public Integer getIsActive() {
        return isActive;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.is_active")
    public void setIsActive(Integer isActive) {
        this.isActive = isActive;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.tenant_id")
    public Integer getTenantId() {
        return tenantId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.tenant_id")
    public void setTenantId(Integer tenantId) {
        this.tenantId = tenantId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.description")
    public String getDescription() {
        return description;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.description")
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", workflowName=").append(workflowName);
        sb.append(", taskType=").append(taskType);
        sb.append(", repairFactoryType=").append(repairFactoryType);
        sb.append(", subProductLine=").append(subProductLine);
        sb.append(", isActive=").append(isActive);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", createTime=").append(createTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", description=").append(description);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}