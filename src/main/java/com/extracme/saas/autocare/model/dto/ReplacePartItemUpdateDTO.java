package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel(description = "换件项目修改DTO")
public class ReplacePartItemUpdateDTO {

    @ApiModelProperty(value = "id")
    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty(value = "车辆运营单位ID")
    @NotBlank(message = "车辆运营单位不能为空")
    private String orgId;

    @ApiModelProperty(value = "零件名称")
    @NotBlank(message = "零件名称不能为空")
    private String partName;

    @ApiModelProperty(value = "零件分组ID")
    @NotNull(message = "零件分组不能为空")
    private Long groupingId;

    @ApiModelProperty(value = "原厂零件号")
    @NotBlank(message = "原厂零件号不能为空")
    private String originalFactoryPartNo;

    @ApiModelProperty(value = "原厂零件名称")
    private String originalFactoryPartName;

    @ApiModelProperty(value = "系统市场价(元)")
    private BigDecimal sysMarketPrice;

    @ApiModelProperty(value = "系统专修价(元)")
    private BigDecimal sysMajorInPrice;

    @ApiModelProperty(value = "本地市场价(元)")
    @NotNull(message = "本地市场价不能为空")
    private BigDecimal localMarketPrice;

    @ApiModelProperty(value = "状态（1：有效 0：无效）")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "本地专修价(元)")
    private BigDecimal localMajorInPrice;

    @ApiModelProperty(value = "车型SEQ")
    @NotNull(message = "车型不能为空")
    private Long vehicleModelSeq;

    @ApiModelProperty(value = "备注")
    private String remark;

}