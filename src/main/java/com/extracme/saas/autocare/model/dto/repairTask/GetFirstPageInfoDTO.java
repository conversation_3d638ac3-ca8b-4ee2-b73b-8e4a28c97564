package com.extracme.saas.autocare.model.dto.repairTask;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "获取首页信息DTO")
public class GetFirstPageInfoDTO extends BasePageDTO {

    @ApiModelProperty(value = "修理厂ID", example = "R001")
    private String repairDepotId;

    @ApiModelProperty(value = "机构id", example = "R001")
    private String orgId;
}