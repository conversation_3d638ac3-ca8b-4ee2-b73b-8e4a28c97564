package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 车辆验收通过DTO
 * 
 * <AUTHOR> Code
 * @date 2024/05/25
 */
@Data
@ApiModel(value = "车辆验收通过信息")
public class VehicleInspectionApproveDTO {
    
    @ApiModelProperty(value = "任务编号", required = true)
    @NotBlank(message = "任务编号不能为空")
    private String taskNo;
    
    @ApiModelProperty(value = "验收备注")
    private String inspectionRemark;
    
    @ApiModelProperty(value = "验收图片列表")
    private List<String> inspectionPictures;
    
    @ApiModelProperty(value = "验收视频列表")
    private List<String> inspectionVideos;
}