package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   修理项目
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table mtc_repair_item
 */
public class MtcRepairItem implements Serializable {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   组织机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.org_id")
    private String orgId;

    /**
     * Database Column Remarks:
     *   修理名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.repair_name")
    private String repairName;

    /**
     * Database Column Remarks:
     *   修理类型分组ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.grouping_id")
    private Long groupingId;

    /**
     * Database Column Remarks:
     *   系统市场价(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.sys_market_price")
    private BigDecimal sysMarketPrice;

    /**
     * Database Column Remarks:
     *   系统专修价(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.sys_major_in_price")
    private BigDecimal sysMajorInPrice;

    /**
     * Database Column Remarks:
     *   本地市场价(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.local_market_price")
    private BigDecimal localMarketPrice;

    /**
     * Database Column Remarks:
     *   本地专修价(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.local_major_in_price")
    private BigDecimal localMajorInPrice;

    /**
     * Database Column Remarks:
     *   是否支持另行报价 1-是 0-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.separate_quotation")
    private Integer separateQuotation;

    /**
     * Database Column Remarks:
     *   状态（1：有效 0：无效）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.status")
    private Integer status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.remark")
    private String remark;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.created_time")
    private Date createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.updated_time")
    private Date updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.org_id")
    public String getOrgId() {
        return orgId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.org_id")
    public void setOrgId(String orgId) {
        this.orgId = orgId == null ? null : orgId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.repair_name")
    public String getRepairName() {
        return repairName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.repair_name")
    public void setRepairName(String repairName) {
        this.repairName = repairName == null ? null : repairName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.grouping_id")
    public Long getGroupingId() {
        return groupingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.grouping_id")
    public void setGroupingId(Long groupingId) {
        this.groupingId = groupingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.sys_market_price")
    public BigDecimal getSysMarketPrice() {
        return sysMarketPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.sys_market_price")
    public void setSysMarketPrice(BigDecimal sysMarketPrice) {
        this.sysMarketPrice = sysMarketPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.sys_major_in_price")
    public BigDecimal getSysMajorInPrice() {
        return sysMajorInPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.sys_major_in_price")
    public void setSysMajorInPrice(BigDecimal sysMajorInPrice) {
        this.sysMajorInPrice = sysMajorInPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.local_market_price")
    public BigDecimal getLocalMarketPrice() {
        return localMarketPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.local_market_price")
    public void setLocalMarketPrice(BigDecimal localMarketPrice) {
        this.localMarketPrice = localMarketPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.local_major_in_price")
    public BigDecimal getLocalMajorInPrice() {
        return localMajorInPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.local_major_in_price")
    public void setLocalMajorInPrice(BigDecimal localMajorInPrice) {
        this.localMajorInPrice = localMajorInPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.separate_quotation")
    public Integer getSeparateQuotation() {
        return separateQuotation;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.separate_quotation")
    public void setSeparateQuotation(Integer separateQuotation) {
        this.separateQuotation = separateQuotation;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.status")
    public Integer getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.status")
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.remark")
    public String getRemark() {
        return remark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.remark")
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.created_time")
    public Date getCreatedTime() {
        return createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.created_time")
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.updated_time")
    public Date getUpdatedTime() {
        return updatedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item.updated_time")
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orgId=").append(orgId);
        sb.append(", repairName=").append(repairName);
        sb.append(", groupingId=").append(groupingId);
        sb.append(", sysMarketPrice=").append(sysMarketPrice);
        sb.append(", sysMajorInPrice=").append(sysMajorInPrice);
        sb.append(", localMarketPrice=").append(localMarketPrice);
        sb.append(", localMajorInPrice=").append(localMajorInPrice);
        sb.append(", separateQuotation=").append(separateQuotation);
        sb.append(", status=").append(status);
        sb.append(", remark=").append(remark);
        sb.append(", createBy=").append(createBy);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}