package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 维修报价提交审核DTO
 */
@Data
@ApiModel(description = "维修报价提交审核DTO")
public class RepairQuoteSubmitDTO {
    
    /**
     * 任务编号
     */
    @NotBlank(message = "任务编号不能为空")
    @Size(max = 32, message = "任务编号长度不能超过32个字符")
    @ApiModelProperty(value = "任务编号", required = true, example = "TASK202307120001")
    private String taskNo;
    
    /**
     * 预计修理天数
     */
    @NotNull(message = "预计修理天数不能为空")
    @Min(value = 1, message = "预计修理天数必须大于0")
    @ApiModelProperty(value = "预计修理天数", required = true, example = "3")
    private Long expectedRepairDays;
    
    /**
     * 定损单金额
     */
    // TODO @NotNull(message = "定损单金额不能为空")
    @DecimalMin(value = "0.01", message = "定损单金额必须大于0")
    @Digits(integer = 10, fraction = 2, message = "定损单金额格式不正确，整数位最多10位，小数位最多2位")
    @ApiModelProperty(value = "定损单金额", required = true, example = "1500.00")
    private BigDecimal lossOrderAmount;
    
    /**
     * 维修保险总金额
     */
    // TODO @NotNull(message = "维修保险总金额不能为空")
    @DecimalMin(value = "0.01", message = "维修保险总金额必须大于0")
    @Digits(integer = 10, fraction = 2, message = "维修保险总金额格式不正确，整数位最多10位，小数位最多2位")
    @ApiModelProperty(value = "维修保险总金额", required = true, example = "1200.00")
    private BigDecimal repairInsuranceTotalAmount;
    
    /**
     * 客户直付标识（1:是, 2:否）
     */
    @NotNull(message = "客户直付标识不能为空")
    @Min(value = 0, message = "客户直付标识只能是1或2")
    @Max(value = 1, message = "客户直付标识只能是1或2")
    @ApiModelProperty(value = "客户直付标识（1:是, 0:否）", required = true, example = "1", allowableValues = "0,1")
    private Integer custPaysDirect;
    
    /**
     * 客户直付金额
     */
    @Digits(integer = 10, fraction = 2, message = "客户直付金额格式不正确，整数位最多10位，小数位最多2位")
    @ApiModelProperty(value = "客户直付金额", example = "300.00", notes = "当客户直付标识为1时必填")
    private BigDecimal custAmount;
    
    /**
     * 损坏部位图片
     */
    // TODO @NotNull(message = "损坏部位图片不能为空")
    // TODO @Size(min = 1, message = "至少上传一张损坏部位图片")
    @ApiModelProperty(value = "损坏部位图片", required = true, notes = "图片URL列表，最多48张")
    private List<String> damagedPartPicture;
    
    /**
     * 损坏部位视频
     */
    @ApiModelProperty(value = "损坏部位视频", notes = "视频URL列表")
    private List<String> damagedPartVideo;
    
    /**
     * 事故责任认定书图片
     */
    @ApiModelProperty(value = "事故责任认定书图片", notes = "图片URL列表，最多4张")
    private List<String> accidentLiabilityConfirmationPicture;
    
    /**
     * 保司定损单图片
     */
    @ApiModelProperty(value = "保司定损单图片", notes = "图片URL列表，最多4张")
    private List<String> insuranceCompanyLossOrderPicture;
    
    /**
     * 我方驾驶证图片
     */
    @ApiModelProperty(value = "我方驾驶证图片", notes = "图片URL列表，最多4张")
    private List<String> ourDriverLicensePicture;
    
    /**
     * 客户直付凭证
     */
    @ApiModelProperty(value = "客户直付凭证", notes = "图片URL列表")
    private List<String> custPicture;
    
    /**
     * 预估理赔金额
     */
    @ApiModelProperty(value = "预估理赔金额", example = "2000.00")
    @Digits(integer = 10, fraction = 2, message = "预估理赔金额格式不正确，整数位最多10位，小数位最多2位")
    private BigDecimal estimatedClaimAmount;
    
    /**
     * 检查图片数量是否超出范围
     * @return 是否超出范围
     */
    @ApiModelProperty(hidden = true)
    public boolean pictureSizeOutOfRange() {
        if (CollectionUtils.isNotEmpty(damagedPartPicture) && damagedPartPicture.size() > 48) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(accidentLiabilityConfirmationPicture) && accidentLiabilityConfirmationPicture.size() > 4) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(insuranceCompanyLossOrderPicture) && insuranceCompanyLossOrderPicture.size() > 4) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(ourDriverLicensePicture) && ourDriverLicensePicture.size() > 4) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(custPicture) && custPicture.size() > 4) {
            return true;
        }
        return false;
    }
}
