package com.extracme.saas.autocare.model.dto.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.extracme.saas.autocare.enums.TaskTypeEnum;

/**
 * 工作流启动请求对象
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Data
@ApiModel("工作流启动请求")
public class WorkflowStartDTO {

    /**
     * 任务类型：
     * 0 - 自建任务
     * 1 - 事故维修
     * 2 - 自费维修
     * 3 - 车辆保养
     * 4 - 轮胎任务
     * 5 - 自费维修(原车辆保养)
     * 6 - 常规保养
     * 7 - 终端维修
     * @see TaskTypeEnum
     */
    @NotNull(message = "任务类型不能为空")
    @ApiModelProperty(value = "任务类型", required = true, example = "1", notes = "0:自建任务, 1:事故维修, 2:自费维修, 3:车辆保养, 4:轮胎任务, 5:自费维修(原车辆保养), 6:常规保养, 7:终端维修")
    private Integer taskType;

    /**
     * 修理厂类型（-1:全部, 1:合作, 2:非合作）
     */
    @NotNull(message = "修理厂类型不能为空")
    @ApiModelProperty(value = "修理厂类型", required = true, example = "1", notes = "-1:全部, 1:合作, 2:非合作")
    private Integer repairFactoryType;

    /**
     * 子产品线
     */
    @NotNull(message = "子产品线不能为空")
    @ApiModelProperty(value = "子产品线", required = true, example = "1")
    private Integer subProductLine;

    /**
     * 业务对象ID（例如维修任务ID）
     */
    @NotBlank(message = "业务对象ID不能为空")
    @ApiModelProperty(value = "业务对象ID", required = true)
    private String businessId;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    @ApiModelProperty(value = "操作人", required = true)
    private String operator;

    /**
     * 备注说明
     */
    @ApiModelProperty("备注说明")
    private String remarks;
}