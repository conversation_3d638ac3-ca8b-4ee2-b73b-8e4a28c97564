package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   维修任务车辆出厂记录表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table mtc_repair_task_leaving_factory
 */
public class MtcRepairTaskLeavingFactory implements Serializable {
    /**
     * Database Column Remarks:
     *   主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   关联任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.task_no")
    private String taskNo;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.vin")
    private String vin;

    /**
     * Database Column Remarks:
     *   提车人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.name")
    private String name;

    /**
     * Database Column Remarks:
     *   提车人联系方式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.phone_number")
    private String phoneNumber;

    /**
     * Database Column Remarks:
     *   车辆出厂时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.delivery_time")
    private String deliveryTime;

    /**
     * Database Column Remarks:
     *   出厂图片,多个图片逗号分隔
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.delivery_pictures")
    private String deliveryPictures;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.remark")
    private String remark;

    /**
     * Database Column Remarks:
     *   修理厂ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_depot_id")
    private String repairDepotId;

    /**
     * Database Column Remarks:
     *   修理厂名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_depot_name")
    private String repairDepotName;

    /**
     * Database Column Remarks:
     *   修理厂组织机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_depot_org_id")
    private String repairDepotOrgId;

    /**
     * Database Column Remarks:
     *   供应商sap编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_depot_sap_code")
    private String repairDepotSapCode;

    /**
     * Database Column Remarks:
     *   维修厂登记状态 1-已登记 2-未登记 3-已关闭
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.leaving_status")
    private Integer leavingStatus;

    /**
     * Database Column Remarks:
     *   维修任务流入时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_task_inflow_time")
    private Date repairTaskInflowTime;

    /**
     * Database Column Remarks:
     *   维修任务接车时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_task_receive_time")
    private Date repairTaskReceiveTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.created_time")
    private Date createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.updated_time")
    private Date updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.task_no")
    public String getTaskNo() {
        return taskNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.task_no")
    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo == null ? null : taskNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.vin")
    public String getVin() {
        return vin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.vin")
    public void setVin(String vin) {
        this.vin = vin == null ? null : vin.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.name")
    public String getName() {
        return name;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.name")
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.phone_number")
    public String getPhoneNumber() {
        return phoneNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.phone_number")
    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber == null ? null : phoneNumber.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.delivery_time")
    public String getDeliveryTime() {
        return deliveryTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.delivery_time")
    public void setDeliveryTime(String deliveryTime) {
        this.deliveryTime = deliveryTime == null ? null : deliveryTime.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.delivery_pictures")
    public String getDeliveryPictures() {
        return deliveryPictures;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.delivery_pictures")
    public void setDeliveryPictures(String deliveryPictures) {
        this.deliveryPictures = deliveryPictures == null ? null : deliveryPictures.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.remark")
    public String getRemark() {
        return remark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.remark")
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_depot_id")
    public String getRepairDepotId() {
        return repairDepotId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_depot_id")
    public void setRepairDepotId(String repairDepotId) {
        this.repairDepotId = repairDepotId == null ? null : repairDepotId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_depot_name")
    public String getRepairDepotName() {
        return repairDepotName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_depot_name")
    public void setRepairDepotName(String repairDepotName) {
        this.repairDepotName = repairDepotName == null ? null : repairDepotName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_depot_org_id")
    public String getRepairDepotOrgId() {
        return repairDepotOrgId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_depot_org_id")
    public void setRepairDepotOrgId(String repairDepotOrgId) {
        this.repairDepotOrgId = repairDepotOrgId == null ? null : repairDepotOrgId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_depot_sap_code")
    public String getRepairDepotSapCode() {
        return repairDepotSapCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_depot_sap_code")
    public void setRepairDepotSapCode(String repairDepotSapCode) {
        this.repairDepotSapCode = repairDepotSapCode == null ? null : repairDepotSapCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.leaving_status")
    public Integer getLeavingStatus() {
        return leavingStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.leaving_status")
    public void setLeavingStatus(Integer leavingStatus) {
        this.leavingStatus = leavingStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_task_inflow_time")
    public Date getRepairTaskInflowTime() {
        return repairTaskInflowTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_task_inflow_time")
    public void setRepairTaskInflowTime(Date repairTaskInflowTime) {
        this.repairTaskInflowTime = repairTaskInflowTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_task_receive_time")
    public Date getRepairTaskReceiveTime() {
        return repairTaskReceiveTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.repair_task_receive_time")
    public void setRepairTaskReceiveTime(Date repairTaskReceiveTime) {
        this.repairTaskReceiveTime = repairTaskReceiveTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.created_time")
    public Date getCreatedTime() {
        return createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.created_time")
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.updated_time")
    public Date getUpdatedTime() {
        return updatedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task_leaving_factory.updated_time")
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task_leaving_factory")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskNo=").append(taskNo);
        sb.append(", vin=").append(vin);
        sb.append(", name=").append(name);
        sb.append(", phoneNumber=").append(phoneNumber);
        sb.append(", deliveryTime=").append(deliveryTime);
        sb.append(", deliveryPictures=").append(deliveryPictures);
        sb.append(", remark=").append(remark);
        sb.append(", repairDepotId=").append(repairDepotId);
        sb.append(", repairDepotName=").append(repairDepotName);
        sb.append(", repairDepotOrgId=").append(repairDepotOrgId);
        sb.append(", repairDepotSapCode=").append(repairDepotSapCode);
        sb.append(", leavingStatus=").append(leavingStatus);
        sb.append(", repairTaskInflowTime=").append(repairTaskInflowTime);
        sb.append(", repairTaskReceiveTime=").append(repairTaskReceiveTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}