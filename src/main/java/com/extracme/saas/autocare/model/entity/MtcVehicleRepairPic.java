package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   维修任务图片/视频表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table mtc_vehicle_repair_pic
 */
public class MtcVehicleRepairPic implements Serializable {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.task_no")
    private String taskNo;

    /**
     * Database Column Remarks:
     *   图片区分 1:损坏部位图片 2:车辆维修图片 10:行驶证图片 11:保单图片 12:事故图片 13:车损图片(标的车) 14:车损图片(三者车) 15:理赔材料 16:其他图片 17:验收视频 18:轮胎更换修复后图片 19:事故责任认定书图片 20:保司定损单 21:我方驾驶证图片 22:维修任务垫付 23:定损视频
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.pic_type")
    private Short picType;

    /**
     * Database Column Remarks:
     *   车辆维修图片URL
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.pic_url")
    private String picUrl;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.created_time")
    private Date createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.updated_time")
    private Date updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_repair_pic")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.task_no")
    public String getTaskNo() {
        return taskNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.task_no")
    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo == null ? null : taskNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.pic_type")
    public Short getPicType() {
        return picType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.pic_type")
    public void setPicType(Short picType) {
        this.picType = picType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.pic_url")
    public String getPicUrl() {
        return picUrl;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.pic_url")
    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl == null ? null : picUrl.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.created_time")
    public Date getCreatedTime() {
        return createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.created_time")
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.updated_time")
    public Date getUpdatedTime() {
        return updatedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.updated_time")
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_repair_pic")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskNo=").append(taskNo);
        sb.append(", picType=").append(picType);
        sb.append(", picUrl=").append(picUrl);
        sb.append(", createBy=").append(createBy);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}