package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   维修项目本地库表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table mtc_repair_item_library_local
 */
public class MtcRepairItemLibraryLocal implements Serializable {
    /**
     * Database Column Remarks:
     *   主键
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   维修项目库项目id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.item_id")
    private Long itemId;

    /**
     * Database Column Remarks:
     *   项目编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.item_no")
    private String itemNo;

    /**
     * Database Column Remarks:
     *   运营公司id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.org_id")
    private String orgId;

    /**
     * Database Column Remarks:
     *   运营公司名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.org_name")
    private String orgName;

    /**
     * Database Column Remarks:
     *   工时费本地市场价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.hour_fee_local_market_price")
    private BigDecimal hourFeeLocalMarketPrice;

    /**
     * Database Column Remarks:
     *   工时费本地上限
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.hour_fee_local_highest_price")
    private BigDecimal hourFeeLocalHighestPrice;

    /**
     * Database Column Remarks:
     *   材料费本地市场价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.material_cost_local_market_price")
    private BigDecimal materialCostLocalMarketPrice;

    /**
     * Database Column Remarks:
     *   材料费本地上限
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.material_cost_local_highest_price")
    private BigDecimal materialCostLocalHighestPrice;

    /**
     * Database Column Remarks:
     *   状态(0：无效  1：有效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.status")
    private Integer status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.misc_Desc")
    private String miscDesc;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.created_time")
    private Date createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.updated_time")
    private Date updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.item_id")
    public Long getItemId() {
        return itemId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.item_id")
    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.item_no")
    public String getItemNo() {
        return itemNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.item_no")
    public void setItemNo(String itemNo) {
        this.itemNo = itemNo == null ? null : itemNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.org_id")
    public String getOrgId() {
        return orgId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.org_id")
    public void setOrgId(String orgId) {
        this.orgId = orgId == null ? null : orgId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.org_name")
    public String getOrgName() {
        return orgName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.org_name")
    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.hour_fee_local_market_price")
    public BigDecimal getHourFeeLocalMarketPrice() {
        return hourFeeLocalMarketPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.hour_fee_local_market_price")
    public void setHourFeeLocalMarketPrice(BigDecimal hourFeeLocalMarketPrice) {
        this.hourFeeLocalMarketPrice = hourFeeLocalMarketPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.hour_fee_local_highest_price")
    public BigDecimal getHourFeeLocalHighestPrice() {
        return hourFeeLocalHighestPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.hour_fee_local_highest_price")
    public void setHourFeeLocalHighestPrice(BigDecimal hourFeeLocalHighestPrice) {
        this.hourFeeLocalHighestPrice = hourFeeLocalHighestPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.material_cost_local_market_price")
    public BigDecimal getMaterialCostLocalMarketPrice() {
        return materialCostLocalMarketPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.material_cost_local_market_price")
    public void setMaterialCostLocalMarketPrice(BigDecimal materialCostLocalMarketPrice) {
        this.materialCostLocalMarketPrice = materialCostLocalMarketPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.material_cost_local_highest_price")
    public BigDecimal getMaterialCostLocalHighestPrice() {
        return materialCostLocalHighestPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.material_cost_local_highest_price")
    public void setMaterialCostLocalHighestPrice(BigDecimal materialCostLocalHighestPrice) {
        this.materialCostLocalHighestPrice = materialCostLocalHighestPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.status")
    public Integer getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.status")
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.misc_Desc")
    public String getMiscDesc() {
        return miscDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.misc_Desc")
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc == null ? null : miscDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.created_time")
    public Date getCreatedTime() {
        return createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.created_time")
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.updated_time")
    public Date getUpdatedTime() {
        return updatedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library_local.updated_time")
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library_local")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", itemId=").append(itemId);
        sb.append(", itemNo=").append(itemNo);
        sb.append(", orgId=").append(orgId);
        sb.append(", orgName=").append(orgName);
        sb.append(", hourFeeLocalMarketPrice=").append(hourFeeLocalMarketPrice);
        sb.append(", hourFeeLocalHighestPrice=").append(hourFeeLocalHighestPrice);
        sb.append(", materialCostLocalMarketPrice=").append(materialCostLocalMarketPrice);
        sb.append(", materialCostLocalHighestPrice=").append(materialCostLocalHighestPrice);
        sb.append(", status=").append(status);
        sb.append(", miscDesc=").append(miscDesc);
        sb.append(", createBy=").append(createBy);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}