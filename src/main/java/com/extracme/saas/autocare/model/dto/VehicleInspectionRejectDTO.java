package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 车辆验收不通过DTO
 * 
 * <AUTHOR> Code
 * @date 2024/05/25
 */
@Data
@ApiModel(value = "车辆验收不通过信息")
public class VehicleInspectionRejectDTO {
    
    @ApiModelProperty(value = "任务编号", required = true)
    @NotBlank(message = "任务编号不能为空")
    private String taskNo;
    
    @ApiModelProperty(value = "拒绝原因", required = true)
    @NotBlank(message = "拒绝原因不能为空")
    private String rejectReason;
    
    @ApiModelProperty(value = "问题图片列表")
    private List<String> problemPictures;
}