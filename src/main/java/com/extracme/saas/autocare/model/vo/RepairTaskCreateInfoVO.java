package com.extracme.saas.autocare.model.vo;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "维修任务创建信息视图对象")
public class RepairTaskCreateInfoVO {

    @ApiModelProperty(value = "送修时间", example = "2024-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date sendRepairTime;

    @ApiModelProperty(value = "受损部位描述", example = "前保险杠、左前叶子板")
    private String damagedPartDescribe;

    @ApiModelProperty(value = "情况描述", notes = "原梧桐字段")
    private String situationDesc;

    @ApiModelProperty(value = "创建照片", notes = "原梧桐字段")
    private List<String> createPicList;
}
