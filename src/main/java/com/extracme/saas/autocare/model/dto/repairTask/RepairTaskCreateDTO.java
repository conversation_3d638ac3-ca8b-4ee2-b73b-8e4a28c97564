package com.extracme.saas.autocare.model.dto.repairTask;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "维修任务创建DTO")
public class RepairTaskCreateDTO {

    @ApiModelProperty(value = "维修类型", notes = "1：事故维修 2：自费维修 3：车辆保养 4：轮胎任务 6：常规保养", example = "2")
    @NotNull(message = "维修类型不能为空")
    private Integer repairTypeId;

    @ApiModelProperty(value = "车架号", example = "LSGPC52U1KF123456")
    @Size(max = 20, message = "车架号的输入长度不能超过20位")
    @NotBlank(message = "车架号不能为空")
    private String vin;

    @ApiModelProperty(value = "车牌号", example = "京A12345")
    @Size(max = 10, message = "车牌号的输入长度不能超过10位")
    @NotBlank(message = "车牌号不能为空")
    private String vehicleNo;

    @ApiModelProperty(value = "修理厂ID", example = "4301")
    @Size(max = 30, message = "修理厂ID的输入长度不能超过30位")
    @NotBlank(message = "修理厂ID不能为空")
    private String repairDepotId;

    @ApiModelProperty(value = "总里程数", example = "12345.67")
    private BigDecimal totalMileage;

    @ApiModelProperty(value = "送修时间", example = "2024-05-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date sendRepairTime;

    @ApiModelProperty(value = "受损部位描述", example = "前保险杠、左前叶子板")
    private String damagedPartDescribe;

    @ApiModelProperty(value = "情况描述", notes = "原梧桐字段")
    private String situationDesc;

    @ApiModelProperty(value = "维修任务创建图片列表")
    private List<String> createPicList;
}