package com.extracme.saas.autocare.model.vo.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 节点状态转换规则视图对象
 *
 * <AUTHOR>
 * @date 2024/05/20
 */
@Data
@ApiModel(description = "节点状态转换规则视图对象")
public class ActivityStatusTransitionVO {

    @ApiModelProperty(value = "转换规则编号")
    private Long id;

    @ApiModelProperty(value = "关联的活动节点转换规则编号")
    private Long activityTransitionId;

    @ApiModelProperty(value = "活动编码")
    private String activityCode;

    @ApiModelProperty(value = "起始状态编码")
    private String fromStatusCode;

    @ApiModelProperty(value = "目标状态编码")
    private String toStatusCode;

    @ApiModelProperty(value = "规则说明")
    private String description;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;
}
