package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   维修任务表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table mtc_repair_task
 */
public class MtcRepairTask implements Serializable {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.task_no")
    private String taskNo;

    /**
     * Database Column Remarks:
     *   任务类型（1：从调度直接推送过来的任务 2：手动刷数据从老平台移植过来的任务 3： 内部生成）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.task_type")
    private Short taskType;

    /**
     * Database Column Remarks:
     *   车辆运营机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.org_id")
    private String orgId;

    /**
     * Database Column Remarks:
     *   车辆运营机构名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.org_name")
    private String orgName;

    /**
     * Database Column Remarks:
     *   车辆所属组织机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.operate_org_id")
    private String operateOrgId;

    /**
     * Database Column Remarks:
     *   车辆所属组织机构名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.operate_org_name")
    private String operateOrgName;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_no")
    private String vehicleNo;

    /**
     * Database Column Remarks:
     *   车型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_model_seq")
    private Long vehicleModelSeq;

    /**
     * Database Column Remarks:
     *   车型名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_model_info")
    private String vehicleModelInfo;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vin")
    private String vin;

    /**
     * Database Column Remarks:
     *   车辆保险所属
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_company_name")
    private String insuranceCompanyName;

    /**
     * Database Column Remarks:
     *   修理类型ID（0：自建任务 1：进保维修 2：自费维修 3：车辆保养 4：轮胎任务 6：常规保养）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_type_id")
    private Integer repairTypeId;

    /**
     * Database Column Remarks:
     *   修理类型名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_type_name")
    private String repairTypeName;

    /**
     * Database Column Remarks:
     *   修理级别 A,B,C
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_grade")
    private String repairGrade;

    /**
     * Database Column Remarks:
     *   修理厂ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_id")
    private String repairDepotId;

    /**
     * Database Column Remarks:
     *   修理厂名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_name")
    private String repairDepotName;

    /**
     * Database Column Remarks:
     *   修理厂组织机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_org_id")
    private String repairDepotOrgId;

    /**
     * Database Column Remarks:
     *   供应商sap编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_sap_code")
    private String repairDepotSapCode;

    /**
     * Database Column Remarks:
     *   sap数据发送状态 0未发送 1已发送 2发送失败 3冲销需重新发送 4发送失败需重新发送 5发送中
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sap_send_status")
    private Integer sapSendStatus;

    /**
     * Database Column Remarks:
     *   sap同步标识 0自动同步 1手动同步
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sap_sync_flag")
    private Integer sapSyncFlag;

    /**
     * Database Column Remarks:
     *   sap发送成功时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sap_success_time")
    private Date sapSuccessTime;

    /**
     * Database Column Remarks:
     *   配件库类型(1:自有配件库 2:精友配件库)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.parts_library_type")
    private Integer partsLibraryType;

    /**
     * Database Column Remarks:
     *   任务创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.task_create_time")
    private Date taskCreateTime;

    /**
     * Database Column Remarks:
     *   调度任务创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.task_inflow_time")
    private Date taskInflowTime;

    /**
     * Database Column Remarks:
     *   车辆接收时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_recive_time")
    private Date vehicleReciveTime;

    /**
     * Database Column Remarks:
     *   车辆验收完成时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_check_time")
    private Date vehicleCheckTime;

    /**
     * Database Column Remarks:
     *   车辆修理完成时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_repair_time")
    private Date vehicleRepairTime;

    /**
     * Database Column Remarks:
     *   改派审核通过时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.reassignment_pass_time")
    private Date reassignmentPassTime;

    /**
     * Database Column Remarks:
     *   结案时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.settle_closing_time")
    private Date settleClosingTime;

    /**
     * Database Column Remarks:
     *   预计修理天数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.expected_repair_days")
    private Long expectedRepairDays;

    /**
     * Database Column Remarks:
     *   预计修理完成日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.expected_repair_complete")
    private Date expectedRepairComplete;

    /**
     * Database Column Remarks:
     *   事故报案号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.accident_report_number")
    private String accidentReportNumber;

    /**
     * Database Column Remarks:
     *   是否需要维修 0:否 1:是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_flag")
    private Short repairFlag;

    /**
     * Database Column Remarks:
     *   终端ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.terminal_id")
    private String terminalId;

    /**
     * Database Column Remarks:
     *   总里程数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.total_mileage")
    private BigDecimal totalMileage;

    /**
     * Database Column Remarks:
     *   终端实时里程数(核损时)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.terminal_mileage")
    private String terminalMileage;

    /**
     * Database Column Remarks:
     *   关联订单
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.associated_order")
    private String associatedOrder;

    /**
     * Database Column Remarks:
     *   订单类型 1=长租订单 2=门店订单 3=渠道订单 4=内部订单 5=分时订单
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.order_type")
    private Integer orderType;

    /**
     * Database Column Remarks:
     *   关联类型： 1=订单匹配 2=手工关联 3=无关联订单
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.relate_type")
    private Integer relateType;

    /**
     * Database Column Remarks:
     *   关联订单备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.order_remark")
    private String orderRemark;

    /**
     * Database Column Remarks:
     *   违章联系人（长租订单）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.violation_name")
    private String violationName;

    /**
     * Database Column Remarks:
     *   违章联系人联系方式（长租订单）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.violation_tel_no")
    private String violationTelNo;

    /**
     * Database Column Remarks:
     *   是否客户承担维修费	1是 2否（长租订单）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_client_maintenance_fee")
    private Byte vehicleClientMaintenanceFee;

    /**
     * Database Column Remarks:
     *   是否客户承担年检费 1=是 2=否（长租订单）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.client_inspect_tag")
    private Byte clientInspectTag;

    /**
     * Database Column Remarks:
     *   是否客户承担保养费 1=是 2=否（长租订单）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.client_upkeep_tag")
    private Byte clientUpkeepTag;

    /**
     * Database Column Remarks:
     *   会员id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.auth_id")
    private String authId;

    /**
     * Database Column Remarks:
     *   是否购买不计免赔 0:否 1:是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.no_deductibles_flag")
    private Integer noDeductiblesFlag;

    /**
     * Database Column Remarks:
     *   服务类别 1-基础服务 2-优享服务 3-尊享服务 （门店订单）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.service_type")
    private Integer serviceType;

    /**
     * Database Column Remarks:
     *   保障内容（门店订单）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.service_content")
    private String serviceContent;

    /**
     * Database Column Remarks:
     *   驾驶员姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.driver_name")
    private String driverName;

    /**
     * Database Column Remarks:
     *   驾驶员手机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.driver_tel")
    private String driverTel;

    /**
     * Database Column Remarks:
     *   巡检姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.routing_inspection_name")
    private String routingInspectionName;

    /**
     * Database Column Remarks:
     *   巡检手机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.routing_inspection_tel")
    private String routingInspectionTel;

    /**
     * Database Column Remarks:
     *   受损部位描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.damaged_part_describe")
    private String damagedPartDescribe;

    /**
     * Database Column Remarks:
     *   事故描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.accident_describe")
    private String accidentDescribe;

    /**
     * Database Column Remarks:
     *   是否有拖车 0:否 1:是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.trailer_flag")
    private Short trailerFlag;

    /**
     * Database Column Remarks:
     *   进保预审金额合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_review_total_amount")
    private BigDecimal repairReviewTotalAmount;

    /**
     * Database Column Remarks:
     *   修理厂换件金额合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_replace_total_amount")
    private BigDecimal repairReplaceTotalAmount;

    /**
     * Database Column Remarks:
     *   修理厂修理金额合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_repair_total_amount")
    private BigDecimal repairRepairTotalAmount;

    /**
     * Database Column Remarks:
     *   修理厂定损总计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_insurance_total_amount")
    private BigDecimal repairInsuranceTotalAmount;

    /**
     * Database Column Remarks:
     *   车管换件金额合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_replace_total_amount")
    private BigDecimal vehicleReplaceTotalAmount;

    /**
     * Database Column Remarks:
     *   车管修理金额合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_repair_total_amount")
    private BigDecimal vehicleRepairTotalAmount;

    /**
     * Database Column Remarks:
     *   车管定损总计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_insurance_total_amount")
    private BigDecimal vehicleInsuranceTotalAmount;

    /**
     * Database Column Remarks:
     *   车管核价意见 0:同意报价 1:异议
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_manage_view_flag")
    private Short vehicleManageViewFlag;

    /**
     * Database Column Remarks:
     *   是否需要复勘 0:否 1:是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.resurvey_flag")
    private Short resurveyFlag;

    /**
     * Database Column Remarks:
     *   复勘部位
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.resurvey_part")
    private String resurveyPart;

    /**
     * Database Column Remarks:
     *   是否需要向用户追偿 0:否 1:是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.recovery_flag")
    private Short recoveryFlag;

    /**
     * Database Column Remarks:
     *   责任情况 1:全责 2:主责 3:次责 4:平责 5:无责
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.duty_situation")
    private Integer dutySituation;

    /**
     * Database Column Remarks:
     *   向用户追偿费用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.recovery_amount")
    private BigDecimal recoveryAmount;

    /**
     * Database Column Remarks:
     *   保险上付费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_amount")
    private BigDecimal insuranceAmount;

    /**
     * Database Column Remarks:
     *   加速折旧费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.acc_dep_amount")
    private BigDecimal accDepAmount;

    /**
     * Database Column Remarks:
     *   停运损失费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.outage_loss_amount")
    private BigDecimal outageLossAmount;

    /**
     * Database Column Remarks:
     *   车辆损失费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_loss_amount")
    private BigDecimal vehicleLossAmount;

    /**
     * Database Column Remarks:
     *   拖车救援费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.trailer_rescue_amount")
    private BigDecimal trailerRescueAmount;

    /**
     * Database Column Remarks:
     *   保养费用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.maintain_amount")
    private BigDecimal maintainAmount;

    /**
     * Database Column Remarks:
     *   定损单金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.loss_order_amount")
    private BigDecimal lossOrderAmount;

    /**
     * Database Column Remarks:
     *   改派修理厂组织机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.reassignment_repair_org_id")
    private String reassignmentRepairOrgId;

    /**
     * Database Column Remarks:
     *   改派原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.reassignment_reasons")
    private String reassignmentReasons;

    /**
     * Database Column Remarks:
     *   改派驳回原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.reassignment_reject_reasons")
    private String reassignmentRejectReasons;

    /**
     * Database Column Remarks:
     *   核损驳回原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_reject_reasons")
    private String verificationRejectReasons;

    /**
     * Database Column Remarks:
     *   核损驳回原因详情
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_reject_reasons_detail")
    private String verificationRejectReasonsDetail;

    /**
     * Database Column Remarks:
     *   核损驳回时等级
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_reject_level")
    private String verificationRejectLevel;

    /**
     * Database Column Remarks:
     *   车辆验收结果 0:验收通过 1:验收不通过
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.check_result_flag")
    private Short checkResultFlag;

    /**
     * Database Column Remarks:
     *   验收不合格原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.check_unqualified_reason")
    private String checkUnqualifiedReason;

    /**
     * Database Column Remarks:
     *   自费和进保任务确认类型（1公司自费2客户付费3我方有责4我方无责）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.confirm_type")
    private Integer confirmType;

    /**
     * Database Column Remarks:
     *   保养转维修标志 -1:待选择0:否 1:是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.maintain_to_repair_flag")
    private Short maintainToRepairFlag;

    /**
     * Database Column Remarks:
     *   核损核价任务占有人ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_loss_task_oper_id")
    private Long verificationLossTaskOperId;

    /**
     * Database Column Remarks:
     *   是否提交上级 0:未提交 1:已提交
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.examine_level")
    private Short examineLevel;

    /**
     * Database Column Remarks:
     *   核损通过时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_loss_check_time")
    private Date verificationLossCheckTime;

    /**
     * Database Column Remarks:
     *   一级业务处理人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_loss_check_id")
    private Long verificationLossCheckId;

    /**
     * Database Column Remarks:
     *   超时原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.over_time_reasons")
    private String overTimeReasons;

    /**
     * Database Column Remarks:
     *   首次报价金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_total_amount_first")
    private BigDecimal repairTotalAmountFirst;

    /**
     * Database Column Remarks:
     *   是否驳回标识
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.nuclear_loss_reversion_flag")
    private Integer nuclearLossReversionFlag;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.remark")
    private String remark;

    /**
     * Database Column Remarks:
     *   轮胎数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.tireNumber")
    private Long tirenumber;

    /**
     * Database Column Remarks:
     *   轮胎单价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.tire_unit_price")
    private BigDecimal tireUnitPrice;

    /**
     * Database Column Remarks:
     *   轮胎品牌
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.tire_brand")
    private String tireBrand;

    /**
     * Database Column Remarks:
     *   关联任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.associated_task_no")
    private String associatedTaskNo;

    /**
     * Database Column Remarks:
     *   是否进保（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_flag")
    private Integer insuranceFlag;

    /**
     * Database Column Remarks:
     *   二级审核级别 2:待运营经理审核 3:待资产管理部审核 4:待运营管理部审核 5:资产管理已退回 6:运营管理部已退回
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.advanced_audit_leve")
    private Integer advancedAuditLeve;

    /**
     * Database Column Remarks:
     *   已来款金额(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.already_incoming_amount")
    private BigDecimal alreadyIncomingAmount;

    /**
     * Database Column Remarks:
     *   已支付金额(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.already_pay_amount")
    private BigDecimal alreadyPayAmount;

    /**
     * Database Column Remarks:
     *   待支付金额(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.wait_pay_amount")
    private BigDecimal waitPayAmount;

    /**
     * Database Column Remarks:
     *   应理赔金额总计(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.must_claim_amount")
    private BigDecimal mustClaimAmount;

    /**
     * Database Column Remarks:
     *   应支付金额总计(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.must_pay_amount")
    private BigDecimal mustPayAmount;

    /**
     * Database Column Remarks:
     *   维修成本金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_repair_cost_amount")
    private Double vehicleRepairCostAmount;

    /**
     * Database Column Remarks:
     *   结算金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_settle_amount")
    private Double repairSettleAmount;

    /**
     * Database Column Remarks:
     *   税前结算金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_fax_settle_amount")
    private Double repairFaxSettleAmount;

    /**
     * Database Column Remarks:
     *   确定转为收入(0：未确认 1：已确认)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.transfer_flag")
    private Integer transferFlag;

    /**
     * Database Column Remarks:
     *   理赔款关联计数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.rational_indemnity_cnt")
    private Integer rationalIndemnityCnt;

    /**
     * Database Column Remarks:
     *   事故发生时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.accident_day_time")
    private Date accidentDayTime;

    /**
     * Database Column Remarks:
     *   免赔结算状态(0:无 1:未结算 2:已结算)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.deduct_flag")
    private Integer deductFlag;

    /**
     * Database Column Remarks:
     *   任务来源（0：调度系统 1：车管系统）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.origin")
    private Integer origin;

    /**
     * Database Column Remarks:
     *   车辆业务状态（-1：未出库 0：分时租赁 1：长租 3：短租 4：公务用车）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.renttype")
    private Integer renttype;

    /**
     * Database Column Remarks:
     *   实际运营标签 0.未投车辆 1.短租运营车辆 2.长租运营车辆 3.备库车辆 4.待退运车辆 5.已处置车辆 6.特殊车辆
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.fact_operate_tag")
    private Integer factOperateTag;

    /**
     * Database Column Remarks:
     *   提车人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.take_user_name")
    private String takeUserName;

    /**
     * Database Column Remarks:
     *   提车人手机
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.take_user_phone")
    private String takeUserPhone;

    /**
     * Database Column Remarks:
     *   提车凭证
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.take_voucher")
    private String takeVoucher;

    /**
     * Database Column Remarks:
     *   提车信息同步时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.syn_take_time")
    private Date synTakeTime;

    /**
     * Database Column Remarks:
     *   关闭原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.close_reason")
    private String closeReason;

    /**
     * Database Column Remarks:
     *   是否保险理赔状态：0否 1是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.claims_flag")
    private Integer claimsFlag;

    /**
     * Database Column Remarks:
     *   预估理赔金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.estimated_claim_amount")
    private BigDecimal estimatedClaimAmount;

    /**
     * Database Column Remarks:
     *   车损类型 1 人为损坏 2 自然损耗
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.car_damage_type")
    private Integer carDamageType;

    /**
     * Database Column Remarks:
     *   车辆处置残值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_scrape_value")
    private BigDecimal vehicleScrapeValue;

    /**
     * Database Column Remarks:
     *   进保预审转自费状态：0否 1是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.review_to_sel_fee_flag")
    private Integer reviewToSelFeeFlag;

    /**
     * Database Column Remarks:
     *   确认车损类型 1：我司车辆原因 2：客户原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.confirm_car_damage_type")
    private Integer confirmCarDamageType;

    /**
     * Database Column Remarks:
     *   事故理赔任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.accident_no")
    private String accidentNo;

    /**
     * Database Column Remarks:
     *   进保预审通过时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_pre_review_time")
    private Date insurancePreReviewTime;

    /**
     * Database Column Remarks:
     *   进保预审阶段
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_pre_review_level")
    private Integer insurancePreReviewLevel;

    /**
     * Database Column Remarks:
     *   预审车辆处置残值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.pre_review_vehicle_scrape_value")
    private BigDecimal preReviewVehicleScrapeValue;

    /**
     * Database Column Remarks:
     *   预审车辆处置残值时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.pre_review_vehicle_scrape_time")
    private Date preReviewVehicleScrapeTime;

    /**
     * Database Column Remarks:
     *   是否使用维修小程序 0-否 1-是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.is_used_applets")
    private String isUsedApplets;

    /**
     * Database Column Remarks:
     *   标准id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.criteria_id")
    private Long criteriaId;

    /**
     * Database Column Remarks:
     *   是否客户直付 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.cust_pays_direct")
    private Integer custPaysDirect;

    /**
     * Database Column Remarks:
     *   客户直付金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.cust_amount")
    private BigDecimal custAmount;

    /**
     * Database Column Remarks:
     *   用户承担金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.user_assumed_amount")
    private BigDecimal userAssumedAmount;

    /**
     * Database Column Remarks:
     *   非用户承担金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.not_user_assumed_amount")
    private BigDecimal notUserAssumedAmount;

    /**
     * Database Column Remarks:
     *   是否同步业财理赔金额 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sync_claim_flag")
    private Integer syncClaimFlag;

    /**
     * Database Column Remarks:
     *   税率
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.tax_rate")
    private String taxRate;

    /**
     * Database Column Remarks:
     *   修理厂类型(1:合作修理厂 2:非合作修理厂)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_type")
    private Integer repairDepotType;

    /**
     * Database Column Remarks:
     *   关联账单(PO)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.declare_no")
    private String declareNo;

    /**
     * Database Column Remarks:
     *   申报状态 1=未提交 2=审批中 3=审批通过 4=审批拒绝
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.declare_status")
    private Integer declareStatus;

    /**
     * Database Column Remarks:
     *   结算状态 1=已生成 2=待结算 3=结算中 4=已结算 5=已作废
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.declare_settlement_status")
    private Integer declareSettlementStatus;

    /**
     * Database Column Remarks:
     *   关联结算单(GR)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.settlement_no")
    private String settlementNo;

    /**
     * Database Column Remarks:
     *   关联结算单状态 1=未提交 2=审核中 3=审批通过 4=审批拒绝 5=已作废 6=已关闭
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.settlement_status")
    private Integer settlementStatus;

    /**
     * Database Column Remarks:
     *   送修时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.send_repair_time")
    private Date sendRepairTime;

    /**
     * Database Column Remarks:
     *   情况描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.situation_desc")
    private String situationDesc;

    /**
     * Database Column Remarks:
     *   资产状态: 0=在建工程, 1=固定资产, 2=固定资产（待报废）, 3=报废, 4=固定资产(待处置), 5=固定资产(已处置), 6=以租代售, 7=库存商品, 8=已处置（未过户）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.property_status")
    private Integer propertyStatus;

    /**
     * Database Column Remarks:
     *   产品线: 1=车管中心, 2=长租, 3=短租, 4=公务用车
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.product_line")
    private Integer productLine;

    /**
     * Database Column Remarks:
     *   子产品线: 1=携程短租-短租, 2=门店-短租, 3=分时-短租, 4=普通-长租, 5=时行-长租, 6=平台业务-长租, 7=政企业务-长租, 8=网约车业务-长租
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sub_product_line")
    private Integer subProductLine;

    /**
     * Database Column Remarks:
     *   自费金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.self_funded_amount")
    private BigDecimal selfFundedAmount;

    /**
     * Database Column Remarks:
     *   连续停放天数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.continue_days")
    private String continueDays;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.created_time")
    private Date createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.updated_time")
    private Date updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.task_no")
    public String getTaskNo() {
        return taskNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.task_no")
    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo == null ? null : taskNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.task_type")
    public Short getTaskType() {
        return taskType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.task_type")
    public void setTaskType(Short taskType) {
        this.taskType = taskType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.org_id")
    public String getOrgId() {
        return orgId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.org_id")
    public void setOrgId(String orgId) {
        this.orgId = orgId == null ? null : orgId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.org_name")
    public String getOrgName() {
        return orgName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.org_name")
    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.operate_org_id")
    public String getOperateOrgId() {
        return operateOrgId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.operate_org_id")
    public void setOperateOrgId(String operateOrgId) {
        this.operateOrgId = operateOrgId == null ? null : operateOrgId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.operate_org_name")
    public String getOperateOrgName() {
        return operateOrgName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.operate_org_name")
    public void setOperateOrgName(String operateOrgName) {
        this.operateOrgName = operateOrgName == null ? null : operateOrgName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_no")
    public String getVehicleNo() {
        return vehicleNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_no")
    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo == null ? null : vehicleNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_model_seq")
    public Long getVehicleModelSeq() {
        return vehicleModelSeq;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_model_seq")
    public void setVehicleModelSeq(Long vehicleModelSeq) {
        this.vehicleModelSeq = vehicleModelSeq;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_model_info")
    public String getVehicleModelInfo() {
        return vehicleModelInfo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_model_info")
    public void setVehicleModelInfo(String vehicleModelInfo) {
        this.vehicleModelInfo = vehicleModelInfo == null ? null : vehicleModelInfo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vin")
    public String getVin() {
        return vin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vin")
    public void setVin(String vin) {
        this.vin = vin == null ? null : vin.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_company_name")
    public String getInsuranceCompanyName() {
        return insuranceCompanyName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_company_name")
    public void setInsuranceCompanyName(String insuranceCompanyName) {
        this.insuranceCompanyName = insuranceCompanyName == null ? null : insuranceCompanyName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_type_id")
    public Integer getRepairTypeId() {
        return repairTypeId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_type_id")
    public void setRepairTypeId(Integer repairTypeId) {
        this.repairTypeId = repairTypeId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_type_name")
    public String getRepairTypeName() {
        return repairTypeName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_type_name")
    public void setRepairTypeName(String repairTypeName) {
        this.repairTypeName = repairTypeName == null ? null : repairTypeName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_grade")
    public String getRepairGrade() {
        return repairGrade;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_grade")
    public void setRepairGrade(String repairGrade) {
        this.repairGrade = repairGrade == null ? null : repairGrade.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_id")
    public String getRepairDepotId() {
        return repairDepotId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_id")
    public void setRepairDepotId(String repairDepotId) {
        this.repairDepotId = repairDepotId == null ? null : repairDepotId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_name")
    public String getRepairDepotName() {
        return repairDepotName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_name")
    public void setRepairDepotName(String repairDepotName) {
        this.repairDepotName = repairDepotName == null ? null : repairDepotName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_org_id")
    public String getRepairDepotOrgId() {
        return repairDepotOrgId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_org_id")
    public void setRepairDepotOrgId(String repairDepotOrgId) {
        this.repairDepotOrgId = repairDepotOrgId == null ? null : repairDepotOrgId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_sap_code")
    public String getRepairDepotSapCode() {
        return repairDepotSapCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_sap_code")
    public void setRepairDepotSapCode(String repairDepotSapCode) {
        this.repairDepotSapCode = repairDepotSapCode == null ? null : repairDepotSapCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sap_send_status")
    public Integer getSapSendStatus() {
        return sapSendStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sap_send_status")
    public void setSapSendStatus(Integer sapSendStatus) {
        this.sapSendStatus = sapSendStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sap_sync_flag")
    public Integer getSapSyncFlag() {
        return sapSyncFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sap_sync_flag")
    public void setSapSyncFlag(Integer sapSyncFlag) {
        this.sapSyncFlag = sapSyncFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sap_success_time")
    public Date getSapSuccessTime() {
        return sapSuccessTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sap_success_time")
    public void setSapSuccessTime(Date sapSuccessTime) {
        this.sapSuccessTime = sapSuccessTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.parts_library_type")
    public Integer getPartsLibraryType() {
        return partsLibraryType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.parts_library_type")
    public void setPartsLibraryType(Integer partsLibraryType) {
        this.partsLibraryType = partsLibraryType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.task_create_time")
    public Date getTaskCreateTime() {
        return taskCreateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.task_create_time")
    public void setTaskCreateTime(Date taskCreateTime) {
        this.taskCreateTime = taskCreateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.task_inflow_time")
    public Date getTaskInflowTime() {
        return taskInflowTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.task_inflow_time")
    public void setTaskInflowTime(Date taskInflowTime) {
        this.taskInflowTime = taskInflowTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_recive_time")
    public Date getVehicleReciveTime() {
        return vehicleReciveTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_recive_time")
    public void setVehicleReciveTime(Date vehicleReciveTime) {
        this.vehicleReciveTime = vehicleReciveTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_check_time")
    public Date getVehicleCheckTime() {
        return vehicleCheckTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_check_time")
    public void setVehicleCheckTime(Date vehicleCheckTime) {
        this.vehicleCheckTime = vehicleCheckTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_repair_time")
    public Date getVehicleRepairTime() {
        return vehicleRepairTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_repair_time")
    public void setVehicleRepairTime(Date vehicleRepairTime) {
        this.vehicleRepairTime = vehicleRepairTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.reassignment_pass_time")
    public Date getReassignmentPassTime() {
        return reassignmentPassTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.reassignment_pass_time")
    public void setReassignmentPassTime(Date reassignmentPassTime) {
        this.reassignmentPassTime = reassignmentPassTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.settle_closing_time")
    public Date getSettleClosingTime() {
        return settleClosingTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.settle_closing_time")
    public void setSettleClosingTime(Date settleClosingTime) {
        this.settleClosingTime = settleClosingTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.expected_repair_days")
    public Long getExpectedRepairDays() {
        return expectedRepairDays;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.expected_repair_days")
    public void setExpectedRepairDays(Long expectedRepairDays) {
        this.expectedRepairDays = expectedRepairDays;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.expected_repair_complete")
    public Date getExpectedRepairComplete() {
        return expectedRepairComplete;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.expected_repair_complete")
    public void setExpectedRepairComplete(Date expectedRepairComplete) {
        this.expectedRepairComplete = expectedRepairComplete;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.accident_report_number")
    public String getAccidentReportNumber() {
        return accidentReportNumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.accident_report_number")
    public void setAccidentReportNumber(String accidentReportNumber) {
        this.accidentReportNumber = accidentReportNumber == null ? null : accidentReportNumber.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_flag")
    public Short getRepairFlag() {
        return repairFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_flag")
    public void setRepairFlag(Short repairFlag) {
        this.repairFlag = repairFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.terminal_id")
    public String getTerminalId() {
        return terminalId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.terminal_id")
    public void setTerminalId(String terminalId) {
        this.terminalId = terminalId == null ? null : terminalId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.total_mileage")
    public BigDecimal getTotalMileage() {
        return totalMileage;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.total_mileage")
    public void setTotalMileage(BigDecimal totalMileage) {
        this.totalMileage = totalMileage;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.terminal_mileage")
    public String getTerminalMileage() {
        return terminalMileage;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.terminal_mileage")
    public void setTerminalMileage(String terminalMileage) {
        this.terminalMileage = terminalMileage == null ? null : terminalMileage.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.associated_order")
    public String getAssociatedOrder() {
        return associatedOrder;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.associated_order")
    public void setAssociatedOrder(String associatedOrder) {
        this.associatedOrder = associatedOrder == null ? null : associatedOrder.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.order_type")
    public Integer getOrderType() {
        return orderType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.order_type")
    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.relate_type")
    public Integer getRelateType() {
        return relateType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.relate_type")
    public void setRelateType(Integer relateType) {
        this.relateType = relateType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.order_remark")
    public String getOrderRemark() {
        return orderRemark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.order_remark")
    public void setOrderRemark(String orderRemark) {
        this.orderRemark = orderRemark == null ? null : orderRemark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.violation_name")
    public String getViolationName() {
        return violationName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.violation_name")
    public void setViolationName(String violationName) {
        this.violationName = violationName == null ? null : violationName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.violation_tel_no")
    public String getViolationTelNo() {
        return violationTelNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.violation_tel_no")
    public void setViolationTelNo(String violationTelNo) {
        this.violationTelNo = violationTelNo == null ? null : violationTelNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_client_maintenance_fee")
    public Byte getVehicleClientMaintenanceFee() {
        return vehicleClientMaintenanceFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_client_maintenance_fee")
    public void setVehicleClientMaintenanceFee(Byte vehicleClientMaintenanceFee) {
        this.vehicleClientMaintenanceFee = vehicleClientMaintenanceFee;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.client_inspect_tag")
    public Byte getClientInspectTag() {
        return clientInspectTag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.client_inspect_tag")
    public void setClientInspectTag(Byte clientInspectTag) {
        this.clientInspectTag = clientInspectTag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.client_upkeep_tag")
    public Byte getClientUpkeepTag() {
        return clientUpkeepTag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.client_upkeep_tag")
    public void setClientUpkeepTag(Byte clientUpkeepTag) {
        this.clientUpkeepTag = clientUpkeepTag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.auth_id")
    public String getAuthId() {
        return authId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.auth_id")
    public void setAuthId(String authId) {
        this.authId = authId == null ? null : authId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.no_deductibles_flag")
    public Integer getNoDeductiblesFlag() {
        return noDeductiblesFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.no_deductibles_flag")
    public void setNoDeductiblesFlag(Integer noDeductiblesFlag) {
        this.noDeductiblesFlag = noDeductiblesFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.service_type")
    public Integer getServiceType() {
        return serviceType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.service_type")
    public void setServiceType(Integer serviceType) {
        this.serviceType = serviceType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.service_content")
    public String getServiceContent() {
        return serviceContent;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.service_content")
    public void setServiceContent(String serviceContent) {
        this.serviceContent = serviceContent == null ? null : serviceContent.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.driver_name")
    public String getDriverName() {
        return driverName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.driver_name")
    public void setDriverName(String driverName) {
        this.driverName = driverName == null ? null : driverName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.driver_tel")
    public String getDriverTel() {
        return driverTel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.driver_tel")
    public void setDriverTel(String driverTel) {
        this.driverTel = driverTel == null ? null : driverTel.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.routing_inspection_name")
    public String getRoutingInspectionName() {
        return routingInspectionName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.routing_inspection_name")
    public void setRoutingInspectionName(String routingInspectionName) {
        this.routingInspectionName = routingInspectionName == null ? null : routingInspectionName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.routing_inspection_tel")
    public String getRoutingInspectionTel() {
        return routingInspectionTel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.routing_inspection_tel")
    public void setRoutingInspectionTel(String routingInspectionTel) {
        this.routingInspectionTel = routingInspectionTel == null ? null : routingInspectionTel.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.damaged_part_describe")
    public String getDamagedPartDescribe() {
        return damagedPartDescribe;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.damaged_part_describe")
    public void setDamagedPartDescribe(String damagedPartDescribe) {
        this.damagedPartDescribe = damagedPartDescribe == null ? null : damagedPartDescribe.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.accident_describe")
    public String getAccidentDescribe() {
        return accidentDescribe;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.accident_describe")
    public void setAccidentDescribe(String accidentDescribe) {
        this.accidentDescribe = accidentDescribe == null ? null : accidentDescribe.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.trailer_flag")
    public Short getTrailerFlag() {
        return trailerFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.trailer_flag")
    public void setTrailerFlag(Short trailerFlag) {
        this.trailerFlag = trailerFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_review_total_amount")
    public BigDecimal getRepairReviewTotalAmount() {
        return repairReviewTotalAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_review_total_amount")
    public void setRepairReviewTotalAmount(BigDecimal repairReviewTotalAmount) {
        this.repairReviewTotalAmount = repairReviewTotalAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_replace_total_amount")
    public BigDecimal getRepairReplaceTotalAmount() {
        return repairReplaceTotalAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_replace_total_amount")
    public void setRepairReplaceTotalAmount(BigDecimal repairReplaceTotalAmount) {
        this.repairReplaceTotalAmount = repairReplaceTotalAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_repair_total_amount")
    public BigDecimal getRepairRepairTotalAmount() {
        return repairRepairTotalAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_repair_total_amount")
    public void setRepairRepairTotalAmount(BigDecimal repairRepairTotalAmount) {
        this.repairRepairTotalAmount = repairRepairTotalAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_insurance_total_amount")
    public BigDecimal getRepairInsuranceTotalAmount() {
        return repairInsuranceTotalAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_insurance_total_amount")
    public void setRepairInsuranceTotalAmount(BigDecimal repairInsuranceTotalAmount) {
        this.repairInsuranceTotalAmount = repairInsuranceTotalAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_replace_total_amount")
    public BigDecimal getVehicleReplaceTotalAmount() {
        return vehicleReplaceTotalAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_replace_total_amount")
    public void setVehicleReplaceTotalAmount(BigDecimal vehicleReplaceTotalAmount) {
        this.vehicleReplaceTotalAmount = vehicleReplaceTotalAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_repair_total_amount")
    public BigDecimal getVehicleRepairTotalAmount() {
        return vehicleRepairTotalAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_repair_total_amount")
    public void setVehicleRepairTotalAmount(BigDecimal vehicleRepairTotalAmount) {
        this.vehicleRepairTotalAmount = vehicleRepairTotalAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_insurance_total_amount")
    public BigDecimal getVehicleInsuranceTotalAmount() {
        return vehicleInsuranceTotalAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_insurance_total_amount")
    public void setVehicleInsuranceTotalAmount(BigDecimal vehicleInsuranceTotalAmount) {
        this.vehicleInsuranceTotalAmount = vehicleInsuranceTotalAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_manage_view_flag")
    public Short getVehicleManageViewFlag() {
        return vehicleManageViewFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_manage_view_flag")
    public void setVehicleManageViewFlag(Short vehicleManageViewFlag) {
        this.vehicleManageViewFlag = vehicleManageViewFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.resurvey_flag")
    public Short getResurveyFlag() {
        return resurveyFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.resurvey_flag")
    public void setResurveyFlag(Short resurveyFlag) {
        this.resurveyFlag = resurveyFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.resurvey_part")
    public String getResurveyPart() {
        return resurveyPart;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.resurvey_part")
    public void setResurveyPart(String resurveyPart) {
        this.resurveyPart = resurveyPart == null ? null : resurveyPart.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.recovery_flag")
    public Short getRecoveryFlag() {
        return recoveryFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.recovery_flag")
    public void setRecoveryFlag(Short recoveryFlag) {
        this.recoveryFlag = recoveryFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.duty_situation")
    public Integer getDutySituation() {
        return dutySituation;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.duty_situation")
    public void setDutySituation(Integer dutySituation) {
        this.dutySituation = dutySituation;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.recovery_amount")
    public BigDecimal getRecoveryAmount() {
        return recoveryAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.recovery_amount")
    public void setRecoveryAmount(BigDecimal recoveryAmount) {
        this.recoveryAmount = recoveryAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_amount")
    public BigDecimal getInsuranceAmount() {
        return insuranceAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_amount")
    public void setInsuranceAmount(BigDecimal insuranceAmount) {
        this.insuranceAmount = insuranceAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.acc_dep_amount")
    public BigDecimal getAccDepAmount() {
        return accDepAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.acc_dep_amount")
    public void setAccDepAmount(BigDecimal accDepAmount) {
        this.accDepAmount = accDepAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.outage_loss_amount")
    public BigDecimal getOutageLossAmount() {
        return outageLossAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.outage_loss_amount")
    public void setOutageLossAmount(BigDecimal outageLossAmount) {
        this.outageLossAmount = outageLossAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_loss_amount")
    public BigDecimal getVehicleLossAmount() {
        return vehicleLossAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_loss_amount")
    public void setVehicleLossAmount(BigDecimal vehicleLossAmount) {
        this.vehicleLossAmount = vehicleLossAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.trailer_rescue_amount")
    public BigDecimal getTrailerRescueAmount() {
        return trailerRescueAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.trailer_rescue_amount")
    public void setTrailerRescueAmount(BigDecimal trailerRescueAmount) {
        this.trailerRescueAmount = trailerRescueAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.maintain_amount")
    public BigDecimal getMaintainAmount() {
        return maintainAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.maintain_amount")
    public void setMaintainAmount(BigDecimal maintainAmount) {
        this.maintainAmount = maintainAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.loss_order_amount")
    public BigDecimal getLossOrderAmount() {
        return lossOrderAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.loss_order_amount")
    public void setLossOrderAmount(BigDecimal lossOrderAmount) {
        this.lossOrderAmount = lossOrderAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.reassignment_repair_org_id")
    public String getReassignmentRepairOrgId() {
        return reassignmentRepairOrgId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.reassignment_repair_org_id")
    public void setReassignmentRepairOrgId(String reassignmentRepairOrgId) {
        this.reassignmentRepairOrgId = reassignmentRepairOrgId == null ? null : reassignmentRepairOrgId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.reassignment_reasons")
    public String getReassignmentReasons() {
        return reassignmentReasons;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.reassignment_reasons")
    public void setReassignmentReasons(String reassignmentReasons) {
        this.reassignmentReasons = reassignmentReasons == null ? null : reassignmentReasons.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.reassignment_reject_reasons")
    public String getReassignmentRejectReasons() {
        return reassignmentRejectReasons;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.reassignment_reject_reasons")
    public void setReassignmentRejectReasons(String reassignmentRejectReasons) {
        this.reassignmentRejectReasons = reassignmentRejectReasons == null ? null : reassignmentRejectReasons.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_reject_reasons")
    public String getVerificationRejectReasons() {
        return verificationRejectReasons;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_reject_reasons")
    public void setVerificationRejectReasons(String verificationRejectReasons) {
        this.verificationRejectReasons = verificationRejectReasons == null ? null : verificationRejectReasons.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_reject_reasons_detail")
    public String getVerificationRejectReasonsDetail() {
        return verificationRejectReasonsDetail;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_reject_reasons_detail")
    public void setVerificationRejectReasonsDetail(String verificationRejectReasonsDetail) {
        this.verificationRejectReasonsDetail = verificationRejectReasonsDetail == null ? null : verificationRejectReasonsDetail.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_reject_level")
    public String getVerificationRejectLevel() {
        return verificationRejectLevel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_reject_level")
    public void setVerificationRejectLevel(String verificationRejectLevel) {
        this.verificationRejectLevel = verificationRejectLevel == null ? null : verificationRejectLevel.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.check_result_flag")
    public Short getCheckResultFlag() {
        return checkResultFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.check_result_flag")
    public void setCheckResultFlag(Short checkResultFlag) {
        this.checkResultFlag = checkResultFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.check_unqualified_reason")
    public String getCheckUnqualifiedReason() {
        return checkUnqualifiedReason;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.check_unqualified_reason")
    public void setCheckUnqualifiedReason(String checkUnqualifiedReason) {
        this.checkUnqualifiedReason = checkUnqualifiedReason == null ? null : checkUnqualifiedReason.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.confirm_type")
    public Integer getConfirmType() {
        return confirmType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.confirm_type")
    public void setConfirmType(Integer confirmType) {
        this.confirmType = confirmType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.maintain_to_repair_flag")
    public Short getMaintainToRepairFlag() {
        return maintainToRepairFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.maintain_to_repair_flag")
    public void setMaintainToRepairFlag(Short maintainToRepairFlag) {
        this.maintainToRepairFlag = maintainToRepairFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_loss_task_oper_id")
    public Long getVerificationLossTaskOperId() {
        return verificationLossTaskOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_loss_task_oper_id")
    public void setVerificationLossTaskOperId(Long verificationLossTaskOperId) {
        this.verificationLossTaskOperId = verificationLossTaskOperId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.examine_level")
    public Short getExamineLevel() {
        return examineLevel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.examine_level")
    public void setExamineLevel(Short examineLevel) {
        this.examineLevel = examineLevel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_loss_check_time")
    public Date getVerificationLossCheckTime() {
        return verificationLossCheckTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_loss_check_time")
    public void setVerificationLossCheckTime(Date verificationLossCheckTime) {
        this.verificationLossCheckTime = verificationLossCheckTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_loss_check_id")
    public Long getVerificationLossCheckId() {
        return verificationLossCheckId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_loss_check_id")
    public void setVerificationLossCheckId(Long verificationLossCheckId) {
        this.verificationLossCheckId = verificationLossCheckId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.over_time_reasons")
    public String getOverTimeReasons() {
        return overTimeReasons;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.over_time_reasons")
    public void setOverTimeReasons(String overTimeReasons) {
        this.overTimeReasons = overTimeReasons == null ? null : overTimeReasons.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_total_amount_first")
    public BigDecimal getRepairTotalAmountFirst() {
        return repairTotalAmountFirst;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_total_amount_first")
    public void setRepairTotalAmountFirst(BigDecimal repairTotalAmountFirst) {
        this.repairTotalAmountFirst = repairTotalAmountFirst;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.nuclear_loss_reversion_flag")
    public Integer getNuclearLossReversionFlag() {
        return nuclearLossReversionFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.nuclear_loss_reversion_flag")
    public void setNuclearLossReversionFlag(Integer nuclearLossReversionFlag) {
        this.nuclearLossReversionFlag = nuclearLossReversionFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.remark")
    public String getRemark() {
        return remark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.remark")
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.tireNumber")
    public Long getTirenumber() {
        return tirenumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.tireNumber")
    public void setTirenumber(Long tirenumber) {
        this.tirenumber = tirenumber;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.tire_unit_price")
    public BigDecimal getTireUnitPrice() {
        return tireUnitPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.tire_unit_price")
    public void setTireUnitPrice(BigDecimal tireUnitPrice) {
        this.tireUnitPrice = tireUnitPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.tire_brand")
    public String getTireBrand() {
        return tireBrand;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.tire_brand")
    public void setTireBrand(String tireBrand) {
        this.tireBrand = tireBrand == null ? null : tireBrand.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.associated_task_no")
    public String getAssociatedTaskNo() {
        return associatedTaskNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.associated_task_no")
    public void setAssociatedTaskNo(String associatedTaskNo) {
        this.associatedTaskNo = associatedTaskNo == null ? null : associatedTaskNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_flag")
    public Integer getInsuranceFlag() {
        return insuranceFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_flag")
    public void setInsuranceFlag(Integer insuranceFlag) {
        this.insuranceFlag = insuranceFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.advanced_audit_leve")
    public Integer getAdvancedAuditLeve() {
        return advancedAuditLeve;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.advanced_audit_leve")
    public void setAdvancedAuditLeve(Integer advancedAuditLeve) {
        this.advancedAuditLeve = advancedAuditLeve;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.already_incoming_amount")
    public BigDecimal getAlreadyIncomingAmount() {
        return alreadyIncomingAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.already_incoming_amount")
    public void setAlreadyIncomingAmount(BigDecimal alreadyIncomingAmount) {
        this.alreadyIncomingAmount = alreadyIncomingAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.already_pay_amount")
    public BigDecimal getAlreadyPayAmount() {
        return alreadyPayAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.already_pay_amount")
    public void setAlreadyPayAmount(BigDecimal alreadyPayAmount) {
        this.alreadyPayAmount = alreadyPayAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.wait_pay_amount")
    public BigDecimal getWaitPayAmount() {
        return waitPayAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.wait_pay_amount")
    public void setWaitPayAmount(BigDecimal waitPayAmount) {
        this.waitPayAmount = waitPayAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.must_claim_amount")
    public BigDecimal getMustClaimAmount() {
        return mustClaimAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.must_claim_amount")
    public void setMustClaimAmount(BigDecimal mustClaimAmount) {
        this.mustClaimAmount = mustClaimAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.must_pay_amount")
    public BigDecimal getMustPayAmount() {
        return mustPayAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.must_pay_amount")
    public void setMustPayAmount(BigDecimal mustPayAmount) {
        this.mustPayAmount = mustPayAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_repair_cost_amount")
    public Double getVehicleRepairCostAmount() {
        return vehicleRepairCostAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_repair_cost_amount")
    public void setVehicleRepairCostAmount(Double vehicleRepairCostAmount) {
        this.vehicleRepairCostAmount = vehicleRepairCostAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_settle_amount")
    public Double getRepairSettleAmount() {
        return repairSettleAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_settle_amount")
    public void setRepairSettleAmount(Double repairSettleAmount) {
        this.repairSettleAmount = repairSettleAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_fax_settle_amount")
    public Double getRepairFaxSettleAmount() {
        return repairFaxSettleAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_fax_settle_amount")
    public void setRepairFaxSettleAmount(Double repairFaxSettleAmount) {
        this.repairFaxSettleAmount = repairFaxSettleAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.transfer_flag")
    public Integer getTransferFlag() {
        return transferFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.transfer_flag")
    public void setTransferFlag(Integer transferFlag) {
        this.transferFlag = transferFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.rational_indemnity_cnt")
    public Integer getRationalIndemnityCnt() {
        return rationalIndemnityCnt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.rational_indemnity_cnt")
    public void setRationalIndemnityCnt(Integer rationalIndemnityCnt) {
        this.rationalIndemnityCnt = rationalIndemnityCnt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.accident_day_time")
    public Date getAccidentDayTime() {
        return accidentDayTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.accident_day_time")
    public void setAccidentDayTime(Date accidentDayTime) {
        this.accidentDayTime = accidentDayTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.deduct_flag")
    public Integer getDeductFlag() {
        return deductFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.deduct_flag")
    public void setDeductFlag(Integer deductFlag) {
        this.deductFlag = deductFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.origin")
    public Integer getOrigin() {
        return origin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.origin")
    public void setOrigin(Integer origin) {
        this.origin = origin;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.renttype")
    public Integer getRenttype() {
        return renttype;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.renttype")
    public void setRenttype(Integer renttype) {
        this.renttype = renttype;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.fact_operate_tag")
    public Integer getFactOperateTag() {
        return factOperateTag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.fact_operate_tag")
    public void setFactOperateTag(Integer factOperateTag) {
        this.factOperateTag = factOperateTag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.take_user_name")
    public String getTakeUserName() {
        return takeUserName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.take_user_name")
    public void setTakeUserName(String takeUserName) {
        this.takeUserName = takeUserName == null ? null : takeUserName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.take_user_phone")
    public String getTakeUserPhone() {
        return takeUserPhone;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.take_user_phone")
    public void setTakeUserPhone(String takeUserPhone) {
        this.takeUserPhone = takeUserPhone == null ? null : takeUserPhone.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.take_voucher")
    public String getTakeVoucher() {
        return takeVoucher;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.take_voucher")
    public void setTakeVoucher(String takeVoucher) {
        this.takeVoucher = takeVoucher == null ? null : takeVoucher.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.syn_take_time")
    public Date getSynTakeTime() {
        return synTakeTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.syn_take_time")
    public void setSynTakeTime(Date synTakeTime) {
        this.synTakeTime = synTakeTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.close_reason")
    public String getCloseReason() {
        return closeReason;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.close_reason")
    public void setCloseReason(String closeReason) {
        this.closeReason = closeReason == null ? null : closeReason.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.claims_flag")
    public Integer getClaimsFlag() {
        return claimsFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.claims_flag")
    public void setClaimsFlag(Integer claimsFlag) {
        this.claimsFlag = claimsFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.estimated_claim_amount")
    public BigDecimal getEstimatedClaimAmount() {
        return estimatedClaimAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.estimated_claim_amount")
    public void setEstimatedClaimAmount(BigDecimal estimatedClaimAmount) {
        this.estimatedClaimAmount = estimatedClaimAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.car_damage_type")
    public Integer getCarDamageType() {
        return carDamageType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.car_damage_type")
    public void setCarDamageType(Integer carDamageType) {
        this.carDamageType = carDamageType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_scrape_value")
    public BigDecimal getVehicleScrapeValue() {
        return vehicleScrapeValue;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_scrape_value")
    public void setVehicleScrapeValue(BigDecimal vehicleScrapeValue) {
        this.vehicleScrapeValue = vehicleScrapeValue;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.review_to_sel_fee_flag")
    public Integer getReviewToSelFeeFlag() {
        return reviewToSelFeeFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.review_to_sel_fee_flag")
    public void setReviewToSelFeeFlag(Integer reviewToSelFeeFlag) {
        this.reviewToSelFeeFlag = reviewToSelFeeFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.confirm_car_damage_type")
    public Integer getConfirmCarDamageType() {
        return confirmCarDamageType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.confirm_car_damage_type")
    public void setConfirmCarDamageType(Integer confirmCarDamageType) {
        this.confirmCarDamageType = confirmCarDamageType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.accident_no")
    public String getAccidentNo() {
        return accidentNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.accident_no")
    public void setAccidentNo(String accidentNo) {
        this.accidentNo = accidentNo == null ? null : accidentNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_pre_review_time")
    public Date getInsurancePreReviewTime() {
        return insurancePreReviewTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_pre_review_time")
    public void setInsurancePreReviewTime(Date insurancePreReviewTime) {
        this.insurancePreReviewTime = insurancePreReviewTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_pre_review_level")
    public Integer getInsurancePreReviewLevel() {
        return insurancePreReviewLevel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_pre_review_level")
    public void setInsurancePreReviewLevel(Integer insurancePreReviewLevel) {
        this.insurancePreReviewLevel = insurancePreReviewLevel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.pre_review_vehicle_scrape_value")
    public BigDecimal getPreReviewVehicleScrapeValue() {
        return preReviewVehicleScrapeValue;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.pre_review_vehicle_scrape_value")
    public void setPreReviewVehicleScrapeValue(BigDecimal preReviewVehicleScrapeValue) {
        this.preReviewVehicleScrapeValue = preReviewVehicleScrapeValue;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.pre_review_vehicle_scrape_time")
    public Date getPreReviewVehicleScrapeTime() {
        return preReviewVehicleScrapeTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.pre_review_vehicle_scrape_time")
    public void setPreReviewVehicleScrapeTime(Date preReviewVehicleScrapeTime) {
        this.preReviewVehicleScrapeTime = preReviewVehicleScrapeTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.is_used_applets")
    public String getIsUsedApplets() {
        return isUsedApplets;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.is_used_applets")
    public void setIsUsedApplets(String isUsedApplets) {
        this.isUsedApplets = isUsedApplets == null ? null : isUsedApplets.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.criteria_id")
    public Long getCriteriaId() {
        return criteriaId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.criteria_id")
    public void setCriteriaId(Long criteriaId) {
        this.criteriaId = criteriaId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.cust_pays_direct")
    public Integer getCustPaysDirect() {
        return custPaysDirect;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.cust_pays_direct")
    public void setCustPaysDirect(Integer custPaysDirect) {
        this.custPaysDirect = custPaysDirect;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.cust_amount")
    public BigDecimal getCustAmount() {
        return custAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.cust_amount")
    public void setCustAmount(BigDecimal custAmount) {
        this.custAmount = custAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.user_assumed_amount")
    public BigDecimal getUserAssumedAmount() {
        return userAssumedAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.user_assumed_amount")
    public void setUserAssumedAmount(BigDecimal userAssumedAmount) {
        this.userAssumedAmount = userAssumedAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.not_user_assumed_amount")
    public BigDecimal getNotUserAssumedAmount() {
        return notUserAssumedAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.not_user_assumed_amount")
    public void setNotUserAssumedAmount(BigDecimal notUserAssumedAmount) {
        this.notUserAssumedAmount = notUserAssumedAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sync_claim_flag")
    public Integer getSyncClaimFlag() {
        return syncClaimFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sync_claim_flag")
    public void setSyncClaimFlag(Integer syncClaimFlag) {
        this.syncClaimFlag = syncClaimFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.tax_rate")
    public String getTaxRate() {
        return taxRate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.tax_rate")
    public void setTaxRate(String taxRate) {
        this.taxRate = taxRate == null ? null : taxRate.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_type")
    public Integer getRepairDepotType() {
        return repairDepotType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_type")
    public void setRepairDepotType(Integer repairDepotType) {
        this.repairDepotType = repairDepotType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.declare_no")
    public String getDeclareNo() {
        return declareNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.declare_no")
    public void setDeclareNo(String declareNo) {
        this.declareNo = declareNo == null ? null : declareNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.declare_status")
    public Integer getDeclareStatus() {
        return declareStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.declare_status")
    public void setDeclareStatus(Integer declareStatus) {
        this.declareStatus = declareStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.declare_settlement_status")
    public Integer getDeclareSettlementStatus() {
        return declareSettlementStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.declare_settlement_status")
    public void setDeclareSettlementStatus(Integer declareSettlementStatus) {
        this.declareSettlementStatus = declareSettlementStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.settlement_no")
    public String getSettlementNo() {
        return settlementNo;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.settlement_no")
    public void setSettlementNo(String settlementNo) {
        this.settlementNo = settlementNo == null ? null : settlementNo.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.settlement_status")
    public Integer getSettlementStatus() {
        return settlementStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.settlement_status")
    public void setSettlementStatus(Integer settlementStatus) {
        this.settlementStatus = settlementStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.send_repair_time")
    public Date getSendRepairTime() {
        return sendRepairTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.send_repair_time")
    public void setSendRepairTime(Date sendRepairTime) {
        this.sendRepairTime = sendRepairTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.situation_desc")
    public String getSituationDesc() {
        return situationDesc;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.situation_desc")
    public void setSituationDesc(String situationDesc) {
        this.situationDesc = situationDesc == null ? null : situationDesc.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.property_status")
    public Integer getPropertyStatus() {
        return propertyStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.property_status")
    public void setPropertyStatus(Integer propertyStatus) {
        this.propertyStatus = propertyStatus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.product_line")
    public Integer getProductLine() {
        return productLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.product_line")
    public void setProductLine(Integer productLine) {
        this.productLine = productLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sub_product_line")
    public Integer getSubProductLine() {
        return subProductLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sub_product_line")
    public void setSubProductLine(Integer subProductLine) {
        this.subProductLine = subProductLine;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.self_funded_amount")
    public BigDecimal getSelfFundedAmount() {
        return selfFundedAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.self_funded_amount")
    public void setSelfFundedAmount(BigDecimal selfFundedAmount) {
        this.selfFundedAmount = selfFundedAmount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.continue_days")
    public String getContinueDays() {
        return continueDays;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.continue_days")
    public void setContinueDays(String continueDays) {
        this.continueDays = continueDays == null ? null : continueDays.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.created_time")
    public Date getCreatedTime() {
        return createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.created_time")
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.updated_time")
    public Date getUpdatedTime() {
        return updatedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.updated_time")
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskNo=").append(taskNo);
        sb.append(", taskType=").append(taskType);
        sb.append(", orgId=").append(orgId);
        sb.append(", orgName=").append(orgName);
        sb.append(", operateOrgId=").append(operateOrgId);
        sb.append(", operateOrgName=").append(operateOrgName);
        sb.append(", vehicleNo=").append(vehicleNo);
        sb.append(", vehicleModelSeq=").append(vehicleModelSeq);
        sb.append(", vehicleModelInfo=").append(vehicleModelInfo);
        sb.append(", vin=").append(vin);
        sb.append(", insuranceCompanyName=").append(insuranceCompanyName);
        sb.append(", repairTypeId=").append(repairTypeId);
        sb.append(", repairTypeName=").append(repairTypeName);
        sb.append(", repairGrade=").append(repairGrade);
        sb.append(", repairDepotId=").append(repairDepotId);
        sb.append(", repairDepotName=").append(repairDepotName);
        sb.append(", repairDepotOrgId=").append(repairDepotOrgId);
        sb.append(", repairDepotSapCode=").append(repairDepotSapCode);
        sb.append(", sapSendStatus=").append(sapSendStatus);
        sb.append(", sapSyncFlag=").append(sapSyncFlag);
        sb.append(", sapSuccessTime=").append(sapSuccessTime);
        sb.append(", partsLibraryType=").append(partsLibraryType);
        sb.append(", taskCreateTime=").append(taskCreateTime);
        sb.append(", taskInflowTime=").append(taskInflowTime);
        sb.append(", vehicleReciveTime=").append(vehicleReciveTime);
        sb.append(", vehicleCheckTime=").append(vehicleCheckTime);
        sb.append(", vehicleRepairTime=").append(vehicleRepairTime);
        sb.append(", reassignmentPassTime=").append(reassignmentPassTime);
        sb.append(", settleClosingTime=").append(settleClosingTime);
        sb.append(", expectedRepairDays=").append(expectedRepairDays);
        sb.append(", expectedRepairComplete=").append(expectedRepairComplete);
        sb.append(", accidentReportNumber=").append(accidentReportNumber);
        sb.append(", repairFlag=").append(repairFlag);
        sb.append(", terminalId=").append(terminalId);
        sb.append(", totalMileage=").append(totalMileage);
        sb.append(", terminalMileage=").append(terminalMileage);
        sb.append(", associatedOrder=").append(associatedOrder);
        sb.append(", orderType=").append(orderType);
        sb.append(", relateType=").append(relateType);
        sb.append(", orderRemark=").append(orderRemark);
        sb.append(", violationName=").append(violationName);
        sb.append(", violationTelNo=").append(violationTelNo);
        sb.append(", vehicleClientMaintenanceFee=").append(vehicleClientMaintenanceFee);
        sb.append(", clientInspectTag=").append(clientInspectTag);
        sb.append(", clientUpkeepTag=").append(clientUpkeepTag);
        sb.append(", authId=").append(authId);
        sb.append(", noDeductiblesFlag=").append(noDeductiblesFlag);
        sb.append(", serviceType=").append(serviceType);
        sb.append(", serviceContent=").append(serviceContent);
        sb.append(", driverName=").append(driverName);
        sb.append(", driverTel=").append(driverTel);
        sb.append(", routingInspectionName=").append(routingInspectionName);
        sb.append(", routingInspectionTel=").append(routingInspectionTel);
        sb.append(", damagedPartDescribe=").append(damagedPartDescribe);
        sb.append(", accidentDescribe=").append(accidentDescribe);
        sb.append(", trailerFlag=").append(trailerFlag);
        sb.append(", repairReviewTotalAmount=").append(repairReviewTotalAmount);
        sb.append(", repairReplaceTotalAmount=").append(repairReplaceTotalAmount);
        sb.append(", repairRepairTotalAmount=").append(repairRepairTotalAmount);
        sb.append(", repairInsuranceTotalAmount=").append(repairInsuranceTotalAmount);
        sb.append(", vehicleReplaceTotalAmount=").append(vehicleReplaceTotalAmount);
        sb.append(", vehicleRepairTotalAmount=").append(vehicleRepairTotalAmount);
        sb.append(", vehicleInsuranceTotalAmount=").append(vehicleInsuranceTotalAmount);
        sb.append(", vehicleManageViewFlag=").append(vehicleManageViewFlag);
        sb.append(", resurveyFlag=").append(resurveyFlag);
        sb.append(", resurveyPart=").append(resurveyPart);
        sb.append(", recoveryFlag=").append(recoveryFlag);
        sb.append(", dutySituation=").append(dutySituation);
        sb.append(", recoveryAmount=").append(recoveryAmount);
        sb.append(", insuranceAmount=").append(insuranceAmount);
        sb.append(", accDepAmount=").append(accDepAmount);
        sb.append(", outageLossAmount=").append(outageLossAmount);
        sb.append(", vehicleLossAmount=").append(vehicleLossAmount);
        sb.append(", trailerRescueAmount=").append(trailerRescueAmount);
        sb.append(", maintainAmount=").append(maintainAmount);
        sb.append(", lossOrderAmount=").append(lossOrderAmount);
        sb.append(", reassignmentRepairOrgId=").append(reassignmentRepairOrgId);
        sb.append(", reassignmentReasons=").append(reassignmentReasons);
        sb.append(", reassignmentRejectReasons=").append(reassignmentRejectReasons);
        sb.append(", verificationRejectReasons=").append(verificationRejectReasons);
        sb.append(", verificationRejectReasonsDetail=").append(verificationRejectReasonsDetail);
        sb.append(", verificationRejectLevel=").append(verificationRejectLevel);
        sb.append(", checkResultFlag=").append(checkResultFlag);
        sb.append(", checkUnqualifiedReason=").append(checkUnqualifiedReason);
        sb.append(", confirmType=").append(confirmType);
        sb.append(", maintainToRepairFlag=").append(maintainToRepairFlag);
        sb.append(", verificationLossTaskOperId=").append(verificationLossTaskOperId);
        sb.append(", examineLevel=").append(examineLevel);
        sb.append(", verificationLossCheckTime=").append(verificationLossCheckTime);
        sb.append(", verificationLossCheckId=").append(verificationLossCheckId);
        sb.append(", overTimeReasons=").append(overTimeReasons);
        sb.append(", repairTotalAmountFirst=").append(repairTotalAmountFirst);
        sb.append(", nuclearLossReversionFlag=").append(nuclearLossReversionFlag);
        sb.append(", remark=").append(remark);
        sb.append(", tirenumber=").append(tirenumber);
        sb.append(", tireUnitPrice=").append(tireUnitPrice);
        sb.append(", tireBrand=").append(tireBrand);
        sb.append(", associatedTaskNo=").append(associatedTaskNo);
        sb.append(", insuranceFlag=").append(insuranceFlag);
        sb.append(", advancedAuditLeve=").append(advancedAuditLeve);
        sb.append(", alreadyIncomingAmount=").append(alreadyIncomingAmount);
        sb.append(", alreadyPayAmount=").append(alreadyPayAmount);
        sb.append(", waitPayAmount=").append(waitPayAmount);
        sb.append(", mustClaimAmount=").append(mustClaimAmount);
        sb.append(", mustPayAmount=").append(mustPayAmount);
        sb.append(", vehicleRepairCostAmount=").append(vehicleRepairCostAmount);
        sb.append(", repairSettleAmount=").append(repairSettleAmount);
        sb.append(", repairFaxSettleAmount=").append(repairFaxSettleAmount);
        sb.append(", transferFlag=").append(transferFlag);
        sb.append(", rationalIndemnityCnt=").append(rationalIndemnityCnt);
        sb.append(", accidentDayTime=").append(accidentDayTime);
        sb.append(", deductFlag=").append(deductFlag);
        sb.append(", origin=").append(origin);
        sb.append(", renttype=").append(renttype);
        sb.append(", factOperateTag=").append(factOperateTag);
        sb.append(", takeUserName=").append(takeUserName);
        sb.append(", takeUserPhone=").append(takeUserPhone);
        sb.append(", takeVoucher=").append(takeVoucher);
        sb.append(", synTakeTime=").append(synTakeTime);
        sb.append(", closeReason=").append(closeReason);
        sb.append(", claimsFlag=").append(claimsFlag);
        sb.append(", estimatedClaimAmount=").append(estimatedClaimAmount);
        sb.append(", carDamageType=").append(carDamageType);
        sb.append(", vehicleScrapeValue=").append(vehicleScrapeValue);
        sb.append(", reviewToSelFeeFlag=").append(reviewToSelFeeFlag);
        sb.append(", confirmCarDamageType=").append(confirmCarDamageType);
        sb.append(", accidentNo=").append(accidentNo);
        sb.append(", insurancePreReviewTime=").append(insurancePreReviewTime);
        sb.append(", insurancePreReviewLevel=").append(insurancePreReviewLevel);
        sb.append(", preReviewVehicleScrapeValue=").append(preReviewVehicleScrapeValue);
        sb.append(", preReviewVehicleScrapeTime=").append(preReviewVehicleScrapeTime);
        sb.append(", isUsedApplets=").append(isUsedApplets);
        sb.append(", criteriaId=").append(criteriaId);
        sb.append(", custPaysDirect=").append(custPaysDirect);
        sb.append(", custAmount=").append(custAmount);
        sb.append(", userAssumedAmount=").append(userAssumedAmount);
        sb.append(", notUserAssumedAmount=").append(notUserAssumedAmount);
        sb.append(", syncClaimFlag=").append(syncClaimFlag);
        sb.append(", taxRate=").append(taxRate);
        sb.append(", repairDepotType=").append(repairDepotType);
        sb.append(", declareNo=").append(declareNo);
        sb.append(", declareStatus=").append(declareStatus);
        sb.append(", declareSettlementStatus=").append(declareSettlementStatus);
        sb.append(", settlementNo=").append(settlementNo);
        sb.append(", settlementStatus=").append(settlementStatus);
        sb.append(", sendRepairTime=").append(sendRepairTime);
        sb.append(", situationDesc=").append(situationDesc);
        sb.append(", propertyStatus=").append(propertyStatus);
        sb.append(", productLine=").append(productLine);
        sb.append(", subProductLine=").append(subProductLine);
        sb.append(", selfFundedAmount=").append(selfFundedAmount);
        sb.append(", continueDays=").append(continueDays);
        sb.append(", createBy=").append(createBy);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}