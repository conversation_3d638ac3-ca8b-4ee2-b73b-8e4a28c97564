package com.extracme.saas.autocare.model.dto;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "待定损配件库列表查询DTO")
public class RepairItemLibraryCheckQueryDTO extends BasePageDTO {

    @ApiModelProperty(value = "所属公司")
    private String orgId;

    @ApiModelProperty(value = "项目名称")
    private String itemName;

    @ApiModelProperty(value = "项目类型 1：保养 2：终端 3：维修")
    private Integer itemType;

    @ApiModelProperty(value = "车型ID")
    private Long vehicleModelSeq;
}