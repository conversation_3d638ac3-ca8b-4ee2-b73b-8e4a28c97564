package com.extracme.saas.autocare.model.dto;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 车辆维修记录查询DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "车辆维修记录查询DTO")
public class VehicleRepairRecordQueryDTO extends BasePageDTO {
    
    /**
     * 车架号
     */
    @ApiModelProperty(value = "车架号", example = "LSVAU033512345678")
    private String vin;
    
    /**
     * ID
     */
    @ApiModelProperty(value = "ID", example = "1")
    private Long id;
    
    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号", example = "MTC202405010001")
    private String taskNo;
    
    /**
     * 修理厂名称
     */
    @ApiModelProperty(value = "修理厂名称", example = "上海某汽车修理厂")
    private String repairDepotName;
    
    /**
     * 修理类型ID 1:事故维修 2:自费维修 3:车辆保养
     */
    @ApiModelProperty(value = "修理类型ID", example = "1", notes = "1:事故维修 2:自费维修 3:车辆保养")
    private String repairTypeId;
    
    /**
     * 任务流入时间范围开始
     */
    @ApiModelProperty(value = "任务流入时间范围开始", example = "2024-05-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date taskInflowTimeStart;
    
    /**
     * 任务流入时间范围结束
     */
    @ApiModelProperty(value = "任务流入时间范围结束", example = "2024-05-31")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date taskInflowTimeEnd;
    
    /**
     * 车辆验收时间范围开始
     */
    @ApiModelProperty(value = "车辆验收时间范围开始", example = "2024-05-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date vehicleCheckDayStart;
    
    /**
     * 车辆验收时间范围结束
     */
    @ApiModelProperty(value = "车辆验收时间范围结束", example = "2024-05-31")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date vehicleCheckDayEnd;
    
    /**
     * 零件名称
     */
    @ApiModelProperty(value = "零件名称", example = "前保险杠")
    private String partName;
    
    /**
     * 总里程数
     */
    @ApiModelProperty(value = "总里程数", example = "12345.67")
    private BigDecimal totalMileage;
    
    /**
     * 维修人员姓名
     */
    @ApiModelProperty(value = "维修人员姓名", example = "张三")
    private String repairName;
}
