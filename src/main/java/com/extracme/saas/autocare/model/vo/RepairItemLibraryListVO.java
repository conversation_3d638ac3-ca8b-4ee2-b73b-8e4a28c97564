package com.extracme.saas.autocare.model.vo;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "维修项目库列表信息")
public class RepairItemLibraryListVO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "序号")
    private Integer serialNumber;

    @ApiModelProperty(value = "项目编号")
    private String itemNo;

    @ApiModelProperty(value = "项目名称")
    private String itemName;

    @ApiModelProperty(value = "项目类型 1：保养 2：终端 3：维修")
    private Integer itemType;

    @ApiModelProperty(value = "车型ID")
    private Long vehicleModelSeq;

    @ApiModelProperty(value = "车型名称")
    private String vehicleModelInfo;

    @ApiModelProperty(value = "工时费全国市场价")
    private BigDecimal hourFeeNationalMarketPrice;

    @ApiModelProperty(value = "工时费全国上限")
    private BigDecimal hourFeeNationalHighestPrice;

    @ApiModelProperty(value = "材料费全国市场价")
    private BigDecimal materialCostNationalMarketPrice;

    @ApiModelProperty(value = "材料费全国上限")
    private BigDecimal materialCostNationalHighestPrice;

    @ApiModelProperty(value = "是否常用 0：否 1：是")
    private Integer isCommonlyUsed;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "状态 0：禁用 1：启用")
    private Integer status;
}
