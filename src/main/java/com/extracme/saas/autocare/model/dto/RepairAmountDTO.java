package com.extracme.saas.autocare.model.dto;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 维修金额DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "维修金额DTO")
public class RepairAmountDTO {
    
    @ApiModelProperty(value = "任务编号")
    private String taskNo;
    
    @ApiModelProperty(value = "车管定损总计")
    private BigDecimal vehicleInsuranceTotalAmount;
    
    @ApiModelProperty(value = "非用户承担金额")
    private BigDecimal notUserAssumedAmount;
    
    @ApiModelProperty(value = "预估保险理赔价格")
    private BigDecimal estimatedClaimAmount;
}
