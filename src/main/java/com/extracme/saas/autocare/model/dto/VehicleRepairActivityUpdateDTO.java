package com.extracme.saas.autocare.model.dto;

import java.math.BigDecimal;
import java.util.List;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import org.apache.commons.collections4.CollectionUtils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 车辆维修活动更新DTO
 */
@Data
@ApiModel(description = "车辆维修活动更新DTO")
public class VehicleRepairActivityUpdateDTO {

    @ApiModelProperty(value = "ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "任务编号", required = true, example = "REPAIR20240601001")
    @Size(max = 50, message = "任务编号长度不能超过50个字符")
    private String taskNo;

    @ApiModelProperty(value = "修理类型ID 1:事故维修 2:自费维修 3:车辆保养", example = "1")
    @Pattern(regexp = "^[1-3]$", message = "修理类型ID只能是1、2或3")
    private String repairTypeId;

    @ApiModelProperty(value = "事故报案号", example = "ACC20240601001")
    @Size(max = 50, message = "事故报案号长度不能超过50个字符")
    private String accidentReportNumber;

    @ApiModelProperty(value = "预计修理天数", example = "3")
    @Pattern(regexp = "^[0-9]+$", message = "预计修理天数必须是正整数")
    private String expectedRepairDays;

    @ApiModelProperty(value = "修理厂换件金额合计", example = "1000.00")
    private String repairReplaceTotalAmount;

    @ApiModelProperty(value = "修理厂修理金额合计", example = "500.00")
    private String repairRepairTotalAmount;

    @ApiModelProperty(value = "修理厂定损总计", example = "1500.00")
    private String repairInsuranceTotalAmount;

    @ApiModelProperty(value = "车管换件金额合计", example = "1000.00")
    private String vehicleReplaceTotalAmount;

    @ApiModelProperty(value = "车管修理金额合计", example = "500.00")
    private String vehicleRepairTotalAmount;

    @ApiModelProperty(value = "车管定损总计", example = "1500.00")
    private String vehicleInsuranceTotalAmount;

    @ApiModelProperty(value = "是否需要维修 0:否 1:是", example = "1")
    @Pattern(regexp = "^[01]$", message = "是否需要维修只能是0或1")
    private String repairFlag;

    @ApiModelProperty(value = "保养费用", example = "300.00")
    private String maintainAmount;

    @ApiModelProperty(value = "是否需要复勘 0:否 1:是", example = "0")
    @Pattern(regexp = "^[01]$", message = "是否需要复勘只能是0或1")
    private String resurveyFlag;

    @ApiModelProperty(value = "复勘部位", example = "左前门")
    @Size(max = 200, message = "复勘部位长度不能超过200个字符")
    private String resurveyPart;

    @ApiModelProperty(value = "责任情况 1:全责 2:主责 3:次责 4:平责 5:无责", example = "1")
    @Pattern(regexp = "^[1-5]$", message = "责任情况只能是1-5之间的数字")
    private String dutySituation;

    @ApiModelProperty(value = "向用户追偿费用", example = "200.00")
    private String recoveryAmount;

    @ApiModelProperty(value = "保险上付费", example = "1000.00")
    private String insuranceAmount;

    @ApiModelProperty(value = "加速折旧费", example = "100.00")
    private String accDepAmount;

    @ApiModelProperty(value = "停运损失费", example = "300.00")
    private String outageLossAmount;

    @ApiModelProperty(value = "车辆损失费", example = "500.00")
    private String vehicleLossAmount;

    @ApiModelProperty(value = "拖车救援费", example = "200.00")
    private String trailerRescueAmount;

    @ApiModelProperty(value = "定损单金额", example = "2000.00")
    private String lossOrderAmount;

    @ApiModelProperty(value = "损坏部位图片", notes = "图片URL列表，最多48张")
    private List<String> damagedPartPicture;

    @ApiModelProperty(value = "损坏部位视频", notes = "视频URL列表")
    private List<String> damagedPartVideo;

    @ApiModelProperty(value = "维修图片（验收图片）", notes = "图片URL列表，最多48张")
    private List<String> repairPicture;

    @ApiModelProperty(value = "事故责任认定书图片", notes = "图片URL列表，最多4张")
    private List<String> accidentLiabilityConfirmationPicture;

    @ApiModelProperty(value = "保司定损单图片", notes = "图片URL列表，最多4张")
    private List<String> insuranceCompanyLossOrderPicture;

    @ApiModelProperty(value = "我方驾驶证图片", notes = "图片URL列表，最多4张")
    private List<String> ourDriverLicensePicture;

    @ApiModelProperty(value = "验收视频", notes = "视频URL列表")
    private List<String> checkVideo;

    @ApiModelProperty(value = "超时原因", example = "配件供应延迟")
    @Size(max = 200, message = "超时原因长度不能超过200个字符")
    private String overTimeReasons;

    @ApiModelProperty(value = "申请改派原因", example = "修理厂无法修理")
    @Size(max = 200, message = "申请改派原因长度不能超过200个字符")
    private String reassignmentReasons;

    @ApiModelProperty(value = "是否保险理赔状态 0:否 1:是", example = "1")
    @Min(value = 0, message = "是否保险理赔状态只能是0或1")
    @Max(value = 1, message = "是否保险理赔状态只能是0或1")
    private Integer claimsFlag;

    @ApiModelProperty(value = "预估保险理赔价格", example = "2000.00")
    @DecimalMin(value = "0", message = "预估保险理赔价格不能小于0")
    private BigDecimal estimatedClaimAmount;

    @ApiModelProperty(value = "修理项目明细")
    private List<ViewRepairItemDetailDTO> repairItemDetail;

    @ApiModelProperty(value = "换件项目明细")
    private List<ReplaceItemDetailDTO> replaceItemDetail;

    @ApiModelProperty(value = "车管核价意见 0:同意报价 1:异议", example = "0")
    @Pattern(regexp = "^[01]$", message = "车管核价意见只能是0或1")
    private String vehicleManageViewFlag;

    @ApiModelProperty(value = "确认类型 1:公司自费 2:客户付费 3:我方有责 4:我方无责", example = "1")
    @Min(value = 1, message = "确认类型只能是1-4之间的数字")
    @Max(value = 4, message = "确认类型只能是1-4之间的数字")
    private Integer confirmType;

    @ApiModelProperty(value = "确认车损类型 1:我司承担 2:客户承担", example = "1")
    @Min(value = 1, message = "确认车损类型只能是1或2")
    @Max(value = 2, message = "确认车损类型只能是1或2")
    private Integer confirmCarDamageType;

    @ApiModelProperty(value = "最后修改时间", example = "2024-06-01 12:00:00")
    private String updateEndTime;

    @ApiModelProperty(value = "是否使用小程序 0-否 1-是", example = "0")
    @Pattern(regexp = "^[01]$", message = "是否使用小程序只能是0或1")
    private String isUsedApplets;

    @ApiModelProperty(value = "是否客户直付 1-是 2-否", example = "1")
    @Min(value = 1, message = "是否客户直付只能是1或2")
    @Max(value = 2, message = "是否客户直付只能是1或2")
    private Integer custPaysDirect;

    @ApiModelProperty(value = "客户直付金额", example = "500.00")
    @DecimalMin(value = "0", message = "客户直付金额不能小于0")
    private BigDecimal custAmount;

    @ApiModelProperty(value = "客户直付凭证", notes = "图片URL列表")
    private List<String> custPicture;

    @ApiModelProperty(value = "是否校验 0-默认校验 1-不校验", example = "0")
    @Min(value = 0, message = "是否校验只能是0或1")
    @Max(value = 1, message = "是否校验只能是0或1")
    private int checkFlag;

    @ApiModelProperty(value = "是否进入下一阶段审核", example = "false")
    private boolean intoNextStageAudit;

    @ApiModelProperty(value = "当前环节（用于限制保存节点）", example = "1")
    private String currentActivityCode;

    /**
     * 检查图片数量是否超出范围
     * @return 是否超出范围
     */
    @ApiModelProperty(hidden = true)
    public boolean pictureSizeOutOfRange() {
        if (CollectionUtils.isNotEmpty(damagedPartPicture) && damagedPartPicture.size() > 48) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(repairPicture) && repairPicture.size() > 48) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(accidentLiabilityConfirmationPicture) && accidentLiabilityConfirmationPicture.size() > 4) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(insuranceCompanyLossOrderPicture) && insuranceCompanyLossOrderPicture.size() > 4) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(ourDriverLicensePicture) && ourDriverLicensePicture.size() > 4) {
            return true;
        }
        return false;
    }
}
