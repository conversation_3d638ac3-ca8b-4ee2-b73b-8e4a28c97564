package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(description = "用户信息VO")
public class UserVO {
    @ApiModelProperty(value = "用户ID")
    private Long id;

    @ApiModelProperty(value = "租户ID", example = "1")
    private Long tenantId;

    @ApiModelProperty(value = "租户名称")
    private String tenantName;

    @ApiModelProperty(value = "所属机构")
    private String orgName;

    @ApiModelProperty(value = "所属机构ID")
    private String orgId;

    @ApiModelProperty(value = "真实姓名")
    private String nickname;

    @ApiModelProperty(value = "账户类型")
    private Integer accountType;

    @ApiModelProperty(value = "对应修理厂")
    private String repairShop;

    @ApiModelProperty(value = "关联维修站点ID")
    private String repairDepotId;

    @ApiModelProperty(value = "修理厂名称")
    private String repairDepotName;

    @ApiModelProperty(value = "状态", example = "1")
    private Integer status;

    @ApiModelProperty(value = "联系电话")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "角色ID列表")
    private List<Long> roleIds;

    @ApiModelProperty(value = "角色名称列表")
    private List<String> roleNames;

    @ApiModelProperty(value = "审批层级（0：无，1：一级，2：二级，3：三级，4：四级）", example = "1")
    private Integer approvalLevel;

    @ApiModelProperty(value = "创建时间")
    private Date createdTime;

    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;
}