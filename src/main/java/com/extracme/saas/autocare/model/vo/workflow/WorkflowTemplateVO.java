package com.extracme.saas.autocare.model.vo.workflow;

import java.util.List;

import com.extracme.saas.autocare.enums.TaskTypeEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 工作流模板视图对象
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Data
@ApiModel("工作流模板视图")
public class WorkflowTemplateVO {

    /**
     * 模板ID
     */
    @ApiModelProperty("模板ID")
    private Long id;

    /**
     * 流程模板名称
     */
    @ApiModelProperty("流程模板名称")
    private String workflowName;

    /**
     * 流程描述
     */
    @ApiModelProperty("流程描述")
    private String description;

    /**
     * 任务类型：
     * 0 - 自建任务
     * 1 - 事故维修
     * 2 - 自费维修
     * 3 - 车辆保养
     * 4 - 轮胎任务
     * 5 - 自费维修(原车辆保养)
     * 6 - 常规保养
     * 7 - 终端维修
     * @see TaskTypeEnum
     */
    @ApiModelProperty(value = "任务类型", example = "1", notes = "0:自建任务, 1:事故维修, 2:自费维修, 3:车辆保养, 4:轮胎任务, 5:自费维修(原车辆保养), 6:常规保养, 7:终端维修")
    private Integer taskType;

    /**
     * 修理厂类型
     */
    @ApiModelProperty("修理厂类型")
    private Integer repairFactoryType;

    /**
     * 子产品线
     */
    @ApiModelProperty("子产品线")
    private Integer subProductLine;

    /**
     * 是否启用
     * 1: 启用
     * 0: 禁用
     */
    @ApiModelProperty(value = "是否启用", example = "1", notes = "1:启用, 0:禁用")
    private Integer isActive;

    /**
     * 租户编码
     */
    @ApiModelProperty("租户编码")
    private String tenantCode;

    /**
     * 活动节点定义列表
     */
    @ApiModelProperty("活动节点定义列表")
    private List<ActivityDefinitionVO> activityDefinitions;
}