package com.extracme.saas.autocare.model.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.annotation.Generated;

/**
 * Database Table Remarks:
 *   维修厂信息表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table mtc_repair_depot_info
 */
public class MtcRepairDepotInfo implements Serializable {
    /**
     * Database Column Remarks:
     *   ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   单点用户ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.sso_user_id")
    private Long ssoUserId;

    /**
     * Database Column Remarks:
     *   修理厂ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_id")
    private String repairDepotId;

    /**
     * Database Column Remarks:
     *   维修厂sap编码
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_sap_code")
    private String repairDepotSapCode;

    /**
     * Database Column Remarks:
     *   修理厂名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_name")
    private String repairDepotName;

    /**
     * Database Column Remarks:
     *   维修厂所属组织机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_org_id")
    private String repairDepotOrgId;

    /**
     * Database Column Remarks:
     *   修理厂账号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_account")
    private String repairDepotAccount;

    /**
     * Database Column Remarks:
     *   修理厂等级
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_grade")
    private String repairDepotGrade;

    /**
     * Database Column Remarks:
     *   修理厂类型(1:合作修理厂 2:非合作修理厂)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_type")
    private Integer repairDepotType;

    /**
     * Database Column Remarks:
     *   是否对外展示(1:展示 2:不展示)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.is_show")
    private Integer isShow;

    /**
     * Database Column Remarks:
     *   是否保养点(0:否 1:是)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.maintenance_point")
    private Integer maintenancePoint;

    /**
     * Database Column Remarks:
     *   修理厂地址（省）ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.province_id")
    private Long provinceId;

    /**
     * Database Column Remarks:
     *   修理厂地址（市）ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.city_id")
    private Long cityId;

    /**
     * Database Column Remarks:
     *   修理厂地址（区）ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.area_id")
    private Long areaId;

    /**
     * Database Column Remarks:
     *   修理厂详细地址
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.address")
    private String address;

    /**
     * Database Column Remarks:
     *   税率
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.tax_rate")
    private String taxRate;

    /**
     * Database Column Remarks:
     *   负责人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.linkman_name")
    private String linkmanName;

    /**
     * Database Column Remarks:
     *   合作模式
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.cooperation_mode")
    private String cooperationMode;

    /**
     * Database Column Remarks:
     *   事故联系人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.accident_contacts")
    private String accidentContacts;

    /**
     * Database Column Remarks:
     *   事故联系电话
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.accident_tel")
    private String accidentTel;

    /**
     * Database Column Remarks:
     *   维保联系人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.maintenance_contacts")
    private String maintenanceContacts;

    /**
     * Database Column Remarks:
     *   维保联系电话
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.maintenance_tel")
    private String maintenanceTel;

    /**
     * Database Column Remarks:
     *   修理厂经度
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_longitude")
    private BigDecimal repairDepotLongitude;

    /**
     * Database Column Remarks:
     *   修理厂纬度
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_latitude")
    private BigDecimal repairDepotLatitude;

    /**
     * Database Column Remarks:
     *   车型是否全选标记(0:没有全选 1:全部选中)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.vehicle_model_all_flag")
    private Integer vehicleModelAllFlag;

    /**
     * Database Column Remarks:
     *   单点用户创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.sso_create_time")
    private Date ssoCreateTime;

    /**
     * Database Column Remarks:
     *   单点用户修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.sso_update_time")
    private Date ssoUpdateTime;

    /**
     * Database Column Remarks:
     *   负责人手机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.old_repair_factory_code")
    private String oldRepairFactoryCode;

    /**
     * Database Column Remarks:
     *   状态(1:有效 0:无效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.status")
    private Integer status;

    /**
     * Database Column Remarks:
     *   逻辑删除状态(1:已删除 0:未删除)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.del_flag")
    private Integer delFlag;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.remark")
    private String remark;

    /**
     * Database Column Remarks:
     *   单点同步标志(1:从单点同步 2:手动从老平台移植)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.sso_flag")
    private Integer ssoFlag;

    /**
     * Database Column Remarks:
     *   可修类目(0:全部 1:外观 2:易损件)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.can_repair_item")
    private Integer canRepairItem;

    /**
     * Database Column Remarks:
     *   是否保修点(0:否 1:是)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.warranty_point")
    private Integer warrantyPoint;

    /**
     * Database Column Remarks:
     *   营业时间设定(0:全天 1:其他)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.business_type")
    private Integer businessType;

    /**
     * Database Column Remarks:
     *   营业开始时间(每隔30分钟加1，0点0分:1 23:30:48)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.business_start_time")
    private Long businessStartTime;

    /**
     * Database Column Remarks:
     *   营业结束时间(每隔30分钟加1，0点0分:1 23:30:48)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.business_end_time")
    private Long businessEndTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.create_by")
    private String createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.created_time")
    private Date createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.update_by")
    private String updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.updated_time")
    private Date updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    private static final long serialVersionUID = 1L;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.sso_user_id")
    public Long getSsoUserId() {
        return ssoUserId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.sso_user_id")
    public void setSsoUserId(Long ssoUserId) {
        this.ssoUserId = ssoUserId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_id")
    public String getRepairDepotId() {
        return repairDepotId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_id")
    public void setRepairDepotId(String repairDepotId) {
        this.repairDepotId = repairDepotId == null ? null : repairDepotId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_sap_code")
    public String getRepairDepotSapCode() {
        return repairDepotSapCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_sap_code")
    public void setRepairDepotSapCode(String repairDepotSapCode) {
        this.repairDepotSapCode = repairDepotSapCode == null ? null : repairDepotSapCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_name")
    public String getRepairDepotName() {
        return repairDepotName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_name")
    public void setRepairDepotName(String repairDepotName) {
        this.repairDepotName = repairDepotName == null ? null : repairDepotName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_org_id")
    public String getRepairDepotOrgId() {
        return repairDepotOrgId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_org_id")
    public void setRepairDepotOrgId(String repairDepotOrgId) {
        this.repairDepotOrgId = repairDepotOrgId == null ? null : repairDepotOrgId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_account")
    public String getRepairDepotAccount() {
        return repairDepotAccount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_account")
    public void setRepairDepotAccount(String repairDepotAccount) {
        this.repairDepotAccount = repairDepotAccount == null ? null : repairDepotAccount.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_grade")
    public String getRepairDepotGrade() {
        return repairDepotGrade;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_grade")
    public void setRepairDepotGrade(String repairDepotGrade) {
        this.repairDepotGrade = repairDepotGrade == null ? null : repairDepotGrade.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_type")
    public Integer getRepairDepotType() {
        return repairDepotType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_type")
    public void setRepairDepotType(Integer repairDepotType) {
        this.repairDepotType = repairDepotType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.is_show")
    public Integer getIsShow() {
        return isShow;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.is_show")
    public void setIsShow(Integer isShow) {
        this.isShow = isShow;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.maintenance_point")
    public Integer getMaintenancePoint() {
        return maintenancePoint;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.maintenance_point")
    public void setMaintenancePoint(Integer maintenancePoint) {
        this.maintenancePoint = maintenancePoint;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.province_id")
    public Long getProvinceId() {
        return provinceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.province_id")
    public void setProvinceId(Long provinceId) {
        this.provinceId = provinceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.city_id")
    public Long getCityId() {
        return cityId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.city_id")
    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.area_id")
    public Long getAreaId() {
        return areaId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.area_id")
    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.address")
    public String getAddress() {
        return address;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.address")
    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.tax_rate")
    public String getTaxRate() {
        return taxRate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.tax_rate")
    public void setTaxRate(String taxRate) {
        this.taxRate = taxRate == null ? null : taxRate.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.linkman_name")
    public String getLinkmanName() {
        return linkmanName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.linkman_name")
    public void setLinkmanName(String linkmanName) {
        this.linkmanName = linkmanName == null ? null : linkmanName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.cooperation_mode")
    public String getCooperationMode() {
        return cooperationMode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.cooperation_mode")
    public void setCooperationMode(String cooperationMode) {
        this.cooperationMode = cooperationMode == null ? null : cooperationMode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.accident_contacts")
    public String getAccidentContacts() {
        return accidentContacts;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.accident_contacts")
    public void setAccidentContacts(String accidentContacts) {
        this.accidentContacts = accidentContacts == null ? null : accidentContacts.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.accident_tel")
    public String getAccidentTel() {
        return accidentTel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.accident_tel")
    public void setAccidentTel(String accidentTel) {
        this.accidentTel = accidentTel == null ? null : accidentTel.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.maintenance_contacts")
    public String getMaintenanceContacts() {
        return maintenanceContacts;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.maintenance_contacts")
    public void setMaintenanceContacts(String maintenanceContacts) {
        this.maintenanceContacts = maintenanceContacts == null ? null : maintenanceContacts.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.maintenance_tel")
    public String getMaintenanceTel() {
        return maintenanceTel;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.maintenance_tel")
    public void setMaintenanceTel(String maintenanceTel) {
        this.maintenanceTel = maintenanceTel == null ? null : maintenanceTel.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_longitude")
    public BigDecimal getRepairDepotLongitude() {
        return repairDepotLongitude;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_longitude")
    public void setRepairDepotLongitude(BigDecimal repairDepotLongitude) {
        this.repairDepotLongitude = repairDepotLongitude;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_latitude")
    public BigDecimal getRepairDepotLatitude() {
        return repairDepotLatitude;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.repair_depot_latitude")
    public void setRepairDepotLatitude(BigDecimal repairDepotLatitude) {
        this.repairDepotLatitude = repairDepotLatitude;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.vehicle_model_all_flag")
    public Integer getVehicleModelAllFlag() {
        return vehicleModelAllFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.vehicle_model_all_flag")
    public void setVehicleModelAllFlag(Integer vehicleModelAllFlag) {
        this.vehicleModelAllFlag = vehicleModelAllFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.sso_create_time")
    public Date getSsoCreateTime() {
        return ssoCreateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.sso_create_time")
    public void setSsoCreateTime(Date ssoCreateTime) {
        this.ssoCreateTime = ssoCreateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.sso_update_time")
    public Date getSsoUpdateTime() {
        return ssoUpdateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.sso_update_time")
    public void setSsoUpdateTime(Date ssoUpdateTime) {
        this.ssoUpdateTime = ssoUpdateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.old_repair_factory_code")
    public String getOldRepairFactoryCode() {
        return oldRepairFactoryCode;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.old_repair_factory_code")
    public void setOldRepairFactoryCode(String oldRepairFactoryCode) {
        this.oldRepairFactoryCode = oldRepairFactoryCode == null ? null : oldRepairFactoryCode.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.status")
    public Integer getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.status")
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.del_flag")
    public Integer getDelFlag() {
        return delFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.del_flag")
    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.remark")
    public String getRemark() {
        return remark;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.remark")
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.sso_flag")
    public Integer getSsoFlag() {
        return ssoFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.sso_flag")
    public void setSsoFlag(Integer ssoFlag) {
        this.ssoFlag = ssoFlag;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.can_repair_item")
    public Integer getCanRepairItem() {
        return canRepairItem;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.can_repair_item")
    public void setCanRepairItem(Integer canRepairItem) {
        this.canRepairItem = canRepairItem;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.warranty_point")
    public Integer getWarrantyPoint() {
        return warrantyPoint;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.warranty_point")
    public void setWarrantyPoint(Integer warrantyPoint) {
        this.warrantyPoint = warrantyPoint;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.business_type")
    public Integer getBusinessType() {
        return businessType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.business_type")
    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.business_start_time")
    public Long getBusinessStartTime() {
        return businessStartTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.business_start_time")
    public void setBusinessStartTime(Long businessStartTime) {
        this.businessStartTime = businessStartTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.business_end_time")
    public Long getBusinessEndTime() {
        return businessEndTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.business_end_time")
    public void setBusinessEndTime(Long businessEndTime) {
        this.businessEndTime = businessEndTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.create_by")
    public String getCreateBy() {
        return createBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.create_by")
    public void setCreateBy(String createBy) {
        this.createBy = createBy == null ? null : createBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.created_time")
    public Date getCreatedTime() {
        return createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.created_time")
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.update_by")
    public String getUpdateBy() {
        return updateBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.update_by")
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy == null ? null : updateBy.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.updated_time")
    public Date getUpdatedTime() {
        return updatedTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_depot_info.updated_time")
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_depot_info")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", ssoUserId=").append(ssoUserId);
        sb.append(", repairDepotId=").append(repairDepotId);
        sb.append(", repairDepotSapCode=").append(repairDepotSapCode);
        sb.append(", repairDepotName=").append(repairDepotName);
        sb.append(", repairDepotOrgId=").append(repairDepotOrgId);
        sb.append(", repairDepotAccount=").append(repairDepotAccount);
        sb.append(", repairDepotGrade=").append(repairDepotGrade);
        sb.append(", repairDepotType=").append(repairDepotType);
        sb.append(", isShow=").append(isShow);
        sb.append(", maintenancePoint=").append(maintenancePoint);
        sb.append(", provinceId=").append(provinceId);
        sb.append(", cityId=").append(cityId);
        sb.append(", areaId=").append(areaId);
        sb.append(", address=").append(address);
        sb.append(", taxRate=").append(taxRate);
        sb.append(", linkmanName=").append(linkmanName);
        sb.append(", cooperationMode=").append(cooperationMode);
        sb.append(", accidentContacts=").append(accidentContacts);
        sb.append(", accidentTel=").append(accidentTel);
        sb.append(", maintenanceContacts=").append(maintenanceContacts);
        sb.append(", maintenanceTel=").append(maintenanceTel);
        sb.append(", repairDepotLongitude=").append(repairDepotLongitude);
        sb.append(", repairDepotLatitude=").append(repairDepotLatitude);
        sb.append(", vehicleModelAllFlag=").append(vehicleModelAllFlag);
        sb.append(", ssoCreateTime=").append(ssoCreateTime);
        sb.append(", ssoUpdateTime=").append(ssoUpdateTime);
        sb.append(", oldRepairFactoryCode=").append(oldRepairFactoryCode);
        sb.append(", status=").append(status);
        sb.append(", delFlag=").append(delFlag);
        sb.append(", remark=").append(remark);
        sb.append(", ssoFlag=").append(ssoFlag);
        sb.append(", canRepairItem=").append(canRepairItem);
        sb.append(", warrantyPoint=").append(warrantyPoint);
        sb.append(", businessType=").append(businessType);
        sb.append(", businessStartTime=").append(businessStartTime);
        sb.append(", businessEndTime=").append(businessEndTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}