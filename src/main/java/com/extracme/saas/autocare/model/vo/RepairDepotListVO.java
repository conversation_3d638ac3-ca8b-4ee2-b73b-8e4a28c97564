package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "修理厂信息列表对象")
public class RepairDepotListVO {

    @ApiModelProperty(value = "ID", example = "12345")
    private Long id;

    @ApiModelProperty(value = "主要合作子公司组织机构名称", example = "环球车享")
    private String mainOrgName;

    @ApiModelProperty(value = "修理厂名称", example = "修理厂A")
    private String repairDepotName;

    @ApiModelProperty(value = "修理厂ID", example = "RD12345")
    private String repairDepotId;

    @ApiModelProperty(value = "修理厂账号", example = "account123")
    private String repairDepotAccount;

    @ApiModelProperty(value = "修理厂类型(0-合作修理厂 1-非合作修理厂)", example = "0")
    private Integer repairDepotType;

    @ApiModelProperty(value = "修理厂等级", example = "A")
    private String repairDepotGrade;

    @ApiModelProperty(value = "是否保养点(否:0 是:1)", example = "1")
    private Integer maintenancePoint;

    @ApiModelProperty(value = "其他合作子公司组织机构名称", example = "ORG12346")
    private String otherOrgName;

    @ApiModelProperty(value = "维修厂可修理车型车型", example = "车型A,车型B")
    private String vehicleModel;

    @ApiModelProperty(value = "合作模式(1:长租 2：分时 3：短租)", example = "1")
    private String cooperationMode;

    @ApiModelProperty(value = "合作模式名称", example = "长租")
    private String cooperationModeName;

    @ApiModelProperty(value = "修理厂详细地址", example = "详细地址A")
    private String address;


    @ApiModelProperty(value = "负责人", example = "张三")
    private String linkmanName;

    @ApiModelProperty(value = "修理厂状态(1-有效 0-无效)", example = "1")
    private Integer status;


}