package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "维修图片明细DTO")
public class RepairPicturesDTO {

    @ApiModelProperty(value = "行驶证图片", notes = "对应picType=10")
    private List<String> drivingLicensePicture;

    @ApiModelProperty(value = "保单图片", notes = "对应picType=11")
    private List<String> policyPicture;

    @ApiModelProperty(value = "事故图片", notes = "对应picType=12")
    private List<String> accidentPicture;

    @ApiModelProperty(value = "车损图片(标的车)", notes = "对应picType=13")
    private List<String> damageAPicture;

    @ApiModelProperty(value = "车损图片(三者车)", notes = "对应picType=14")
    private List<String> damageBPicture;

    @ApiModelProperty(value = "理赔材料", notes = "对应picType=15")
    private List<String> claimsPicture;

    @ApiModelProperty(value = "其他图片", notes = "对应picType=16")
    private List<String> otherPicture;

    @ApiModelProperty(value = "损坏部位视频", notes = "对应picType=18")
    private List<String> damagedPartVideo;

    @ApiModelProperty(value = "维修后图片", notes = "对应picType=19")
    private List<String> afterPic;

    @ApiModelProperty(value = "事故责任认定书图片", notes = "对应picType=20")
    private List<String> accidentLiabilityConfirmationPicture;

    @ApiModelProperty(value = "保司定损单图片", notes = "对应picType=21")
    private List<String> insuranceCompanyLossOrderPicture;

    @ApiModelProperty(value = "我方驾驶证图片", notes = "对应picType=22")
    private List<String> ourDriverLicensePicture;

    @ApiModelProperty(value = "客户直付凭证", notes = "对应picType=23")
    private List<String> custPicture;

    @ApiModelProperty(value = "损坏部位图片", notes = "对应picType=1")
    private List<String> damagedPartPicture;

    @ApiModelProperty(value = "维修图片（验收图片）", notes = "对应picType=2")
    private List<String> repairPicture;

    @ApiModelProperty(value = "验收视频", notes = "对应picType=17")
    private List<String> checkVideo;
}