package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@ApiModel(description = "修理厂信息详情对象")
public class RepairDepotDetailsVO {

    @ApiModelProperty(value = "维修厂ID不能为空")
    private Long id;

    @ApiModelProperty(value = "修理厂名称", example = "修理厂A")
    private String repairDepotName;

    @ApiModelProperty(value = "是否保养点(0-否 1-是)", example = "1")
    private Integer maintenancePoint;

    @ApiModelProperty(value = "修理厂等级", example = "A")
    private String repairDepotGrade;

    @ApiModelProperty(value = "主要合作子公司组织机构ID", example = "ORG12345")
    private String mainOrgId;

    @ApiModelProperty(value = "其他组织机构", example = "ORG12346")
    private List<String> otherOrgIdList;

    @ApiModelProperty(value = "修理厂地址（省）ID")
    private Long provinceId;

    @ApiModelProperty(value = "修理厂地址（市）ID")
    private Long cityId;

    @ApiModelProperty(value = "修理厂地址（区）ID")
    private Long areaId;

    @ApiModelProperty(value = "修理厂详细地址", example = "详细地址A")
    private String address;

    @ApiModelProperty(value = "事故联系人", example = "李四")
    private String accidentContacts;

    @ApiModelProperty(value = "事故联系电话", example = "12345678901")
    private String accidentTel;

    @ApiModelProperty(value = "维保联系人", example = "王五")
    private String maintenanceContacts;

    @ApiModelProperty(value = "维保联系电话", example = "12345678902")
    private String maintenanceTel;

    @ApiModelProperty(value = "修理厂经度", example = "120.123456")
    private String repairDepotLongitude;

    @ApiModelProperty(value = "修理厂纬度", example = "30.123456")
    private String repairDepotLatitude;

    @ApiModelProperty(value = "状态（ 0-无效 1-有效）", example = "1")
    private Integer status;

    @ApiModelProperty(value = "负责人", example = "张三")
    private String linkmanName;

    @ApiModelProperty(value = "可修类目(0-全部 1-外观 2-易损件)", example = "0")
    private Integer canRepairItem;

    @ApiModelProperty(value = "营业时间类型(0-全天 1-其他)", example = "1")
    private Integer businessType;

    @ApiModelProperty(value = "营业开始时间", example = "09:00")
    private String businessStartTime;

    @ApiModelProperty(value = "营业结束时间", example = "18:00")
    private String businessEndTime;

    @ApiModelProperty(value = "是否保修点(0-否 1-是)", example = "1")
    private Integer warrantyPoint;

    @ApiModelProperty(value = "维修厂可修理车型车型表", example = "车型A,车型B")
    private List<Long> vehicleModelList;

    @ApiModelProperty(value = "维修厂可保修车型车型表", example = "车型A,车型B")
    private List<Long> warrantyVehicleModelList;

    @ApiModelProperty(value = "合作模式(1-长租 2-分时 3-短租)")
    private List<Integer> cooperationModeList;

    @ApiModelProperty(value = "车型是否全选标记 (0-没有全选 1-全部选中)", example = "1")
    private Integer vehicleModelAllFlag;

    @ApiModelProperty(value = "供应商编码-sap", example = "SAP12345")
    private String repairDepotSapCode;

    @ApiModelProperty(value = "供应商名", example = "供应商A")
    private String repairDepotSupplierName;

    @ApiModelProperty(value = "税率", example = "0.13")
    @NotBlank(message = "税率不能为空")
    private String taxRate;
}