package com.extracme.saas.autocare.model.dto;

import java.math.BigDecimal;

import lombok.Data;

/**
 * 核损核价审核DTO
 */
@Data
public class LossAssessmentApproveDTO {
    
    /**
     * 任务编号
     */
    private String taskNo;
    
    /**
     * 定损单金额
     */
    private BigDecimal lossOrderAmount;
    
    /**
     * 维修保险总金额
     */
    private BigDecimal repairInsuranceTotalAmount;
    
    /**
     * 客户是否直付 1:是 2:否
     */
    private Integer custPaysDirect;
    
    /**
     * 客户支付金额
     */
    private BigDecimal custAmount;
    
    /**
     * 审核备注
     */
    private String approvalRemark;

    /**
     *   车管核价意见 0:同意报价 1:异议
     */
    private Short vehicleManageViewFlag;

    private Integer checkFlag;

    /**
     * 是否需要复勘 1:是 2:否
     */
    private String resurveyFlag;
    
    /**
     * 复勘部位
     */
    private String resurveyPart;
}
