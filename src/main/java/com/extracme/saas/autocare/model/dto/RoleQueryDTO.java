package com.extracme.saas.autocare.model.dto;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "角色查询DTO")
public class RoleQueryDTO extends BasePageDTO {
    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "租户ID", hidden = true)
    private Long tenantId;
}