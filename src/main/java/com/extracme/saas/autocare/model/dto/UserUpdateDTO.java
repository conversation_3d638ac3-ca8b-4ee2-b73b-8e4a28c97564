package com.extracme.saas.autocare.model.dto;

import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;
import io.swagger.annotations.ApiModelProperty;

/**
 * 用户更新DTO
 */
@Data
public class UserUpdateDTO {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @Positive(message = "用户ID必须大于0")
    @ApiModelProperty(value = "用户ID", required = true, example = "1")
    private Long id;

    /**
     * 昵称，2-20位字符
     */
    @Size(min = 2, max = 20, message = "昵称长度必须在2-20位之间")
    @ApiModelProperty(value = "昵称，2-20位字符", example = "张三")
    private String nickname;

    /**
     * 手机号，11位
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @ApiModelProperty(value = "手机号，11位", example = "13800138000")
    private String mobile;

    /**
     * 邮箱地址
     */
    @Email(message = "邮箱格式不正确")
    @ApiModelProperty(value = "邮箱地址", example = "<EMAIL>")
    private String email;

    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID", example = "ORG001")
    private String orgId;

    /**
     * 角色ID列表
     */
    @ApiModelProperty(value = "角色ID列表", example = "[1, 2]")
    private List<Long> roleIds;

    /**
     * 状态：0-禁用，1-启用
     */
    @Min(value = 0, message = "状态值不正确")
    @Max(value = 1, message = "状态值不正确")
    @ApiModelProperty(value = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

    /**
     * 审批层级（0：无，1：一级，2：二级，3：三级，4：四级）
     */
    @Min(value = 0, message = "审批层级最小值为0")
    @Max(value = 4, message = "审批层级最大值为4")
    @ApiModelProperty(value = "审批层级（0：无，1：一级，2：二级，3：三级，4：四级）", example = "1")
    private Integer approvalLevel;

    /**
     * 关联维修站点ID
     */
    @ApiModelProperty(value = "关联维修站点ID，用于维修站点类型用户", example = "DEPOT001")
    private String repairDepotId;

    /**
     * 账号类型：0-普通，1-修理厂
     */
    @Min(value = 0, message = "账号类型值不正确")
    @Max(value = 2, message = "账号类型值不正确")
    @ApiModelProperty(value = "账号类型：0-超级管理员 1-运营人员 2-修理厂", example = "0")
    private Integer accountType;

    /**
     * 租户ID（可选，仅超级管理员可指定，普通用户自动设置为当前租户）
     */
    @ApiModelProperty(value = "租户ID（可选，仅超级管理员可指定，普通用户自动设置为当前租户）", example = "1")
    private Long tenantId;
}
