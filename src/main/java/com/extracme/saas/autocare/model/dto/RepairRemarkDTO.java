package com.extracme.saas.autocare.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 维修备注数据传输对象
 */
@Data
@ApiModel(description = "维修备注DTO")
public class RepairRemarkDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "主键ID")
    private Long id;
    
    @ApiModelProperty(value = "任务编号")
    private String taskNo;
    
    @ApiModelProperty(value = "维修阶段", notes = "1:定损报价 2:核损核价(一级审核) 3:核损核价(审核通过) 4:车辆维修 5:车辆验收 6:轮胎任务")
    private Short repairStage;
    
    @ApiModelProperty(value = "备注")
    private String remark;
    
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    @ApiModelProperty(value = "创建时间")
    private Date createdTime;
    
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    
    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;
}