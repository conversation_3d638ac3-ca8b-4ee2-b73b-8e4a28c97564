package com.extracme.saas.autocare.model.dto;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "本地维修项目列表查询DTO")
public class RepairItemLibraryLocalQueryDTO extends BasePageDTO {

    @ApiModelProperty(value = "项目ID")
    @NotNull(message = "项目ID不能为空")
    private Long itemId;

    @ApiModelProperty(value = "项目名称")
    private String orgId;

    @ApiModelProperty(value = "状态(0-无效 1-有效)")
    private Integer status;

}