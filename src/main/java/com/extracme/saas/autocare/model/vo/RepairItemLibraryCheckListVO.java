package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "定损配件库列表信息")
public class RepairItemLibraryCheckListVO {

    @ApiModelProperty(value = "项目id")
    private Long itemId;

    @ApiModelProperty(value = "项目编号")
    private String itemNo;

    @ApiModelProperty(value = "项目名称")
    private String itemName;

    @ApiModelProperty(value = "项目类型 1：保养 2：终端 3：维修")
    private Integer itemType;

    /**
     * 项目数量 默认为1
     */
    private Integer itemNumber = 1;

    @ApiModelProperty(value = "材料费")
    private BigDecimal materialCostMarketPrice;

    @ApiModelProperty(value = "工时费")
    private BigDecimal hourFeeMarketPrice;


    private BigDecimal hourFeeNationalMarketPrice;

    private BigDecimal materialCostNationalMarketPrice;

    private BigDecimal hourFeeLocalMarketPrice;

    private BigDecimal materialCostLocalMarketPrice;
}
