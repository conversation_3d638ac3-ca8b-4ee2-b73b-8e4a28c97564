package com.extracme.saas.autocare.model.dto.repairTask;

import java.math.BigDecimal;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.extracme.saas.autocare.model.dto.ViewRepairItemDetailDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "维修任务更新DTO")
public class RepairTaskUpdateDTO {
    @ApiModelProperty(value = "任务id")
    private String taskId;
    
    @ApiModelProperty(value = "任务编号")
    private String taskNo;
    
    @ApiModelProperty(value = "修理类型ID 1:事故维修 2:自费维修 3:车辆保养")
    private String repairTypeId;
    
    @ApiModelProperty(value = "事故报案号")
    private String accidentReportNumber;
    
    @ApiModelProperty(value = "预计修理天数")
    private Long expectedRepairDays;
    
    @ApiModelProperty(value = "修理厂换件金额合计")
    private String repairReplaceTotalAmount;
    
    @ApiModelProperty(value = "修理厂修理金额合计")
    private String repairRepairTotalAmount;
    
    @ApiModelProperty(value = "修理厂定损总计")
    private String repairInsuranceTotalAmount;
    
    @ApiModelProperty(value = "车管换件金额合计")
    private String vehicleReplaceTotalAmount;
    
    @ApiModelProperty(value = "车管修理金额合计")
    private String vehicleRepairTotalAmount;
    
    @ApiModelProperty(value = "车管定损总计")
    private String vehicleInsuranceTotalAmount;
    
    @ApiModelProperty(value = "是否需要维修 0:否 1:是")
    private String repairFlag;
    
    @ApiModelProperty(value = "保养费用")
    private String maintainAmount;
    
    @ApiModelProperty(value = "是否需要复勘 0:否 1:是")
    private String resurveyFlag;
    
    @ApiModelProperty(value = "复勘部位")
    private String resurveyPart;
    
    @ApiModelProperty(value = "责任情况 1:全责 2:主责 3:次责 4:平责 5:无责")
    private String dutySituation;
    
    @ApiModelProperty(value = "向用户追偿费用")
    private String recoveryAmount;
    
    @ApiModelProperty(value = "保险上付费")
    private String insuranceAmount;
    
    @ApiModelProperty(value = "加速折旧费")
    private String accDepAmount;
    
    @ApiModelProperty(value = "停运损失费")
    private String outageLossAmount;
    
    @ApiModelProperty(value = "车辆损失费")
    private String vehicleLossAmount;
    
    @ApiModelProperty(value = "拖车救援费")
    private String trailerRescueAmount;
    
    @ApiModelProperty(value = "定损单金额")
    private String lossOrderAmount;
    
    @ApiModelProperty(value = "损坏部位图片")
    private List<String> damagedPartPicture;
    
    @ApiModelProperty(value = "损坏部位视频")
    private List<String> damagedPartVideo;

    @ApiModelProperty(value = "维修图片（验收图片）")
    private List<String> repairPicture;
    
    @ApiModelProperty(value = "事故责任认定书图片")
    private List<String> accidentLiabilityConfirmationPicture;
    
    @ApiModelProperty(value = "保司定损单图片")
    private List<String> insuranceCompanyLossOrderPicture;
    
    @ApiModelProperty(value = "我方驾驶证图片")
    private List<String> ourDriverLicensePicture;

    @ApiModelProperty(value = "验收视频")
    private List<String> checkVideo;

    @ApiModelProperty(value = "超时原因")
    private String overTimeReasons;
    
    @ApiModelProperty(value = "申请改派原因")
    private String reassignmentReasons;
    
    @ApiModelProperty(value = "是否保险理赔状态")
    private Integer claimsFlag;
    
    @ApiModelProperty(value = "预估保险理赔价格")
    private BigDecimal estimatedClaimAmount;
    
    @ApiModelProperty(value = "修理项目明细")
    private List<ViewRepairItemDetailDTO> repairItemDetail;
    
    @ApiModelProperty(value = "换件项目明细")
    private List<ViewRepairItemDetailDTO> replaceItemDetail;
    
    @ApiModelProperty(value = "车管核价意见 0:同意报价 1:异议")
    private String vehicleManageViewFlag;
    
    @ApiModelProperty(value = "确认类型 1:公司自费 2:客户付费 3:我方有责 4:我方无责")
    private Integer confirmType;
    
    @ApiModelProperty(value = "确认车损类型 1:我司承担 2:客户承担")
    private Integer confirmCarDamageType;
    
    @ApiModelProperty(value = "最后修改时间")
    private String updateEndTime;
    
    @ApiModelProperty(value = "是否使用小程序 0-否 1-是")
    private String isUsedApplets;
    
    @ApiModelProperty(value = "是否客户直付 1-是 2-否")
    private Integer custPaysDirect;
    
    @ApiModelProperty(value = "客户直付金额")
    private BigDecimal custAmount;
    
    @ApiModelProperty(value = "客户直付凭证")
    private List<String> custPicture;

    @ApiModelProperty(value = "是否校验 0-默认校验 1-不校验")
    private int checkFlag;

    @ApiModelProperty(value = "核损核价级别")
    private Integer advancedAuditLeve; 

    /**
     * 检查图片数量是否超出范围
     * @return 是否超出范围
     */
    @ApiModelProperty(hidden = true)
    public boolean pictureSizeOutOfRange() {
        if (CollectionUtils.isNotEmpty(damagedPartPicture) && damagedPartPicture.size() > 48) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(repairPicture) && repairPicture.size() > 48) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(accidentLiabilityConfirmationPicture)
                && accidentLiabilityConfirmationPicture.size() > 4) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(insuranceCompanyLossOrderPicture)
                && insuranceCompanyLossOrderPicture.size() > 4) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(ourDriverLicensePicture) && ourDriverLicensePicture.size() > 4) {
            return true;
        }

        return false;
    }
}
