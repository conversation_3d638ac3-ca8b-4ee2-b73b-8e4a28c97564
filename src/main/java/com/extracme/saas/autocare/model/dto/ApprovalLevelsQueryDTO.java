package com.extracme.saas.autocare.model.dto;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 审批层级查询DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "审批层级查询DTO")
public class ApprovalLevelsQueryDTO extends BasePageDTO {

    @ApiModelProperty(value = "审批层级（0：无，1：一级，2：二级，3：三级，4：四级）", example = "1")
    private Integer approvalLevel;

    @ApiModelProperty(value = "最小自主审批金额", example = "1000.00")
    private BigDecimal minSelfApprovalAmount;

    @ApiModelProperty(value = "最大自主审批金额", example = "50000.00")
    private BigDecimal maxSelfApprovalAmount;
}
