package com.extracme.saas.autocare.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 维修厂首页统计信息VO
 */
@Data
@ApiModel(description = "维修厂首页统计信息")
public class RepairDepotFirstPageInfoVO {
    
    @ApiModelProperty(value = "修理厂ID", example = "12345")
    private String repairDepotId;

    @ApiModelProperty(value = "修理厂名称", example = "某某快修店")
    private String repairDepotName;

    @ApiModelProperty(value = "当前在修车辆总数", example = "15")
    private Long currentRepairNum;

    @ApiModelProperty(value = "今日新增车辆数", example = "5")
    private Long todayAddNum;

    @ApiModelProperty(value = "在修A类任务数", example = "3")
    private Long aRepairNum;

    @ApiModelProperty(value = "在修B类任务数", example = "7")
    private Long bRepairNum;

    @ApiModelProperty(value = "在修C类任务数", example = "5")
    private Long cRepairNum;

    @ApiModelProperty(value = "今日出厂车辆数", example = "2")
    private Long todayOutNum;

    @ApiModelProperty(value = "尚未接车数量", example = "1")
    private Long noReciveNum;
}