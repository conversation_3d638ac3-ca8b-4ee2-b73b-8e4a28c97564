package com.extracme.saas.autocare.model.dto;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "客户付款信息DTO")
public class CustomerPaymentDTO {

    @ApiModelProperty(value = "是否客户直付(1-是 2-否)", example = "1")
    private Integer custPaysDirect;

    @ApiModelProperty(value = "客户直付金额", example = "500.00")
    private BigDecimal custAmount;

    @ApiModelProperty(value = "用户承担金额", example = "300.00")
    private BigDecimal userAssumedAmount;

    @ApiModelProperty(value = "用户承担金额", example = "300.00")
    private BigDecimal notUserAssumedAmount;

    @ApiModelProperty(value = "自费金额", example = "200.00")
    private BigDecimal selfFundedAmount;
}