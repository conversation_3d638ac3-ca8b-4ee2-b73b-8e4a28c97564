package com.extracme.saas.autocare.workflow.handler;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.workflow.core.WorkflowContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 核损核价审核通过处理器
 * 处理核损核价活动节点审核通过流转至车辆维修的逻辑
 */
@Slf4j
@Component
public class ApproveQuoteHandler extends AbstractEventHandler {

    /**
     * 事件类型：审核通过
     */
    private static final String EVENT_TYPE_APPROVE_QUOTE = "APPROVE_QUOTE";

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    /**
     * 验证核损核价状态
     *
     * @param businessId 业务ID
     * @throws WorkflowException 如果验证失败
     */
    private void validateApprovalStatus(String businessId) throws WorkflowException {
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(businessId);
        if (null == workflowInstance) {
            throw new WorkflowException(-1, "未找到实例：" + businessId);
        }
        // 验证当前实例是否处于核损核价环节
        if (!workflowInstance.getCurrentActivityCode().equals("LOSS_ASSESSMENT")) {
            throw new WorkflowException(-1, "当前实例活动节点不允许核损核价审核通过");
        }
        // 验证是否核损核价的处理中状态
        if (!workflowInstance.getStatusCode().equals("PROCESSING")) {
            throw new WorkflowException(-1, "当前实例状态不允许核损核价审核通过");
        }
    }

    /**
     * 保存修理项目明细
     *
     * @param mtcRepairTask 维修任务
     */
    private void saveRepairItems(MtcRepairTask mtcRepairTask) {
        // 判断是否使用自有配件库
        boolean isOwnPartsSystem = false;
        if (isOwnPartsSystem) {
            // 保存修理项目明细
            log.info("保存修理项目明细，任务ID：{}", mtcRepairTask.getId());
            // 实际实现中需要调用相应的服务保存修理项目明细
        }
    }

    /**
     * 保存换件项目明细
     *
     * @param mtcRepairTask 维修任务
     */
    private void saveReplacementItems(MtcRepairTask mtcRepairTask) {
        // 判断是否使用自有配件库
        boolean isOwnPartsSystem = false;
        if (isOwnPartsSystem) {
            // 保存换件项目明细
            log.info("保存换件项目明细，任务ID：{}", mtcRepairTask.getId());
            // 实际实现中需要调用相应的服务保存换件项目明细
        }
    }

    /**
     * 业财对接，同步自费金额账单
     *
     * @param mtcRepairTask 维修任务
     */
    private void syncFinancialSystem(MtcRepairTask mtcRepairTask) {
        // 同步自费金额账单到财务系统
        log.info("同步自费金额账单到财务系统，任务ID：{}", mtcRepairTask.getId());
        // 实际实现中需要调用财务系统API
    }

    /**
     * 检查是否有提前提车任务
     *
     * @param mtcRepairTask 维修任务
     * @return 是否有提前提车任务
     */
    private boolean checkEarlyPickup(MtcRepairTask mtcRepairTask) {
        // 检查是否有提前提车任务
        log.info("检查是否有提前提车任务，任务ID：{}", mtcRepairTask.getId());
        // 实际实现中需要查询相关数据
        return false;
    }

    /**
     * 通知外部系统
     *
     * @param businessId 业务ID
     */
    private void notifyExternalSystems(String businessId) {
        // 通知相关系统核损核价已审核通过
        log.info("通知外部系统核损核价已审核通过：{}", businessId);
    }

    @Override
    public String getSupportedEventType() {
        return EVENT_TYPE_APPROVE_QUOTE;
    }

    @Override
    public Integer getSupportedTaskType() {
        return -1; // 支持所有任务类型
    }

    @Override
    public String getHandlerName() {
        return "核损核价-审核通过处理器";
    }

    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        WorkflowInstance instance = context.getWorkflowInstance();
        String businessId = instance.getBusinessId();
        LoginUser loginUser = SessionUtils.getLoginUser();

        try {
            // 1. 状态校验
            validateApprovalStatus(businessId);

            // 2. 获取维修任务
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(businessId);
            if (mtcRepairTask == null) {
                throw new WorkflowException(-1, "未找到维修任务：" + businessId);
            }

            // 3. 保存修理项目明细
            saveRepairItems(mtcRepairTask);

            // 4. 保存换件项目明细
            saveReplacementItems(mtcRepairTask);

            // 5. 业财对接
            syncFinancialSystem(mtcRepairTask);

            // 6. 记录操作日志
            MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
            mtcOperatorLog.setUpdateBy(loginUser.getUsername());
            mtcOperatorLog.setUpdatedTime(new Date());
            mtcOperatorLog.setCreateBy(loginUser.getUsername());
            mtcOperatorLog.setCreatedTime(new Date());
            mtcOperatorLog.setRemark("核损核价");
            mtcOperatorLog.setRecordId(mtcRepairTask.getId());
            
            // 构建操作内容
            String opeContent = "审核通过，核损金额为：" + mtcRepairTask.getLossOrderAmount() + "元";
            if (null != mtcRepairTask.getCustPaysDirect() && mtcRepairTask.getCustPaysDirect() > 0) {
                if (mtcRepairTask.getCustPaysDirect() == 1) {
                    opeContent = opeContent + "，客户直付：是";
                } else if (mtcRepairTask.getCustPaysDirect() == 2) {
                    opeContent = opeContent + "，客户直付：否";
                }
                
                if (null != mtcRepairTask.getCustAmount()) {
                    opeContent = opeContent + "，金额：" + mtcRepairTask.getCustAmount() + "元";
                }
            }
            
            mtcOperatorLog.setOpeContent(opeContent);
            mtcOperatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            mtcOperatorLog.setCurrentActivityCode(instance.getCurrentActivityCode());
            tableOperatorLogService.insertSelective(mtcOperatorLog);

            // 7. 检查是否有提前提车任务
            boolean hasEarlyPickup = checkEarlyPickup(mtcRepairTask);
            
            // 8. 添加车辆维修流程日志
            MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
            mtcProcessLog.setUpdateBy(loginUser.getUsername());
            mtcProcessLog.setUpdatedTime(new Date());
            mtcProcessLog.setCreateBy(loginUser.getUsername());
            mtcProcessLog.setCreatedTime(new Date());
            mtcProcessLog.setRemark("车辆维修");
            mtcProcessLog.setRecordId(mtcRepairTask.getId());
            mtcProcessLog.setOpeContent(hasEarlyPickup ? "提前提车" : "维修中");
            mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            mtcProcessLog.setCurrentActivityCode("IN_REPAIR"); // 下一个环节为车辆维修
            tableOperatorLogService.insertSelective(mtcProcessLog);

            // 9. 通知外部系统
            notifyExternalSystems(businessId);
            
        } catch (Exception e) {
            log.error("处理核损核价审核通过事件失败：{}", e.getMessage(), e);
            if (e instanceof WorkflowException) {
                throw (WorkflowException) e;
            }
            throw new WorkflowException(-1, "处理核损核价审核通过事件失败：" + e.getMessage());
        }
    }
}