package com.extracme.saas.autocare.workflow.handler;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.ActivityStatusEnum;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.workflow.core.WorkflowContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 核损核价清除占据人处理器
 * 处理核损核价活动节点清除任务占据人的逻辑
 */
@Slf4j
@Component
public class ClearLossAssessmentOwnerHandler extends AbstractEventHandler {

    /**
     * 事件类型：清除核损核价占据人
     */
    private static final String EVENT_TYPE_CLEAR_LOSS_ASSESSMENT_OWNER = "CLEAR_LOSS_ASSESSMENT_OWNER";

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    /**
     * 验证节点状态
     *
     * @param businessId 业务ID
     * @throws WorkflowException 如果验证失败
     */
    private void validateLossAssessmentStatus(String businessId) throws WorkflowException {
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(businessId);
        if (null == workflowInstance) {
            throw new WorkflowException(-1, "未找到实例：" + businessId);
        }
        // 验证当前实例是否处于核损核价环节
        if (!workflowInstance.getCurrentActivityCode().equals("LOSS_ASSESSMENT")) {
            throw new WorkflowException(-1, "当前实例活动节点不允许清除核损核价占据人");
        }
        // 验证是否核损核价的处理中状态
        if (!workflowInstance.getStatusCode().equals(ActivityStatusEnum.PROCESSING.getCode())) {
            throw new WorkflowException(-1, "只有处理中的任务才能清除任务占据人");
        }
    }

    @Override
    public String getSupportedEventType() {
        return EVENT_TYPE_CLEAR_LOSS_ASSESSMENT_OWNER;
    }

    @Override
    public Integer getSupportedTaskType() {
        return -1; // 支持所有任务类型
    }

    @Override
    public String getHandlerName() {
        return "核损核价-清除占据人处理器";
    }

    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        WorkflowInstance instance = context.getWorkflowInstance();
        String businessId = instance.getBusinessId();
        LoginUser loginUser = SessionUtils.getLoginUser();

        try {
            log.info("开始清除核损核价占据人，业务ID：{}", businessId);
            // 获取维修任务
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(businessId);
            if (mtcRepairTask == null) {
                throw new WorkflowException(-1, "未找到维修任务：" + businessId);
            }

            // 1. 状态校验
            validateLossAssessmentStatus(businessId);

            // 2. 清除任务占据人
            tableRepairTaskService.clearOwner(mtcRepairTask.getId(), loginUser.getUsername());

            // 3. 记录操作日志
            MtcOperatorLog operatorLog = new MtcOperatorLog();
            operatorLog.setUpdateBy(loginUser.getUsername());
            operatorLog.setUpdatedTime(new Date());
            operatorLog.setCreateBy(loginUser.getUsername());
            operatorLog.setCreatedTime(new Date());
            operatorLog.setRemark("核损核价");
            operatorLog.setOpeContent((String)context.getExtraParams());
            operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            operatorLog.setCurrentActivityCode(instance.getCurrentActivityCode());
            operatorLog.setRecordId(mtcRepairTask.getId());

            tableOperatorLogService.insertSelective(operatorLog);
            log.info("核损核价占据人已清除，业务ID：{}", businessId);
        } catch (Exception e) {
            log.error("处理清除核损核价占据人事件失败：{}", e.getMessage(), e);
            if (e instanceof WorkflowException) {
                throw (WorkflowException) e;
            }
            throw new WorkflowException(-1, "处理清除核损核价占据人事件失败：" + e.getMessage());
        }
    }
}