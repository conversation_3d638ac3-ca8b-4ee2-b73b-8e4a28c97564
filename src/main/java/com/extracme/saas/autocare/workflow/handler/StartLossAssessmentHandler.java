package com.extracme.saas.autocare.workflow.handler;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.ActivityStatusEnum;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.workflow.core.WorkflowContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 开始核损核价处理器
 * 处理核损核价活动节点从未处理状态转为处理中状态的逻辑
 */
@Slf4j
@Component
public class StartLossAssessmentHandler extends AbstractEventHandler {

    /**
     * 事件类型：开始核损核价
     */
    private static final String EVENT_TYPE_START_LOSS_ASSESSMENT = "START_LOSS_ASSESSMENT";

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    /**
     * 验证节点状态
     *
     * @param businessId 业务ID
     * @throws WorkflowException 如果验证失败
     */
    private void validateLossAssessmentStatus(String businessId) throws WorkflowException {
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(businessId);
        if (null == workflowInstance) {
            throw new WorkflowException(-1, "未找到实例：" + businessId);
        }
        // 验证当前实例是否处于核损核价环节
        if (!workflowInstance.getCurrentActivityCode().equals("LOSS_ASSESSMENT")) {
            throw new WorkflowException(-1, "当前实例活动节点不允许开始核损核价");
        }
        // 验证是否核损核价的未处理状态
        if (!workflowInstance.getStatusCode().equals(ActivityStatusEnum.UNPROCESSED.getCode())) {
            throw new WorkflowException(-1, "当前实例状态不允许开始核损核价");
        }
    }

    @Override
    public String getSupportedEventType() {
        return EVENT_TYPE_START_LOSS_ASSESSMENT;
    }

    @Override
    public Integer getSupportedTaskType() {
        return -1; // 支持所有任务类型
    }

    @Override
    public String getHandlerName() {
        return "核损核价-开始处理器";
    }

    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        WorkflowInstance instance = context.getWorkflowInstance();
        String businessId = instance.getBusinessId();
        LoginUser loginUser = SessionUtils.getLoginUser();

        try {
            log.info("开始处理核损核价，业务ID：{}", businessId);
            // 获取维修任务ID
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(businessId);
            if (mtcRepairTask == null) {
                throw new WorkflowException(-1, "未找到维修任务：" + businessId);
            }

            // 1. 状态校验
            validateLossAssessmentStatus(businessId);

            // 2. 更新任务占据人
            MtcRepairTask update = new MtcRepairTask();
            update.setId(mtcRepairTask.getId());
            update.setVerificationLossCheckId(SessionUtils.getUserId());
            update.setUpdateBy(SessionUtils.getUsername());
            update.setUpdatedTime(new Date());
            tableRepairTaskService.updateSelectiveById(update);

            // 3. 记录操作日志
            MtcOperatorLog operatorLog = new MtcOperatorLog();
            operatorLog.setUpdateBy(loginUser.getUsername());
            operatorLog.setUpdatedTime(new Date());
            operatorLog.setCreateBy(loginUser.getUsername());
            operatorLog.setCreatedTime(new Date());
            operatorLog.setRemark("核损核价");
            operatorLog.setOpeContent("开始处理核损核价");
            operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            operatorLog.setCurrentActivityCode(instance.getCurrentActivityCode());
            operatorLog.setRecordId(mtcRepairTask.getId());

            tableOperatorLogService.insertSelective(operatorLog);
            log.info("核损核价状态已更新为处理中，业务ID：{}", businessId);
        } catch (Exception e) {
            log.error("处理开始核损核价事件失败：{}", e.getMessage(), e);
            if (e instanceof WorkflowException) {
                throw (WorkflowException) e;
            }
            throw new WorkflowException(-1, "处理开始核损核价事件失败：" + e.getMessage());
        }
    }
}