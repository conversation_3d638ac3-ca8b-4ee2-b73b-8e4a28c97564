package com.extracme.saas.autocare.workflow.handler;

import com.extracme.saas.autocare.workflow.core.WorkflowContext;
import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.TaskTypeEnum;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 事故维修事件处理器
 * 处理事故维修类型的工作流事件
 *
 * <AUTHOR>
 * @date 2024/05/15
 */
@Slf4j
@Component
public class AccidentRepairEventHandler extends AbstractEventHandler {

    /**
     * 事件类型：完成交接
     */
    private static final String EVENT_TYPE_COMPLETE_HANDOVER = "accident_COMPLETE_HANDOVER";

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        // 获取当前流程实例
        WorkflowInstance instance = context.getWorkflowInstance();
        String businessId = instance.getBusinessId();

        log.info("事故维修事件处理器处理事件: {}, 业务ID: {}", getSupportedEventType(), businessId);

        try {
            // 1. 状态校验
            validateStatus(businessId);

            // 2. 获取维修任务
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(businessId);
            if (mtcRepairTask == null) {
                throw new WorkflowException(-1, "未找到维修任务: " + businessId);
            }

            // 3. 更新维修接车时间状态
            mtcRepairTask.setVehicleReciveTime(new Date()); // 使用车辆接收时间表示交接完成
            tableRepairTaskService.updateSelectiveById(mtcRepairTask);

            // 4. 记录操作日志
            MtcOperatorLog operatorLog = new MtcOperatorLog();
            operatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            operatorLog.setRecordId(Long.parseLong(businessId));
            operatorLog.setOpeContent("事故维修-确认交接");
            operatorLog.setRemark("事故维修流程");
            operatorLog.setCurrentActivityCode(instance.getCurrentActivityCode());
            operatorLog.setCreatedTime(new Date());
            operatorLog.setCreateBy(context.getOperator());
            tableOperatorLogService.insertSelective(operatorLog);

            // 5. 通知外部系统
            notifyExternalSystems(businessId);

        } catch (Exception e) {
            log.error("事故维修事件处理失败: {}", e.getMessage(), e);
            throw new WorkflowException(-1, "事故维修事件处理失败: " + e.getMessage());
        }
    }

    @Override
    public String getSupportedEventType() {
        return EVENT_TYPE_COMPLETE_HANDOVER;
    }

    @Override
    public Integer getSupportedTaskType() {
        return TaskTypeEnum.ACCIDENT_REPAIR.getCode();
    }

    @Override
    public String getHandlerName() {
        return "事故维修事件处理器";
    }

    /**
     * 验证状态
     *
     * @param businessId 业务ID
     */
    private void validateStatus(String businessId) {
        // 验证当前状态是否允许完成交接
        // 例如：检查必要信息是否已填写完整
        // 如果验证失败，抛出 WorkflowException
    }

    /**
     * 通知外部系统
     *
     * @param businessId 业务ID
     */
    private void notifyExternalSystems(String businessId) {
        // 通知梧桐系统
        log.info("通知梧桐系统事故维修交接完成: {}", businessId);

        // 通知出厂登记系统
        log.info("通知出厂登记系统事故维修交接完成: {}", businessId);
    }
}
