package com.extracme.saas.autocare.workflow.handler;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.workflow.core.WorkflowContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 验收通过处理器
 * 处理验收活动节点审核通过流转至车辆交接的逻辑
 */
@Slf4j
@Component
public class ApproveInspectionHandler extends AbstractEventHandler {

    /**
     * 事件类型：验收通过
     */
    private static final String EVENT_TYPE_APPROVE_INSPECTION = "APPROVE_INSPECTION";

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    /**
     * 验证验收状态
     *
     * @param businessId 业务ID
     * @throws WorkflowException 如果验证失败
     */
    private void validateInspectionStatus(String businessId) throws WorkflowException {
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(businessId);
        if (null == workflowInstance) {
            throw new WorkflowException(-1, "未找到实例：" + businessId);
        }
        // 验证当前实例是否处于验收环节
        if (!workflowInstance.getCurrentActivityCode().equals("QUALITY_INSPECTION")) {
            throw new WorkflowException(-1, "当前实例活动节点不允许验收通过");
        }
        // 验证是否验收的处理中状态
        if (!workflowInstance.getStatusCode().equals("PROCESSING")) {
            throw new WorkflowException(-1, "当前实例状态不允许验收通过");
        }
    }

    /**
     * 更新维修任务验收信息
     *
     * @param mtcRepairTask 维修任务
     */
    private void updateInspectionInfo(MtcRepairTask mtcRepairTask) {
        // 更新验收时间和状态
        //mtcRepairTask.setInspectionTime(new Date());
        //mtcRepairTask.setInspectionStatus(1); // 1表示验收通过
        tableRepairTaskService.updateSelectiveById(mtcRepairTask);
        log.info("更新维修任务验收信息，任务ID：{}", mtcRepairTask.getId());
    }

    /**
     * 处理结算信息
     *
     * @param mtcRepairTask 维修任务
     */
    private void processSettlement(MtcRepairTask mtcRepairTask) {
        // 处理结算相关信息
        log.info("处理结算信息，任务ID：{}", mtcRepairTask.getId());
        // 实际实现中需要调用结算服务处理结算信息
    }

    /**
     * 通知外部系统
     *
     * @param businessId 业务ID
     */
    private void notifyExternalSystems(String businessId) {
        // 通知相关系统验收已通过
        log.info("通知外部系统验收已通过：{}", businessId);
    }

    @Override
    public String getSupportedEventType() {
        return EVENT_TYPE_APPROVE_INSPECTION;
    }

    @Override
    public Integer getSupportedTaskType() {
        return -1; // 支持所有任务类型
    }

    @Override
    public String getHandlerName() {
        return "验收-通过处理器";
    }

    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        WorkflowInstance instance = context.getWorkflowInstance();
        String businessId = instance.getBusinessId();
        LoginUser loginUser = SessionUtils.getLoginUser();

        try {
            // 1. 状态校验
            validateInspectionStatus(businessId);

            // 2. 获取维修任务
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(businessId);
            if (mtcRepairTask == null) {
                throw new WorkflowException(-1, "未找到维修任务：" + businessId);
            }

            // 3. 更新验收信息
            updateInspectionInfo(mtcRepairTask);

            // 4. 处理结算信息
            processSettlement(mtcRepairTask);

            // 5. 记录操作日志
            MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
            mtcOperatorLog.setUpdateBy(loginUser.getUsername());
            mtcOperatorLog.setUpdatedTime(new Date());
            mtcOperatorLog.setCreateBy(loginUser.getUsername());
            mtcOperatorLog.setCreatedTime(new Date());
            mtcOperatorLog.setRemark("验收");
            mtcOperatorLog.setRecordId(mtcRepairTask.getId());
            
            // 构建操作内容
            String opeContent = "验收通过，维修质量合格";
            mtcOperatorLog.setOpeContent(opeContent);
            mtcOperatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            mtcOperatorLog.setCurrentActivityCode(instance.getCurrentActivityCode());
            tableOperatorLogService.insertSelective(mtcOperatorLog);

            // 6. 添加车辆交接流程日志
            MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
            mtcProcessLog.setUpdateBy(loginUser.getUsername());
            mtcProcessLog.setUpdatedTime(new Date());
            mtcProcessLog.setCreateBy(loginUser.getUsername());
            mtcProcessLog.setCreatedTime(new Date());
            mtcProcessLog.setRemark("车辆交接");
            mtcProcessLog.setRecordId(mtcRepairTask.getId());
            mtcProcessLog.setOpeContent("待交接");
            mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            mtcProcessLog.setCurrentActivityCode("HANDOVER"); // 下一个环节为车辆交接
            tableOperatorLogService.insertSelective(mtcProcessLog);

            // 7. 通知外部系统
            notifyExternalSystems(businessId);
            
        } catch (Exception e) {
            log.error("处理验收通过事件失败：{}", e.getMessage(), e);
            if (e instanceof WorkflowException) {
                throw (WorkflowException) e;
            }
            throw new WorkflowException(-1, "处理验收通过事件失败：" + e.getMessage());
        }
    }
}