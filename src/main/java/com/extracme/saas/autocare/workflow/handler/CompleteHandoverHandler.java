package com.extracme.saas.autocare.workflow.handler;

import com.extracme.saas.autocare.workflow.core.WorkflowContext;
import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.LeavingStatusEnum;
import com.extracme.saas.autocare.enums.TaskTypeEnum;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.MtcRepairTaskLeavingFactory;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskLeavingFactoryService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CompleteHandoverHandler extends AbstractEventHandler {

    /**
     * 事件类型：完成交接
     */
    private static final String EVENT_TYPE_COMPLETE_HANDOVER = "COMPLETE_HANDOVER";

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Autowired
    private TableRepairTaskLeavingFactoryService tableRepairTaskLeavingFactoryService;

    private void validateHandoverStatus(String businessId) throws WorkflowException {
        // 验证当前实例是否处于车辆交接环节
        // 验证是否车辆交接的未处理状态
        // 如果验证失败，抛出 WorkflowException
    }

    private void notifyExternalSystems(String businessId) {
        // 通知梧桐系统
        // externalSystemNotifier.notifyWutong(businessId, "HANDOVER_COMPLETE");

        // 通知出厂登记系统
        // externalSystemNotifier.notifyFactorySystem(businessId, "HANDOVER_COMPLETE");
    }

    @Override
    public String getSupportedEventType() {
        return EVENT_TYPE_COMPLETE_HANDOVER;
    }

    @Override
    public Integer getSupportedTaskType() {
        return TaskTypeEnum.ACCIDENT_REPAIR.getCode();
    }

    @Override
    public String getHandlerName() {
        return "车辆交接处理器";
    }

    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        WorkflowInstance instance = context.getWorkflowInstance();
        String businessId = instance.getBusinessId();

        try {
            Date currentTime = new Date();

            // 1. 状态校验
            validateHandoverStatus(businessId);

            // 2. 获取维修任务
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(businessId);
            if (mtcRepairTask == null) {
                throw new WorkflowException(-1, "未找到维修任务：" + businessId);   
            }
            
            // 3. 更新维修任务接车时间
            MtcRepairTask updateRepairTask = new MtcRepairTask();
            updateRepairTask.setId(mtcRepairTask.getId());
            updateRepairTask.setVehicleReciveTime(currentTime);
            tableRepairTaskService.updateSelectiveById(updateRepairTask);

            // 4. 记录活动日志
            MtcOperatorLog newOperateLog = new MtcOperatorLog();
            newOperateLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            newOperateLog.setRecordId(mtcRepairTask.getId());
            newOperateLog.setOpeContent("确认接车");
            newOperateLog.setRemark("车辆交接");
            newOperateLog.setCurrentActivityCode(instance.getCurrentActivityCode());
            tableOperatorLogService.insertSelective(newOperateLog);

            // 5. 新增出厂登记
            MtcRepairTaskLeavingFactory newLeavingFactory = new MtcRepairTaskLeavingFactory();
            newLeavingFactory.setTaskNo(mtcRepairTask.getTaskNo());
            newLeavingFactory.setVin(mtcRepairTask.getVin());
            newLeavingFactory.setRepairDepotId(mtcRepairTask.getRepairDepotId());
            newLeavingFactory.setRepairDepotName(mtcRepairTask.getRepairDepotName());
            newLeavingFactory.setRepairDepotOrgId(mtcRepairTask.getRepairDepotOrgId());
            newLeavingFactory.setRepairDepotSapCode(mtcRepairTask.getRepairDepotSapCode());
            newLeavingFactory.setRepairTaskInflowTime(mtcRepairTask.getTaskInflowTime());
            newLeavingFactory.setRepairTaskReceiveTime(currentTime);
            newLeavingFactory.setLeavingStatus(LeavingStatusEnum.NOT_REGISTERED.getCode());
            tableRepairTaskLeavingFactoryService.insert(newLeavingFactory);

            // 5. 通知外部系统
            notifyExternalSystems(businessId);
        } catch (Exception e) {
            // todo 完善WorkflowException
            throw new WorkflowException(-1, "处理车辆交接完成事件失败");
        }
    }
}
