package com.extracme.saas.autocare.workflow.handler;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.ActivityStatusEnum;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.workflow.core.WorkflowContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 申请验收处理器
 * 处理申请验收事件的工作流处理器
 *
 * <AUTHOR>
 * @date 2024/05/20
 */
@Slf4j
@Component
public class RequestForInspectionHandler extends AbstractEventHandler {

    /**
     * 事件类型：申请验收
     */
    private static final String EVENT_TYPE_REQUEST_FOR_INSPECTION = "REQUEST_FOR_INSPECTION";

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    /**
     * 验证申请验收状态
     *
     * @param businessId 业务ID
     * @throws WorkflowException 如果验证失败
     */
    private void validateHandoverStatus(String businessId) throws WorkflowException {
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(businessId);
        if (null == workflowInstance) {
            throw new WorkflowException(-1, "未找到实例：" + businessId);
        }
        // 验证当前实例是否处于车辆维修环节
        if (!workflowInstance.getCurrentActivityCode().equals("IN_REPAIR")) {
            throw new WorkflowException(-1, "当前实例活动节点不允许申请验收");
        }
        // 验证是否车辆维修的未处理状态
        if (!workflowInstance.getStatusCode().equals(ActivityStatusEnum.PROCESSING.getCode())) {
            throw new WorkflowException(-1, "当前实例状态不允许申请验收");
        }
    }

    /**
     * 通知外部系统
     *
     * @param businessId 业务ID
     */
    private void notifyExternalSystems(String businessId) {
        // 同步自费金额账单
        // 梧桐维修任务增加日志
        log.info("通知外部系统申请验收：{}", businessId);
    }

    @Override
    public String getSupportedEventType() {
        return EVENT_TYPE_REQUEST_FOR_INSPECTION;
    }

    @Override
    public Integer getSupportedTaskType() {
        return -1;
    }

    @Override
    public String getHandlerName() {
        return "申请验收处理器";
    }

    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        WorkflowInstance instance = context.getWorkflowInstance();
        String businessId = instance.getBusinessId();
        LoginUser loginUser = SessionUtils.getLoginUser();

        log.info("申请验收处理器处理事件: {}, 业务ID: {}", getSupportedEventType(), businessId);

        try {
            // 1. 状态校验
            validateHandoverStatus(businessId);

            // 2. 获取维修任务
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(businessId);
            if (mtcRepairTask == null) {
                throw new WorkflowException(-1, "未找到维修任务：" + businessId);
            }

            // 3.任务超时判断
            // TODO: 实现任务超时判断逻辑

            // 4. 维修任务垫付（放保存逻辑）
            // TODO: 实现维修任务垫付逻辑

            // 5.客户直付金额（放保存逻辑）
            // 客户直付金额 -> 影响用户承担金额
            // TODO: 实现客户直付金额逻辑

            // 6. 重新计算自费金额
            // TODO: 实现重新计算自费金额逻辑

            // 7.记录操作日志
            MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
            mtcOperatorLog.setUpdateBy(loginUser.getUsername());
            mtcOperatorLog.setUpdatedTime(new Date());
            mtcOperatorLog.setCreateBy(loginUser.getUsername());
            mtcOperatorLog.setCreatedTime(new Date());
            mtcOperatorLog.setRemark("车辆维修");
            mtcOperatorLog.setRecordId(mtcRepairTask.getId());
            // 维修垫付日志inject
            Integer custPaysDirect = mtcRepairTask.getCustPaysDirect();
            String opeContent = "申请验收";
            if (null != custPaysDirect) {
                if (custPaysDirect > 0) {
                    if (custPaysDirect == 1) {
                        opeContent = opeContent + " 是否客户直付:是";
                    } else if (custPaysDirect == 2) {
                        opeContent = opeContent + " 是否客户直付:否";
                    }
                }
                if (null != mtcRepairTask.getCustAmount()) {
                    opeContent = opeContent + ", 金额" + mtcRepairTask.getCustAmount();
                }
            }
            mtcOperatorLog.setOpeContent(opeContent);
            mtcOperatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            mtcOperatorLog.setCurrentActivityCode(instance.getCurrentActivityCode());
            tableOperatorLogService.insertSelective(mtcOperatorLog);

            // 8. 通知外部系统
            notifyExternalSystems(businessId);
        } catch (Exception e) {
            log.error("处理申请验收事件失败：{}", e.getMessage(), e);
            if (e instanceof WorkflowException) {
                throw (WorkflowException) e;
            }
            throw new WorkflowException(-1, "处理申请验收事件失败：" + e.getMessage());
        }
    }
}
