package com.extracme.saas.autocare.workflow.handler;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.extracme.saas.autocare.constent.TableNameConstant;
import com.extracme.saas.autocare.enums.ActivityStatusEnum;
import com.extracme.saas.autocare.exception.WorkflowException;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.workflow.core.WorkflowContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 提交报价处理器
 * 处理维修报价活动节点流转至核损核价的逻辑
 */
@Slf4j
@Component
public class SubmitQuoteHandler extends AbstractEventHandler {

    /**
     * 事件类型：提交报价
     */
    private static final String EVENT_TYPE_SUBMIT_QUOTE = "SUBMIT_QUOTE";

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    /**
     * 验证报价状态
     *
     * @param businessId 业务ID
     * @throws WorkflowException 如果验证失败
     */
    private void validateQuoteStatus(String businessId) throws WorkflowException {
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(businessId);
        if (null == workflowInstance) {
            throw new WorkflowException(-1, "未找到实例：" + businessId);
        }
        // 验证当前实例是否处于维修报价环节
        if (!workflowInstance.getCurrentActivityCode().equals("REPAIR_QUOTATION")) {
            throw new WorkflowException(-1, "当前实例活动节点不允许提交报价");
        }
        // 验证是否维修报价的处理中状态
        if (!workflowInstance.getStatusCode().equals(ActivityStatusEnum.PROCESSING.getCode())) {
            throw new WorkflowException(-1, "当前实例状态不允许提交报价");
        }
    }

    /**
     * 验证定损单金额和配件信息
     *
     * @param mtcRepairTask 维修任务
     * @throws WorkflowException 如果验证失败
     */
    private void validateQuoteData(MtcRepairTask mtcRepairTask) throws WorkflowException {
        // 检查配件是否已填写（精友系统或自有系统）
        boolean isJingyouSystem = true;
        if (isJingyouSystem) {
            // 精友系统配件校验
        } else {
            // 自有系统配件校验
            // 这里可以添加自有系统的配件校验逻辑
            // 例如：检查配件列表是否为空
        }
    }

    /**
     * 调用精友API提交定损到核损
     *
     * @param mtcRepairTask 维修任务
     */
    private void submitToJingyou(MtcRepairTask mtcRepairTask) {
        // 判断是否使用精友系统

    }

    /**
     * 通知外部系统
     *
     * @param businessId 业务ID
     */
    private void notifyExternalSystems(String businessId) {
        // todo 2非转自费的事故维修任务
        // todo 2同步业财定损单金额
        // todo 2同步业财图片
        // todo 2梧桐维修任务增加日志
    }

    @Override
    public String getSupportedEventType() {
        return EVENT_TYPE_SUBMIT_QUOTE;
    }

    @Override
    public Integer getSupportedTaskType() {
        return -1; // 支持所有任务类型
    }

    @Override
    public String getHandlerName() {
        return "维修报价-提交审核处理器";
    }

    @Override
    protected void doHandle(WorkflowContext context, String tenantId) {
        WorkflowInstance instance = context.getWorkflowInstance();
        String businessId = instance.getBusinessId();
        LoginUser loginUser = SessionUtils.getLoginUser();

        // 记录开始处理时间（用于监听者模式记录处理时间）
        try {
            // 1. 状态校验
            validateQuoteStatus(businessId);

            // 2. 获取维修任务
            MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(businessId);
            if (mtcRepairTask == null) {
                throw new WorkflowException(-1, "未找到维修任务：" + businessId);
            }

            // todo 3. 验证定损单金额和配件信息
            validateQuoteData(mtcRepairTask);

            // todo 4. 调用精友API提交定损到核损
            submitToJingyou(mtcRepairTask);

            // 5. 记录操作日志
            MtcOperatorLog mtcOperatorLog = new MtcOperatorLog();
            mtcOperatorLog.setUpdateBy(loginUser.getUsername());
            mtcOperatorLog.setUpdatedTime(new Date());
            mtcOperatorLog.setCreateBy(loginUser.getUsername());
            mtcOperatorLog.setCreatedTime(new Date());
            mtcOperatorLog.setRemark("维修报价");
            mtcOperatorLog.setRecordId(mtcRepairTask.getId());
            // 构建操作内容
            String opeContent = "提交审核，金额为：" + mtcRepairTask.getRepairInsuranceTotalAmount() + "元,";
            if (null != mtcRepairTask.getCustPaysDirect()) {
                if (mtcRepairTask.getCustPaysDirect() > 0) {
                    if (mtcRepairTask.getCustPaysDirect() == 1) {
                        opeContent = opeContent + " 是否客户直付:是";
                    } else if (mtcRepairTask.getCustPaysDirect() == 2) {
                        opeContent = opeContent + " 是否客户直付:否";
                    }
                }
                if (null != mtcRepairTask.getCustAmount()) {
                    opeContent = opeContent + ", 金额"
                            + mtcRepairTask.getCustAmount();
                }
            }
            mtcOperatorLog.setOpeContent(opeContent.toString());
            mtcOperatorLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            mtcOperatorLog.setCurrentActivityCode(instance.getCurrentActivityCode());
            tableOperatorLogService.insertSelective(mtcOperatorLog);

            MtcOperatorLog mtcProcessLog = new MtcOperatorLog();
            // 设置操作人信息
            mtcProcessLog.setUpdateBy(loginUser.getUsername());
            mtcProcessLog.setUpdatedTime(new Date());
            mtcProcessLog.setCreateBy(loginUser.getUsername());
            mtcProcessLog.setCreatedTime(new Date());

            // 设置日志内容
            mtcProcessLog.setRemark("核损核价");
            mtcProcessLog.setRecordId(mtcRepairTask.getId());
            mtcProcessLog.setOpeContent("待处理");
            mtcProcessLog.setTableName(TableNameConstant.TABLE_NAME_MTC_REPAIR_TASK);
            mtcProcessLog.setCurrentActivityCode(instance.getCurrentActivityCode());

            // 添加流程日志
            tableOperatorLogService.insertSelective(mtcProcessLog);

            // 6. 通知外部系统
            notifyExternalSystems(businessId);
        } catch (Exception e) {
            log.error("处理提交报价事件失败：{}", e.getMessage(), e);
            if (e instanceof WorkflowException) {
                throw (WorkflowException) e;
            }
            throw new WorkflowException(-1, "处理提交报价事件失败：" + e.getMessage());
        }
    }
}
