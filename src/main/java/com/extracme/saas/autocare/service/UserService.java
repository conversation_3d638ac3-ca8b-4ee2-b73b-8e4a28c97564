package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.MerchantAdminQueryDTO;
import com.extracme.saas.autocare.model.dto.UserCreateDTO;
import com.extracme.saas.autocare.model.dto.UserQueryDTO;
import com.extracme.saas.autocare.model.dto.UserUpdateDTO;
import com.extracme.saas.autocare.model.vo.MerchantAdminVO;
import com.extracme.saas.autocare.model.vo.UserVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 创建用户
     *
     * @param createDTO 用户创建信息
     * @return 用户ID
     */
    Long createUser(UserCreateDTO createDTO);

    /**
     * 更新用户
     *
     * @param updateDTO 用户更新信息
     */
    void updateUser(UserUpdateDTO updateDTO);

    /**
     * 分页查询用户列表
     *
     * @param queryDTO 查询参数
     * @return 用户列表
     */
    BasePageVO<UserVO> getUserList(UserQueryDTO queryDTO);

    /**
     * 根据ID获取用户详情
     *
     * @param id 用户ID
     * @return 用户详情
     */
    UserVO getUserById(Long id);

    /**
     * 分页查询商户管理员列表
     *
     * @param queryDTO 查询条件
     * @return 商户管理员列表·
     */
    BasePageVO<MerchantAdminVO> getMerchantAdminList(MerchantAdminQueryDTO queryDTO);

    /**
     * 根据ID获取商户管理员详情
     *
     * @param id 商户管理员ID
     * @return 商户管理员详情
     */
    MerchantAdminVO getMerchantAdminById(Long id);
}