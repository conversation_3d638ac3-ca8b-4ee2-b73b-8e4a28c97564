package com.extracme.saas.autocare.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.extracme.saas.autocare.exception.BusinessException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.extracme.saas.autocare.model.dto.ReplacePartItemCreateDTO;
import com.extracme.saas.autocare.model.dto.ReplacePartItemQueryDTO;
import com.extracme.saas.autocare.model.dto.ReplacePartItemUpdateDTO;
import com.extracme.saas.autocare.model.dto.ReplacePartItemUpdateStatusDTO;
import com.extracme.saas.autocare.model.entity.MtcPartRepairItemGrouping;
import com.extracme.saas.autocare.model.entity.MtcReplaceItem;
import com.extracme.saas.autocare.model.vo.PartRepairItemGroupingVO;
import com.extracme.saas.autocare.model.vo.ReplacePartItemDetailsVO;
import com.extracme.saas.autocare.model.vo.ReplacePartItemListVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TablePartRepairItemGroupingService;
import com.extracme.saas.autocare.repository.TableReplacePartItemService;
import com.extracme.saas.autocare.service.ReplacePartItemService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * 修理厂服务实现类
 */
@Slf4j
@Service
public class ReplacePartItemServiceImpl implements ReplacePartItemService {

    @Autowired
    private TableReplacePartItemService tableReplacePartItemService;

    @Autowired
    private TablePartRepairItemGroupingService tablePartRepairItemGroupingService;

    @Override
    public BasePageVO<ReplacePartItemListVO> queryReplacePartItemList(ReplacePartItemQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<MtcReplaceItem> list = tableReplacePartItemService.queryReplaceItemList(queryDTO);
        PageInfo<MtcReplaceItem> pageInfo = new PageInfo<>(list);
        List<ReplacePartItemListVO> voList = list.stream().map(item -> {
            ReplacePartItemListVO vo = new ReplacePartItemListVO();
            BeanUtils.copyProperties(item, vo);
            vo.setGroupingName("零件分组名称");
            vo.setOrgName("车辆运营单位名称");
            return vo;
        }).collect(Collectors.toList());
        // 构建返回结果
        return BasePageVO.of(voList, pageInfo);
    }

    @Override
    public ReplacePartItemDetailsVO getReplacePartItemDetails(Long id) {
        MtcReplaceItem replaceItem = tableReplacePartItemService.selectById(id);
        if (replaceItem == null) {
            return null;
        }
        ReplacePartItemDetailsVO replacePartItemDetailsVO = new ReplacePartItemDetailsVO();
        BeanUtils.copyProperties(replaceItem, replacePartItemDetailsVO);
        return replacePartItemDetailsVO;
    }

    @Override
    public void createReplacePartItem(ReplacePartItemCreateDTO createDTO) {
        //TODO 获取用户对应的组织公司信息

        // 同一个公司 同一种车型的零件名称不可重复
        ReplacePartItemQueryDTO queryDTO = new ReplacePartItemQueryDTO();
        queryDTO.setPartName(createDTO.getPartName());
        queryDTO.setVehicleModelSeq(createDTO.getVehicleModelSeq());
        queryDTO.setOrgId(createDTO.getOrgId());
        List<MtcReplaceItem> replaceItemList = tableReplacePartItemService.queryReplaceItemList(queryDTO);
        if (CollectionUtils.isNotEmpty(replaceItemList)) {
            throw new RuntimeException("该零件名称已存在");
        }
        MtcReplaceItem replaceItem = new MtcReplaceItem();
        BeanUtils.copyProperties(createDTO, replaceItem);
        tableReplacePartItemService.insert(replaceItem);
        //TODO 添加日志
    }

    @Override
    public void updateReplacePartItem(ReplacePartItemUpdateDTO updateDTO) {
        MtcReplaceItem replaceItem = tableReplacePartItemService.selectById(updateDTO.getId());
        if (replaceItem == null) {
            throw new RuntimeException("该记录不存在");
        }
        if (!StringUtils.equals(updateDTO.getPartName(), replaceItem.getPartName())){
            ReplacePartItemQueryDTO queryDTO = new ReplacePartItemQueryDTO();
            queryDTO.setPartName(updateDTO.getPartName());
            queryDTO.setVehicleModelSeq(updateDTO.getVehicleModelSeq());
            queryDTO.setOrgId(updateDTO.getOrgId());
            List<MtcReplaceItem> replaceItemList = tableReplacePartItemService.queryReplaceItemList(queryDTO);
            if (CollectionUtils.isNotEmpty(replaceItemList)) {
                throw new RuntimeException("该零件名称已存在");
            }
        }
        BeanUtils.copyProperties(updateDTO, replaceItem);
        tableReplacePartItemService.updateSelectiveById(replaceItem);
    }

    @Override
    public void deleteReplacePartItem(Long id) {
        MtcReplaceItem replaceItem = tableReplacePartItemService.selectById(id);
        if (replaceItem == null) {
            throw new RuntimeException("该记录不存在");
        }
        tableReplacePartItemService.deleteById(id);
        //TODO 添加日志
    }

    @Override
    public void updateReplacePartItemStatus(ReplacePartItemUpdateStatusDTO updateStatusDTO) {
        MtcReplaceItem replaceItem = tableReplacePartItemService.selectById(updateStatusDTO.getId());
        if (replaceItem == null) {
            throw new RuntimeException("该记录不存在");
        }
        replaceItem.setStatus(updateStatusDTO.getItemStatus());
        tableReplacePartItemService.updateSelectiveById(replaceItem);
        //TODO 添加日志
    }

    @Override
    public List<PartRepairItemGroupingVO> queryItemGroupingTree(Integer groupingType) {
        List<MtcPartRepairItemGrouping> groupingList = tablePartRepairItemGroupingService.queryPartRepairItemGroupingList(groupingType);
        if (CollectionUtils.isEmpty(groupingList)) {
            return Collections.emptyList();
        }
        List<TreeNode<String>> nodeList = CollUtil.newArrayList();
        groupingList.forEach(info -> {
            Map<String, Object> map = BeanUtil.beanToMap(info);
            TreeNode<String> node = new TreeNode<>(map.get("groupingId").toString(), map.get("fatherId").toString(), map.get("groupingName").toString(), 0L);
            node.setExtra(map);
            nodeList.add(node);
        });
        //配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        treeNodeConfig.setWeightKey("weight");
        treeNodeConfig.setIdKey("id");
        //转换器
        List<Tree<String>> treeNodes = TreeUtil.build(nodeList, "0", treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    // 扩展属性 ...
                    Long sort = Convert.toLong(treeNode.getExtra().get("levelNo"), 0L);
                    Date createTime = (Date) treeNode.getExtra().get("createTime");
                    if (sort == 0 && treeNode.getExtra().get("createTime") != null) {
                        sort = createTime.getTime();
                    }
                    tree.putExtra("sort", treeNode.getExtra().get("position"));
                    tree.putExtra("weight", sort);
                });
        if (ObjectUtil.length(treeNodes) == 0) {
            throw new BusinessException("零件修理项目结构错误");
        }
        List<PartRepairItemGroupingVO> treeListResponse = BeanUtil.copyToList(treeNodes, PartRepairItemGroupingVO.class);// JSONUtil.toBean(JSONUtil.toJsonStr(tree), OrgTreeResponse.class);
        return treeListResponse;

    }
}