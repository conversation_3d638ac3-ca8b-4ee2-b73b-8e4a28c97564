package com.extracme.saas.autocare.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.model.dto.OperatorLogDTO;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.service.OperatorLogService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

@Service
public class OperatorLogServiceImpl implements OperatorLogService {

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Override
    public PageInfo<OperatorLogDTO> queryOperatorLog(Integer pageNum, Integer pageSize, Long recordId, String tableName) {
        PageHelper.startPage(pageNum, pageSize);
        List<OperatorLogDTO> list = tableOperatorLogService.queryOperatorLog(recordId, tableName);
        return new PageInfo<>(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordOperationLog(String tableName, Long recordId, String content, String currentActivityCode, String operator) {
        MtcOperatorLog log = new MtcOperatorLog();
        log.setTableName(tableName);
        log.setRecordId(recordId);
        log.setOpeContent(content);
        log.setCurrentActivityCode(currentActivityCode);
        log.setCreatedTime(new Date());
        log.setCreateBy(operator);
        tableOperatorLogService.insertSelective(log);
    }

    @Override
    public List<MtcOperatorLog> queryLogsByRecordId(Long recordId) {
        return tableOperatorLogService.selectByRecordId(recordId);
    }

    @Override
    public List<MtcOperatorLog> queryLogsByRecordIdAndTache(Long recordId, String currentTache) {
        return tableOperatorLogService.selectByRecordIdAndTache(recordId, currentTache);
    }
}