package com.extracme.saas.autocare.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.extracme.saas.autocare.model.dto.TokenDTO;
import com.extracme.saas.autocare.model.dto.repairTask.AccidentTaskDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskUpdateDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.service.InsurancePreReviewService;

/**
 * 保险预审服务实现类
 */
@Service
public class InsurancePreReviewServiceImpl implements InsurancePreReviewService {

    @Override
    public void saveRepairTask(String id, RepairTaskUpdateDTO repairTaskUpdateDTO, TokenDTO tokenDTO) {
        // 空实现，待完善
    }

    @Override
    public List<AccidentTaskDTO> getAccidentTaskList(String vin) {
        // 空实现，待完善
        return null;
    }

    @Override
    public void relateAccidentTask(Long taskId, String accidentNo) {
        // 空实现，待完善
    }

    @Override
    public void validateAccidentNo(MtcRepairTask repairTask, String accidentNo) {
        // 空实现，待完善
    }
}
