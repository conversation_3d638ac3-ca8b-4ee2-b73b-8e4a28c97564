package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.LossAssessmentDTO;
import com.extracme.saas.autocare.model.dto.LossAssessmentApproveDTO;

/**
 * 核损核价服务接口
 */
public interface LossAssessmentService {

    /**
     * 保存核损核价信息
     *
     * @param lossAssessmentDTO 核损核价信息
     */
    void saveLossAssessment(LossAssessmentDTO lossAssessmentDTO);

    /**
     * 核损核价-审核通过
     *
     * @param approveDTO 审核参数
     */
    void approveLossAssessment(LossAssessmentApproveDTO approveDTO);

    /**
     * 核损核价-平级移交
     *
     * @param approveDTO 审核参数
     */
    void updateTransferTask(String taskNo);

    /**
     * 核损核价-清除任务占据人
     *
     * @param approveDTO 审核参数
     */
    void clearOwner(String taskNo);
}