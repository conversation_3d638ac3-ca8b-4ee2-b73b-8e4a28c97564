package com.extracme.saas.autocare.service;

import java.util.List;

import com.extracme.saas.autocare.model.dto.OperatorLogDTO;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.github.pagehelper.PageInfo;

public interface OperatorLogService {

    /**
     * 查看操作日志
     *
     * @param pageSize  每页显示条数
     * @param pageNum   页码
     * @param recordId  记录主键
     * @param tableName 表名
     */
    PageInfo<OperatorLogDTO> queryOperatorLog(Integer pageNum, Integer pageSize, Long recordId, String tableName);

    /**
     * 记录维修流程操作日志
     *
     * @param tableName           关联表名
     * @param recordId            关联记录ID
     * @param content             操作内容
     * @param currentActivityCode 当前环节编码
     * @param operator            操作人
     */
    void recordOperationLog(String tableName, Long recordId, String content, String currentActivityCode, String operator);

    /**
     * 根据记录ID查询操作日志列表
     */
    List<MtcOperatorLog> queryLogsByRecordId(Long recordId);

    /**
     * 根据记录ID和环节查询操作日志列表
     */
    List<MtcOperatorLog> queryLogsByRecordIdAndTache(Long recordId, String currentTache);
}