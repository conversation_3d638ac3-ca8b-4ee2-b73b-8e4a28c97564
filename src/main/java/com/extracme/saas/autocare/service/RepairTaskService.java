package com.extracme.saas.autocare.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.RepairRemarkDTO;
import com.extracme.saas.autocare.model.dto.repairTask.GetFirstPageInfoDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskCreateDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskListQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskProcessQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskUpdateDTO;
import com.extracme.saas.autocare.model.vo.MtcTaskListResultVO;
import com.extracme.saas.autocare.model.vo.RepairDepotFirstPageInfoVO;
import com.extracme.saas.autocare.model.vo.RepairDepotInRepairingVO;
import com.extracme.saas.autocare.model.vo.RepairTaskDetailsVO;
import com.extracme.saas.autocare.model.vo.RepairTaskProcessListVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;

/**
 * 维修任务服务接口
 */
public interface RepairTaskService {

    /**
     * 创建维修任务
     *
     * @param createDTO 创建参数
     * @return 维修任务ID
     */
    Long createRepairTask(RepairTaskCreateDTO createDTO);

    /**
     * 获取维修厂首页统计信息
     * 
     * @param repairDepotId 修理厂ID
     * @return 首页统计信息
     */
    BasePageVO<RepairDepotFirstPageInfoVO> getFirstPageInfo(GetFirstPageInfoDTO getFirstPageInfoDTO);

    /**
     * 获取维修厂首页统计信息
     * 
     * @param repairDepotId 修理厂ID
     * @return 首页统计信息
     */
    BasePageVO<RepairDepotInRepairingVO> getRepairDepotInRepairingInfo(GetFirstPageInfoDTO getFirstPageInfoDTO);

    /**
     * 流程查询-查询维修任务流程列表
     *
     * @param queryDTO 维修任务流程查询DTO
     * @return 维修任务列表
     */
    BasePageVO<RepairTaskProcessListVO> queryRepairTaskProcessList(RepairTaskProcessQueryDTO queryDTO);

    /**
     * 各环节-查询维修任务列表
     *
     * @param queryDTO 维修任务流程查询DTO
     * @return 维修任务列表
     */
    MtcTaskListResultVO queryRepairTaskList(RepairTaskListQueryDTO queryDTO);

    /**
     * 获取维修任务详情
     *
     * @param id 维修任务ID
     * @return 维修任务详情
     */
    RepairTaskDetailsVO getRepairTaskDetails(Long id);

    /**
     * 维修任务保存
     *
     * @param id                  任务id
     * @param repairTaskUpdateDTO 任务修改信息bo
     * @param tokenDTO
     */
    void saveRepairTask(Long id, RepairTaskUpdateDTO repairTaskUpdateDTO);

    /**
     * 处理维修任务相关的图片和视频文件
     * 公共方法，供多个服务共用图片处理逻辑
     *
     * @param taskNo 任务编号
     * @param mediaTypeMap 图片/视频类型映射，键为图片类型，值为图片URL列表
     * @param operatorName 操作人用户名
     * @throws BusinessException 处理过程中的业务异常
     */
    void processMediaFiles(String taskNo, Map<BigDecimal, List<String>> mediaTypeMap, String operatorName);

    /**
     * 待分配-选择修理厂并更新维修任务信息
     *
     * @param taskId 维修任务ID
     * @param repairDepotId 修理厂ID
     * @return 是否更新成功
     * @throws BusinessException 业务异常
     */
    void selectRepairDepot(Long taskId, String repairDepotId);

    /**
     * 添加维修备注
     *
     * @param repairRemarkDTO 维修备注信息
     * @throws BusinessException 业务异常
     */
    void addRepairRemark(RepairRemarkDTO repairRemarkDTO);

    /**
     * 删除维修备注
     *
     * @param id 备注ID
     * @param repairStage 维修阶段
     * @throws BusinessException 业务异常
     */
    void deleteRepairRemark(Long id, String repairStage);
}
