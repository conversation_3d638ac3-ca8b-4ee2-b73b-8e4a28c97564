package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.VehicleRepairActivityUpdateDTO;
import com.extracme.saas.autocare.model.dto.VehicleRepairRecordQueryDTO;
import com.extracme.saas.autocare.model.vo.VehicleRepairRecordVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;

/**
 * 车辆维修活动节点服务接口
 * <AUTHOR>
 * @date 2025/05/21
 */
public interface VehicleRepairActivityService {

    /**
     * 车辆维修申请验收
     *
     * @param updateDTO 更新维修任务的DTO
     */
    void changeVehicleCheck(VehicleRepairActivityUpdateDTO updateDTO);

    /**
     * 查询车辆历次维修记录
     *
     * @param queryDTO 查询条件DTO
     * @return 分页的车辆维修记录
     */
    BasePageVO<VehicleRepairRecordVO> queryVehicleRepairRecord(VehicleRepairRecordQueryDTO queryDTO);
}
