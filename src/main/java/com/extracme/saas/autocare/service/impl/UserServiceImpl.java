package com.extracme.saas.autocare.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.extracme.saas.autocare.enums.ErrorCode;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.exception.UserException;
import com.extracme.saas.autocare.model.dto.MerchantAdminQueryDTO;
import com.extracme.saas.autocare.model.dto.UserCreateDTO;
import com.extracme.saas.autocare.model.dto.UserQueryDTO;
import com.extracme.saas.autocare.model.dto.UserUpdateDTO;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.entity.MtcRepairDepotInfo;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.model.vo.MerchantAdminVO;
import com.extracme.saas.autocare.model.vo.UserVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TableRepairDepotInfoService;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserRoleService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.service.UserService;
import com.extracme.saas.autocare.util.OperateLogUtil;
import com.extracme.saas.autocare.util.SessionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final TableUserService tableUserService;
    private final TableRoleService tableRoleService;
    private final TableUserRoleService tableUserRoleService;
    private final TableTenantService tableTenantService;
    private final TableRepairDepotInfoService tableRepairDepotInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createUser(UserCreateDTO createDTO) {
        // 检查手机号是否已存在
        if (tableUserService.countByMobile(createDTO.getMobile()) > 0) {
            throw new UserException(UserException.USER_ALREADY_EXISTS, "手机号已存在");
        }

        // 创建用户实体
        SysUser user = new SysUser();
        user.setUsername(createDTO.getMobile());
        user.setNickname(createDTO.getNickname());
        user.setMobile(createDTO.getMobile());
        user.setEmail(createDTO.getEmail());
        user.setOrgId(createDTO.getOrgId());
        user.setStatus(createDTO.getStatus());
        user.setApprovalLevel(createDTO.getApprovalLevel());
        user.setRepairDepotId(createDTO.getRepairDepotId());
        user.setAccountType(createDTO.getAccountType());
        user.setTenantId(createDTO.getTenantId());

        // 保存用户
        SysUser savedUser = tableUserService.insert(user);
        Long userId = savedUser.getId();

        // 如果包含角色ID列表，创建用户角色关联
        if (createDTO.getRoleIds() != null && !createDTO.getRoleIds().isEmpty()) {
            tableUserRoleService.batchCreate(userId, createDTO.getRoleIds());
        }

        // 记录操作日志
        OperateLogUtil.recordUserCreateDetailed(createDTO.getMobile(), createDTO.getNickname());

        return userId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(UserUpdateDTO updateDTO) {
        // 检查用户是否存在
        SysUser existingUser = tableUserService.selectById(updateDTO.getId());
        if (existingUser == null) {
            throw new UserException(UserException.USER_NOT_FOUND, "用户不存在");
        }

        // 如果更新手机号，检查新手机号是否已被其他用户使用
        if (updateDTO.getMobile() != null && !updateDTO.getMobile().equals(existingUser.getMobile())) {
            if (tableUserService.countByMobile(updateDTO.getMobile()) > 0) {
                throw new UserException(UserException.USER_ALREADY_EXISTS, "手机号已被其他用户使用");
            }
        }

        // 更新用户信息
        SysUser user = new SysUser();
        user.setId(updateDTO.getId());
        user.setUsername(updateDTO.getMobile());
        user.setNickname(updateDTO.getNickname());
        user.setMobile(updateDTO.getMobile());
        user.setTenantId(updateDTO.getTenantId());
        user.setEmail(updateDTO.getEmail());
        user.setOrgId(updateDTO.getOrgId());
        user.setStatus(updateDTO.getStatus());
        user.setApprovalLevel(updateDTO.getApprovalLevel());
        user.setRepairDepotId(updateDTO.getRepairDepotId());
        user.setAccountType(updateDTO.getAccountType());

        // 更新用户基本信息
        tableUserService.updateSelectiveById(user);

        // 如果包含角色ID列表，更新用户角色
        if (updateDTO.getRoleIds() != null) {
            // 更新用户角色关联
            tableUserRoleService.updateUserRoles(updateDTO.getId(), updateDTO.getRoleIds());
            log.info("更新用户角色: userId={}, roleIds={}", updateDTO.getId(), updateDTO.getRoleIds());
        }

        // 记录操作日志 - 使用详细的变更对比记录
        SysUser updatedUser = buildUpdatedUserForComparison(existingUser, updateDTO);
        OperateLogUtil.recordUserUpdateDetailed(existingUser, updatedUser);
    }

    /**
     * 构建用于日志对比的更新后用户对象
     * @param existingUser 原始用户信息
     * @param updateDTO 更新参数
     * @return 构建后的用户对象
     */
    private SysUser buildUpdatedUserForComparison(SysUser existingUser, UserUpdateDTO updateDTO) {
        SysUser updatedUser = new SysUser();
        BeanUtils.copyProperties(existingUser, updatedUser);
        updatedUser.setNickname(updateDTO.getNickname() != null ? updateDTO.getNickname() : existingUser.getNickname());
        updatedUser.setMobile(updateDTO.getMobile() != null ? updateDTO.getMobile() : existingUser.getMobile());
        updatedUser.setEmail(updateDTO.getEmail() != null ? updateDTO.getEmail() : existingUser.getEmail());
        updatedUser.setOrgId(updateDTO.getOrgId() != null ? updateDTO.getOrgId() : existingUser.getOrgId());
        updatedUser.setStatus(updateDTO.getStatus() != null ? updateDTO.getStatus() : existingUser.getStatus());
        updatedUser.setApprovalLevel(updateDTO.getApprovalLevel() != null ? updateDTO.getApprovalLevel() : existingUser.getApprovalLevel());
        updatedUser.setRepairDepotId(updateDTO.getRepairDepotId() != null ? updateDTO.getRepairDepotId() : existingUser.getRepairDepotId());
        updatedUser.setAccountType(updateDTO.getAccountType() != null ? updateDTO.getAccountType() : existingUser.getAccountType());
        updatedUser.setTenantId(updateDTO.getTenantId() != null ? updateDTO.getTenantId() : existingUser.getTenantId());
        return updatedUser;
    }

    @Override
    public BasePageVO<UserVO> getUserList(UserQueryDTO queryDTO) {
        // 判断当前用户是否为超级管理员
        boolean isSuperAdmin = SessionUtils.isSuperAdmin();

        // 根据用户类型决定租户ID过滤条件
        Long tenantIdFilter = null;
        if (!isSuperAdmin) {
            // 普通用户只能查询自己所属租户下的用户数据
            LoginUser currentLoginUser = SessionUtils.getLoginUser();
            if (currentLoginUser != null && currentLoginUser.getUser() != null) {
                tenantIdFilter = currentLoginUser.getUser().getTenantId();
            }
        }
        // 超级管理员不设置租户ID过滤条件，可以查询所有租户的用户数据

        // 开启分页
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 构建查询条件
        List<SysUser> users = tableUserService.findByCondition(
            queryDTO.getNickname(),
            queryDTO.getMobile(),
            queryDTO.getRoleId(),
            tenantIdFilter
        );

        // 获取分页信息
        PageInfo<SysUser> pageInfo = new PageInfo<>(users);

        // 如果没有查询到用户，直接返回空结果
        if (users.isEmpty()) {
            return BasePageVO.of(Collections.emptyList(), pageInfo);
        }

        // 一次性查询所有租户信息，构建租户ID与租户名称的映射关系
        List<SysTenant> allTenants = tableTenantService.findByCondition(null, null, null);
        Map<Long, String> tenantIdToNameMap = allTenants.stream()
            .collect(Collectors.toMap(SysTenant::getId, SysTenant::getTenantName, (existing, replacement) -> existing));

        // 预加载修理厂信息，构建修理厂ID与修理厂名称的映射关系
        Map<String, String> repairDepotNameMap = buildRepairDepotNameMap();

        // 提取所有用户ID
        List<Long> userIds = users.stream()
            .map(SysUser::getId)
            .collect(Collectors.toList());

        // 批量查询所有用户的角色
        Map<Long, List<SysRole>> userRolesMap = batchGetUserRoles(userIds);

        // 在内存中组装数据
        List<UserVO> voList = users.stream().map(user -> {
            UserVO vo = new UserVO();
            BeanUtils.copyProperties(user, vo);

            // 从Map中获取租户名称并设置
            if (user.getTenantId() != null) {
                String tenantName = tenantIdToNameMap.get(user.getTenantId());
                vo.setTenantName(tenantName);
            }

            // 从Map中获取修理厂名称并设置
            if (user.getRepairDepotId() != null && !user.getRepairDepotId().isEmpty()) {
                String repairDepotName = getRepairDepotNameFromMap(user, repairDepotNameMap, isSuperAdmin);
                vo.setRepairDepotName(repairDepotName != null ? repairDepotName : "");
            } else {
                vo.setRepairDepotName("");
            }

            // 从Map中获取用户角色
            List<SysRole> roles = userRolesMap.getOrDefault(user.getId(), Collections.emptyList());

            // 提取角色ID列表
            List<Long> roleIds = roles.stream()
                .map(SysRole::getId)
                .collect(Collectors.toList());
            vo.setRoleIds(roleIds);

            // 提取角色名称列表
            List<String> roleNames = roles.stream()
                .map(SysRole::getRoleName)
                .collect(Collectors.toList());
            vo.setRoleNames(roleNames);

            return vo;
        }).collect(Collectors.toList());

        // 构建返回结果
        return BasePageVO.of(voList, pageInfo);
    }

    @Override
    public UserVO getUserById(Long id) {
        // 查询用户基本信息
        SysUser user = tableUserService.selectById(id);
        if (user == null) {
            throw new UserException(UserException.USER_NOT_FOUND, "用户不存在");
        }

        // 查询用户角色
        List<SysRole> roles = tableRoleService.findByUserId(id);

        // 组装用户详情
        UserVO vo = new UserVO();
        BeanUtils.copyProperties(user, vo);

        // 设置租户名称
        if (user.getTenantId() != null) {
            SysTenant tenant = tableTenantService.selectById(user.getTenantId());
            if (tenant != null) {
                vo.setTenantName(tenant.getTenantName());
            }
        }

        // 设置修理厂名称
        if (user.getTenantId() != null && user.getRepairDepotId() != null && !user.getRepairDepotId().isEmpty()) {
            String repairDepotName = getRepairDepotNameByTenantAndDepotId(user.getTenantId(), user.getRepairDepotId());
            vo.setRepairDepotName(repairDepotName != null ? repairDepotName : "");
        } else {
            vo.setRepairDepotName("");
        }

        // 提取角色ID列表
        List<Long> roleIds = roles.stream()
            .map(SysRole::getId)
            .collect(Collectors.toList());
        vo.setRoleIds(roleIds);

        // 提取角色名称列表
        List<String> roleNames = roles.stream()
            .map(SysRole::getRoleName)
            .collect(Collectors.toList());
        vo.setRoleNames(roleNames);

        return vo;
    }

    /**
     * 批量获取用户角色
     * 一次性查询所有用户的角色，减少数据库访问次数
     *
     * @param userIds 用户ID列表
     * @return 用户ID到角色列表的映射
     */
    private Map<Long, List<SysRole>> batchGetUserRoles(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 创建结果Map
        Map<Long, List<SysRole>> result = new HashMap<>();

        // 初始化每个用户的角色列表为空列表
        userIds.forEach(userId -> result.put(userId, new ArrayList<>()));

        // 使用现有的方法查询每个用户的角色，并将结果放入Map中
        for (Long userId : userIds) {
            List<SysRole> roles = tableRoleService.findByUserId(userId);
            result.put(userId, roles);
        }

        return result;
    }

    /**
     * 构建修理厂名称映射表
     * 预加载修理厂信息，避免N+1查询问题
     *
     * <p>多租户数据隔离说明：</p>
     * <ul>
     *   <li>普通用户：通过 @TenantSchema 注解，自动应用租户数据隔离，只查询当前租户的修理厂</li>
     *   <li>超级管理员：绕过多租户拦截器，查询所有租户的修理厂信息</li>
     *   <li>映射键值策略：普通用户使用 repairDepotId，超级管理员使用 tenantId_repairDepotId</li>
     * </ul>
     *
     * @return 修理厂ID与修理厂名称的映射关系
     */
    private Map<String, String> buildRepairDepotNameMap() {
        Long currentTenantId = SessionUtils.getTenantId();
        boolean isSuperAdmin = determineIfSuperAdmin();

        logRepairDepotLoadStart(currentTenantId, isSuperAdmin);

        try {
            List<MtcRepairDepotInfo> repairDepots = queryRepairDepots(isSuperAdmin, currentTenantId);
            return buildRepairDepotMapping(repairDepots, isSuperAdmin, currentTenantId);
        } catch (Exception e) {
            logRepairDepotLoadError(currentTenantId, isSuperAdmin, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 判断当前用户是否为超级管理员
     *
     * @return true-超级管理员，false-普通用户
     */
    private boolean determineIfSuperAdmin() {
        LoginUser loginUser = SessionUtils.getLoginUser();

        if (loginUser == null || loginUser.getUser() == null) {
            return false;
        }

        Integer accountType = loginUser.getUser().getAccountType();
        return accountType != null && accountType == 0;
    }

    /**
     * 查询修理厂数据
     *
     * @param isSuperAdmin 是否为超级管理员
     * @param currentTenantId 当前租户ID
     * @return 修理厂列表
     */
    private List<MtcRepairDepotInfo> queryRepairDepots(boolean isSuperAdmin, Long currentTenantId) {
        if (isSuperAdmin) {
            return queryRepairDepotsForSuperAdmin();
        }
        return queryRepairDepotsForRegularUser(currentTenantId);
    }

    /**
     * 查询超级管理员的修理厂数据
     *
     * @return 所有租户的修理厂列表
     */
    private List<MtcRepairDepotInfo> queryRepairDepotsForSuperAdmin() {
        List<MtcRepairDepotInfo> repairDepots = tableRepairDepotInfoService.findAllValidDepotsForSuperAdmin();
        log.debug("超级管理员查询所有租户修理厂信息，共找到: {} 条", repairDepots.size());
        return repairDepots;
    }

    /**
     * 查询普通用户的修理厂数据
     *
     * @param currentTenantId 当前租户ID
     * @return 当前租户的修理厂列表
     */
    private List<MtcRepairDepotInfo> queryRepairDepotsForRegularUser(Long currentTenantId) {
        List<MtcRepairDepotInfo> repairDepots = tableRepairDepotInfoService.findValidDepots();
        log.debug("普通用户查询当前租户修理厂信息，租户ID: {}, 共找到: {} 条", currentTenantId, repairDepots.size());
        return repairDepots;
    }

    /**
     * 记录修理厂加载开始日志
     *
     * @param currentTenantId 当前租户ID
     * @param isSuperAdmin 是否为超级管理员
     */
    private void logRepairDepotLoadStart(Long currentTenantId, boolean isSuperAdmin) {
        String userType = isSuperAdmin ? "超级管理员" : "普通用户";
        log.debug("开始预加载修理厂信息，当前租户ID: {}, 用户类型: {}", currentTenantId, userType);
    }

    /**
     * 记录修理厂加载错误日志
     *
     * @param currentTenantId 当前租户ID
     * @param isSuperAdmin 是否为超级管理员
     * @param e 异常信息
     */
    private void logRepairDepotLoadError(Long currentTenantId, boolean isSuperAdmin, Exception e) {
        String userType = isSuperAdmin ? "超级管理员" : "普通用户";
        log.error("预加载修理厂信息失败，租户ID: {}, 用户类型: {}", currentTenantId, userType, e);
    }

    /**
     * 构建修理厂名称映射
     *
     * @param repairDepots 修理厂列表
     * @param isSuperAdmin 是否为超级管理员
     * @param currentTenantId 当前租户ID
     * @return 修理厂映射表
     */
    private Map<String, String> buildRepairDepotMapping(List<MtcRepairDepotInfo> repairDepots,
                                                       boolean isSuperAdmin, Long currentTenantId) {
        if (repairDepots.isEmpty()) {
            log.debug("修理厂列表为空，返回空映射表");
            return Collections.emptyMap();
        }

        Map<String, String> repairDepotNameMap = new HashMap<>();

        for (MtcRepairDepotInfo depot : repairDepots) {
            if (!isValidRepairDepot(depot)) {
                continue;
            }

            String mappingKey = buildMappingKey(depot, isSuperAdmin);
            String displayName = buildDisplayName(depot, isSuperAdmin);

            repairDepotNameMap.put(mappingKey, displayName);
        }

        logMappingResult(isSuperAdmin, currentTenantId, repairDepotNameMap.size());
        return repairDepotNameMap;
    }

    /**
     * 验证修理厂信息是否有效
     *
     * @param depot 修理厂信息
     * @return true-有效，false-无效
     */
    private boolean isValidRepairDepot(MtcRepairDepotInfo depot) {
        if (depot.getRepairDepotId() == null || depot.getRepairDepotName() == null) {
            log.warn("修理厂信息不完整，跳过处理，ID: {}, 名称: {}",
                    depot.getRepairDepotId(), depot.getRepairDepotName());
            return false;
        }
        return true;
    }

    /**
     * 构建映射键
     *
     * @param depot 修理厂信息
     * @param isSuperAdmin 是否为超级管理员
     * @return 映射键
     */
    private String buildMappingKey(MtcRepairDepotInfo depot, boolean isSuperAdmin) {
        String repairDepotId = depot.getRepairDepotId();

        if (isSuperAdmin) {
            String tenantInfo = extractTenantFromRepairDepot(depot);
            return tenantInfo + "_" + repairDepotId;
        }

        return repairDepotId;
    }

    /**
     * 构建显示名称
     *
     * @param depot 修理厂信息
     * @param isSuperAdmin 是否为超级管理员
     * @return 显示名称
     */
    private String buildDisplayName(MtcRepairDepotInfo depot, boolean isSuperAdmin) {
        String repairDepotName = depot.getRepairDepotName();

        if (isSuperAdmin) {
            String tenantName = extractTenantNameFromRepairDepot(depot);
            return repairDepotName + " [租户:" + tenantName + "]";
        }

        return repairDepotName;
    }

    /**
     * 记录映射构建结果日志
     *
     * @param isSuperAdmin 是否为超级管理员
     * @param currentTenantId 当前租户ID
     * @param mappingCount 映射数量
     */
    private void logMappingResult(boolean isSuperAdmin, Long currentTenantId, int mappingCount) {
        String userType = isSuperAdmin ? "超级管理员" : "普通用户";
        log.info("成功构建修理厂映射，用户类型: {}, 租户ID: {}, 映射数量: {}",
                userType, currentTenantId, mappingCount);
    }

    /**
     * 从修理厂信息中提取租户信息
     *
     * @param depot 修理厂信息对象
     * @return 租户标识
     */
    private String extractTenantFromRepairDepot(MtcRepairDepotInfo depot) {
        // 优先从备注字段提取租户信息
        String tenantFromRemark = extractTenantFromRemark(depot.getRemark(), depot.getRepairDepotId());
        if (tenantFromRemark != null) {
            return tenantFromRemark;
        }

        // 备用方案：从修理厂ID中提取
        String tenantFromId = extractTenantFromRepairDepotId(depot.getRepairDepotId());
        if (tenantFromId != null) {
            return tenantFromId;
        }

        // 默认值
        return "unknown";
    }

    /**
     * 从备注字段中提取租户信息
     *
     * @param remark 备注信息
     * @param repairDepotId 修理厂ID（用于日志记录）
     * @return 租户标识，如果提取失败返回null
     */
    private String extractTenantFromRemark(String remark, String repairDepotId) {
        if (remark == null || !remark.contains("TENANT_ID:")) {
            return null;
        }

        try {
            String[] parts = remark.split(";");
            for (String part : parts) {
                if (part.startsWith("TENANT_ID:")) {
                    return part.substring("TENANT_ID:".length());
                }
            }
        } catch (Exception e) {
            log.warn("解析修理厂租户信息失败，修理厂ID: {}, 备注: {}", repairDepotId, remark, e);
        }

        return null;
    }

    /**
     * 从修理厂信息中提取租户名称
     *
     * @param depot 修理厂信息对象
     * @return 租户名称
     */
    private String extractTenantNameFromRepairDepot(MtcRepairDepotInfo depot) {
        String tenantNameFromRemark = extractTenantNameFromRemark(depot.getRemark(), depot.getRepairDepotId());
        if (tenantNameFromRemark != null) {
            return tenantNameFromRemark;
        }

        // 备用方案：返回租户ID作为名称
        return extractTenantFromRepairDepot(depot);
    }

    /**
     * 从备注字段中提取租户名称
     *
     * @param remark 备注信息
     * @param repairDepotId 修理厂ID（用于日志记录）
     * @return 租户名称，如果提取失败返回null
     */
    private String extractTenantNameFromRemark(String remark, String repairDepotId) {
        if (remark == null || !remark.contains("TENANT_NAME:")) {
            return null;
        }

        try {
            String[] parts = remark.split(";");
            for (String part : parts) {
                if (part.startsWith("TENANT_NAME:")) {
                    return part.substring("TENANT_NAME:".length());
                }
            }
        } catch (Exception e) {
            log.warn("解析修理厂租户名称失败，修理厂ID: {}, 备注: {}", repairDepotId, remark, e);
        }

        return null;
    }

    /**
     * 从修理厂ID中提取租户信息（兼容旧方法）
     *
     * @param repairDepotId 修理厂ID
     * @return 租户标识，如果提取失败返回null
     */
    private String extractTenantFromRepairDepotId(String repairDepotId) {
        if (repairDepotId == null || !repairDepotId.contains("_")) {
            return null;
        }

        // 假设格式为 tenantId_timestamp 或类似格式
        String[] parts = repairDepotId.split("_");
        if (parts.length > 0) {
            return parts[0];
        }

        return null;
    }

    /**
     * 从映射表中获取修理厂名称
     * 根据用户类型采用不同的查找策略
     *
     * @param user 用户信息
     * @param repairDepotNameMap 修理厂名称映射表
     * @param isSuperAdmin 是否为超级管理员
     * @return 修理厂名称
     */
    private String getRepairDepotNameFromMap(SysUser user, Map<String, String> repairDepotNameMap, boolean isSuperAdmin) {
        String repairDepotId = user.getRepairDepotId();

        if (isSuperAdmin) {
            return getRepairDepotNameForSuperAdmin(user, repairDepotNameMap, repairDepotId);
        }

        return getRepairDepotNameForRegularUser(repairDepotNameMap, repairDepotId);
    }

    /**
     * 为超级管理员获取修理厂名称
     *
     * @param user 用户信息
     * @param repairDepotNameMap 修理厂名称映射表
     * @param repairDepotId 修理厂ID
     * @return 修理厂名称
     */
    private String getRepairDepotNameForSuperAdmin(SysUser user, Map<String, String> repairDepotNameMap, String repairDepotId) {
        String tenantInfo = extractTenantFromRepairDepotId(repairDepotId);
        if (tenantInfo == null) {
            tenantInfo = "unknown";
        }

        String mappingKey = tenantInfo + "_" + repairDepotId;
        String repairDepotName = repairDepotNameMap.get(mappingKey);

        log.debug("超级管理员查找修理厂名称，用户租户: {}, 修理厂ID: {}, 映射键: {}, 结果: {}",
                user.getTenantId(), repairDepotId, mappingKey, repairDepotName);

        return repairDepotName;
    }

    /**
     * 为普通用户获取修理厂名称
     *
     * @param repairDepotNameMap 修理厂名称映射表
     * @param repairDepotId 修理厂ID
     * @return 修理厂名称
     */
    private String getRepairDepotNameForRegularUser(Map<String, String> repairDepotNameMap, String repairDepotId) {
        String repairDepotName = repairDepotNameMap.get(repairDepotId);
        log.debug("普通用户查找修理厂名称，修理厂ID: {}, 结果: {}", repairDepotId, repairDepotName);
        return repairDepotName;
    }

    /**
     * 根据修理厂ID获取修理厂名称
     * 用于单个用户查询时获取修理厂名称
     *
     * <p>多租户数据隔离说明：</p>
     * <ul>
     *   <li>通过多租户拦截器自动应用租户隔离，只查询当前租户的修理厂</li>
     *   <li>直接使用 repairDepotId 作为查询条件，简化查询逻辑</li>
     * </ul>
     *
     * @param tenantId 租户ID（用于日志记录）
     * @param repairDepotId 修理厂ID
     * @return 修理厂名称，如果未找到则返回null
     */
    private String getRepairDepotNameByTenantAndDepotId(Long tenantId, String repairDepotId) {
        try {
            // 优先从缓存获取
            String nameFromCache = getRepairDepotNameFromCache(repairDepotId);
            if (nameFromCache != null) {
                return nameFromCache;
            }

            // 备用方案：直接查询数据库
            return getRepairDepotNameFromDatabase(tenantId, repairDepotId);

        } catch (Exception e) {
            log.error("查询修理厂名称失败，租户ID: {}, 修理厂ID: {}", tenantId, repairDepotId, e);
            return null;
        }
    }

    /**
     * 从缓存中获取修理厂名称
     *
     * @param repairDepotId 修理厂ID
     * @return 修理厂名称，如果未找到返回null
     */
    private String getRepairDepotNameFromCache(String repairDepotId) {
        Map<String, String> repairDepotNameMap = buildRepairDepotNameMap();
        String repairDepotName = repairDepotNameMap.get(repairDepotId);

        if (repairDepotName != null) {
            log.debug("从缓存获取修理厂名称，修理厂ID: {}, 名称: {}", repairDepotId, repairDepotName);
        }

        return repairDepotName;
    }

    /**
     * 从数据库中获取修理厂名称
     *
     * @param tenantId 租户ID
     * @param repairDepotId 修理厂ID
     * @return 修理厂名称，如果未找到返回null
     */
    private String getRepairDepotNameFromDatabase(Long tenantId, String repairDepotId) {
        // 注意：由于多租户拦截器的存在，这里只会查询当前租户的修理厂
        MtcRepairDepotInfo depot = tableRepairDepotInfoService.selectByRepairDepotCode(repairDepotId);

        if (depot != null && depot.getRepairDepotName() != null) {
            log.debug("从数据库获取修理厂名称，修理厂ID: {}, 名称: {}", repairDepotId, depot.getRepairDepotName());
            return depot.getRepairDepotName();
        }

        log.debug("未找到修理厂信息，租户ID: {}, 修理厂ID: {}", tenantId, repairDepotId);
        return null;
    }

    @Override
    public BasePageVO<MerchantAdminVO> getMerchantAdminList(MerchantAdminQueryDTO queryDTO) {
        // 开启分页
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 查询商户管理员用户列表
        // 这里假设商户管理员的账号类型为1
        List<SysUser> users = tableUserService.findMerchantAdmins(
                queryDTO.getMerchantName(),
                queryDTO.getAdminName(),
                queryDTO.getMobile(),
                queryDTO.getStatus()
        );

        System.out.println(JSON.toJSONString(users));

        // 转换为VO
        List<MerchantAdminVO> adminVOs = users.stream().map(user -> {
            MerchantAdminVO adminVO = new MerchantAdminVO();
            BeanUtils.copyProperties(user, adminVO);
            return adminVO;
        }).collect(Collectors.toList());

        // 构建分页结果
        PageInfo<SysUser> pageInfo = new PageInfo<>(users);
        return BasePageVO.of(adminVOs, pageInfo);
    }

    @Override
    public MerchantAdminVO getMerchantAdminById(Long id) {
        // 查询用户基本信息
        SysUser user = tableUserService.selectById(id);
        if (user == null) {
            throw new BusinessException(ErrorCode.MERCHANT_ADMIN_NOT_FOUND);
        }

        // 检查是否为商户管理员
        if (user.getAccountType() == null || user.getAccountType() != 1) {
            throw new BusinessException(ErrorCode.MERCHANT_ADMIN_NOT_FOUND);
        }

        // 组装商户管理员详情
        MerchantAdminVO adminVO = new MerchantAdminVO();
        BeanUtils.copyProperties(user, adminVO);

        // 查询商户名称
        if (user.getTenantId() != null) {
            SysTenant tenant = tableTenantService.selectById(user.getTenantId());
            if (tenant != null) {
                adminVO.setTenantName(tenant.getTenantName());
            }
        }

        // 查询角色名称列表
        List<SysRole> roles = tableRoleService.findByUserId(id);
        List<String> roleNames = roles.stream()
                .map(SysRole::getRoleName)
                .collect(Collectors.toList());
        adminVO.setRoleNames(roleNames);

        return adminVO;
    }
}