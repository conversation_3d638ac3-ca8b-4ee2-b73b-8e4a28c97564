package com.extracme.saas.autocare.service;

import java.math.BigDecimal;

import com.extracme.saas.autocare.model.dto.RepairAmountDTO;

/**
 * 维修金额服务接口
 */
public interface RepairAmountService {
    
    /**
     * 获取自费金额
     * 
     * @param repairAmountDTO 维修金额DTO
     * @param accidentDamage 事故损失
     * @return 自费金额
     */
    BigDecimal getSelfFundedAmount(RepairAmountDTO repairAmountDTO, BigDecimal accidentDamage);
    
    /**
     * 同步自费金额账单
     * 
     * @param taskNo 任务编号
     */
    void syncSelfFundedAmountBill(String taskNo);
}
