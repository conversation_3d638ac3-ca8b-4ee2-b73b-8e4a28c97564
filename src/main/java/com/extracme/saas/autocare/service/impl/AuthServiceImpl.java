package com.extracme.saas.autocare.service.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.enums.ErrorCode;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.TokenDTO;
import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TablePermissionService;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableUserService;
import com.extracme.saas.autocare.service.AuthService;
import com.extracme.saas.autocare.service.SmsService;
import com.extracme.saas.autocare.util.JwtUtil;

import lombok.RequiredArgsConstructor;

/**
 * 身份认证服务实现类
 */
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private static final Logger logger = LoggerFactory.getLogger(AuthServiceImpl.class);
    private static final String LOGIN_TYPE = "LOGIN";

    @Value("${jwt.expiration:86400}") // 默认24小时
    private int jwtExpiration;

    private final SmsService smsService;
    private final TableUserService userService;
    private final TableRoleService roleService;
    private final TablePermissionService permissionService;
    private final TableTenantService tenantService;
    private final JwtUtil jwtUtil;

    /**
     * 通过短信验证码登录
     *
     * @param mobile 手机号
     * @param code 验证码
     * @return 登录成功返回token信息，失败返回null
     */
    @Override
    @Transactional
    public TokenDTO loginByMobile(String mobile, String code) {
        // 1. 查询用户是否存在
        Optional<SysUser> userOpt = userService.findByMobile(mobile);
        if (!userOpt.isPresent()) {
            logger.warn("用户不存在, 手机号: {}", mobile);
            return null;
        }

        SysUser user = userOpt.get();

        // 2. 检查用户状态
        if (user.getStatus() != 1) {
            logger.warn("用户已禁用, ID: {}, 手机号: {}", user.getId(), mobile);
            return null;
        }

        // 3. 验证验证码
        if (!smsService.verifyCode(mobile, code, LOGIN_TYPE)) {
            logger.warn("验证码校验失败, 手机号: {}", mobile);
            return null;
        }

        // 4. 标记验证码为已使用
        smsService.markCodeAsUsed(mobile, code, LOGIN_TYPE);

        // 5. 生成JWT Token
        String token = jwtUtil.generateToken(String.valueOf(user.getId()));

        // 6. 构建返回结果
        TokenDTO tokenDTO = new TokenDTO();
        tokenDTO.setAccessToken(token);
        tokenDTO.setUserId(user.getId());
        tokenDTO.setUsername(user.getUsername());
        tokenDTO.setMobile(user.getMobile());

        // 设置过期时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.SECOND, jwtExpiration);
        tokenDTO.setExpireTime(calendar.getTime());

        logger.info("用户登录成功, ID: {}, 手机号: {}", user.getId(), mobile);
        return tokenDTO;
    }

    /**
     * 退出登录
     *
     * @param token 用户令牌
     * @return 是否成功退出
     */
    @Override
    public boolean logout(String token) {
        // 实际应用中，可以将token加入黑名单或从缓存中删除
        logger.info("用户退出登录, token: {}", token);
        return true;
    }

    /**
     * 发送登录验证码
     *
     * @param mobile 手机号
     * @param ipAddress 请求IP地址
     * @return 是否发送成功
     */
    @Override
    public boolean sendLoginCode(String mobile, String ipAddress) {
        return smsService.sendLoginVerificationCode(mobile, ipAddress);
    }

    /**
     * 检查用户是否登录
     *
     * @param token 用户令牌
     * @return 是否已登录
     */
    @Override
    public boolean isLoggedIn(String token) {
        return token != null && !token.isEmpty() && jwtUtil.validateToken(token);
    }

    @Override
    public boolean validateToken(String token) {
        return jwtUtil.validateToken(token);
    }

    @Override
    public String getUserIdFromToken(String token) {
        return jwtUtil.getUserIdFromToken(token);
    }

    @Override
    public List<SysRole> getUserRoles(String token) {
        String userId = jwtUtil.getUserIdFromToken(token);
        return roleService.findByUserId(Long.parseLong(userId));
    }

    @Override
    public boolean hasRole(String token, String roleCode) {
        String userId = jwtUtil.getUserIdFromToken(token);
        return roleService.hasRole(Long.parseLong(userId), roleCode);
    }

    @Override
    public List<SysPermission> getUserPermissions(String token) {
        String userId = jwtUtil.getUserIdFromToken(token);
        return permissionService.findByUserId(Long.parseLong(userId));
    }

    @Override
    public boolean hasPermission(String token, String permissionCode) {
        if (!validateToken(token)) {
            return false;
        }
        String userId = jwtUtil.getUserIdFromToken(token);
        return permissionService.hasPermission(Long.parseLong(userId), permissionCode);
    }

    /**
     * 通过短信验证码登录（支持多租户）
     *
     * @param mobile 手机号
     * @param code 验证码
     * @param tenantId 租户ID（可选）
     * @return 登录成功返回token信息，失败抛出异常
     */
    @Override
    @Transactional
    public TokenDTO loginByMobileWithTenant(String mobile, String code, Long tenantId) {
        logger.debug("开始多租户登录验证, 手机号: {}, 租户ID: {}", mobile, tenantId);

        // 1. 验证验证码
        if (!smsService.verifyCode(mobile, code, LOGIN_TYPE)) {
            logger.warn("验证码校验失败, 手机号: {}", mobile);
            throw new BusinessException(ErrorCode.USERNAME_OR_PASSWORD_ERROR);
        }

        // 2. 查询用户关联的所有租户ID
        List<Long> tenantIds = userService.findTenantIdsByMobile(mobile);
        logger.debug("用户[{}]关联的租户ID列表: {}", mobile, tenantIds);

        if (tenantIds.isEmpty()) {
            logger.warn("用户未关联任何商户账号, 手机号: {}", mobile);
            throw new BusinessException(ErrorCode.NO_TENANT_ACCOUNTS);
        }

        SysUser user;
        if (tenantId != null) {
            // 3a. 如果指定了租户ID，验证用户是否有权限访问该租户
            if (!tenantIds.contains(tenantId)) {
                logger.warn("用户无权限访问指定租户, 手机号: {}, 租户ID: {}", mobile, tenantId);
                throw new BusinessException(ErrorCode.TENANT_ACCESS_DENIED);
            }

            // 查询指定租户下的用户
            Optional<SysUser> userOpt = userService.findByMobileAndTenantId(mobile, tenantId);
            if (!userOpt.isPresent()) {
                logger.warn("指定租户下用户不存在, 手机号: {}, 租户ID: {}", mobile, tenantId);
                throw new BusinessException(ErrorCode.USER_NOT_FOUND);
            }
            user = userOpt.get();
        } else {
            // 3b. 如果未指定租户ID，检查用户是否有多个商户账号
            if (tenantIds.size() > 1) {
                logger.warn("用户存在多个商户账号，需要选择具体租户, 手机号: {}, 租户数量: {}", mobile, tenantIds.size());
                throw new BusinessException(ErrorCode.MULTIPLE_TENANT_ACCOUNTS);
            }

            // 只有一个商户账号，自动登录
            Long singleTenantId = tenantIds.get(0);
            Optional<SysUser> userOpt = userService.findByMobileAndTenantId(mobile, singleTenantId);
            if (!userOpt.isPresent()) {
                logger.warn("用户不存在, 手机号: {}, 租户ID: {}", mobile, singleTenantId);
                throw new BusinessException(ErrorCode.USER_NOT_FOUND);
            }
            user = userOpt.get();
        }

        // 4. 检查用户状态
        if (user.getStatus() != 1) {
            logger.warn("用户已禁用, ID: {}, 手机号: {}", user.getId(), mobile);
            throw new BusinessException(ErrorCode.ACCOUNT_LOCKED);
        }

        // 5. 标记验证码为已使用
        smsService.markCodeAsUsed(mobile, code, LOGIN_TYPE);

        // 6. 生成JWT Token
        String token = jwtUtil.generateToken(String.valueOf(user.getId()));

        // 7. 构建返回结果
        TokenDTO tokenDTO = new TokenDTO();
        tokenDTO.setAccessToken(token);
        tokenDTO.setUserId(user.getId());
        tokenDTO.setUsername(user.getUsername());
        tokenDTO.setMobile(user.getMobile());

        // 设置过期时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.SECOND, jwtExpiration);
        tokenDTO.setExpireTime(calendar.getTime());

        logger.info("用户多租户登录成功, ID: {}, 手机号: {}, 租户ID: {}", user.getId(), mobile, user.getTenantId());
        return tokenDTO;
    }

    /**
     * 根据手机号查询用户名下所有商户账号
     *
     * @param mobile 手机号
     * @return 商户账号列表，ID为租户ID，value为租户名称
     */
    @Override
    public List<ComboVO<String>> getUserTenantAccounts(String mobile) {
        logger.debug("查询用户商户账号列表, 手机号: {}", mobile);

        // 1. 查询用户关联的所有租户ID
        List<Long> tenantIds = userService.findTenantIdsByMobile(mobile);
        if (tenantIds.isEmpty()) {
            logger.debug("用户未关联任何商户账号, 手机号: {}", mobile);
            return new ArrayList<>();
        }

        // 2. 查询租户信息并构建下拉列表
        List<ComboVO<String>> result = new ArrayList<>();
        for (Long tenantId : tenantIds) {
            SysTenant tenant = tenantService.selectById(tenantId);
            if (tenant != null && tenant.getStatus() == 1) { // 只返回启用的租户
                ComboVO<String> combo = new ComboVO<>();
                combo.setId(String.valueOf(tenant.getId()));
                combo.setValue(tenant.getTenantName());
                result.add(combo);
            }
        }

        logger.debug("用户[{}]的商户账号列表查询完成, 共{}个账号", mobile, result.size());
        return result;
    }
}