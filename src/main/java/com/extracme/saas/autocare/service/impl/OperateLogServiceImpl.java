package com.extracme.saas.autocare.service.impl;

import com.extracme.saas.autocare.enums.OperateLogModuleTypeEnum;
import com.extracme.saas.autocare.enums.OperateLogOperateTypeEnum;
import com.extracme.saas.autocare.model.dto.OperateLogQueryDTO;
import com.extracme.saas.autocare.model.entity.SysOperateLog;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.model.vo.OperateLogVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TableSysOperateLogService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.service.OperateLogService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 操作日志服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OperateLogServiceImpl implements OperateLogService {

    private final TableSysOperateLogService tableSysOperateLogService;
    private final TableTenantService tableTenantService;

    /**
     * 格式化用户名称为"昵称【手机号】"格式
     *
     * @param nickname 用户昵称
     * @param mobile 用户手机号
     * @return 格式化后的用户名称
     */
    private String formatUserDisplayName(String nickname, String mobile) {
        // 参数校验
        if (!StringUtils.hasText(nickname) && !StringUtils.hasText(mobile)) {
            return "未知用户";
        }
        
        // 如果昵称为空，使用手机号
        if (!StringUtils.hasText(nickname)) {
            return StringUtils.hasText(mobile) ? mobile : "未知用户";
        }
        
        // 如果手机号为空，只使用昵称
        if (!StringUtils.hasText(mobile)) {
            return nickname;
        }
        
        // 正常情况：昵称【手机号】
        return nickname + "【" + mobile + "】";
    }

    @Override
    public BasePageVO<OperateLogVO> getOperateLogList(OperateLogQueryDTO queryDTO) {
        // 判断当前用户是否为超级管理员
        boolean isSuperAdmin = SessionUtils.isSuperAdmin();

        // 根据用户类型决定租户ID过滤条件
        Long tenantIdFilter = null;
        if (!isSuperAdmin) {
            // 普通用户只能查询自己所属租户下的操作日志
            LoginUser currentLoginUser = SessionUtils.getLoginUser();
            if (currentLoginUser != null && currentLoginUser.getUser() != null) {
                tenantIdFilter = currentLoginUser.getUser().getTenantId();
            }
        }
        // 超级管理员不设置租户ID过滤条件，可以查询所有租户的操作日志

        // 开启分页
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 查询操作日志列表
        List<SysOperateLog> operateLogs = tableSysOperateLogService.findByCondition(tenantIdFilter);

        // 获取分页信息
        PageInfo<SysOperateLog> pageInfo = new PageInfo<>(operateLogs);

        // 如果没有查询到操作日志，直接返回空结果
        if (operateLogs.isEmpty()) {
            return BasePageVO.of(Collections.emptyList(), pageInfo);
        }

        // 一次性查询所有租户信息，构建租户ID与租户名称的映射关系
        List<SysTenant> allTenants = tableTenantService.findByCondition(null, null, null);
        Map<Long, String> tenantIdToNameMap = allTenants.stream()
            .collect(Collectors.toMap(SysTenant::getId, SysTenant::getTenantName, (existing, replacement) -> existing));

        // 在内存中组装数据
        List<OperateLogVO> voList = operateLogs.stream().map(operateLog -> {
            OperateLogVO vo = new OperateLogVO();
            BeanUtils.copyProperties(operateLog, vo);

            // 从Map中获取租户名称并设置
            if (operateLog.getTenantId() != null) {
                String tenantName = tenantIdToNameMap.get(operateLog.getTenantId());
                vo.setTenantName(tenantName);
            }

            // 设置模块类型名称
            vo.setModuleTypeName(OperateLogModuleTypeEnum.getNameByCode(operateLog.getModuleType()));

            // 设置操作类型名称
            vo.setOperateTypeName(OperateLogOperateTypeEnum.getNameByCode(operateLog.getOperateType()));

            return vo;
        }).collect(Collectors.toList());

        return BasePageVO.of(voList, pageInfo);
    }

    @Override
    @Async
    public void recordOperateLogAsync(OperateLogModuleTypeEnum moduleType,
                                     OperateLogOperateTypeEnum operateType,
                                     String content,
                                     String miscDesc) {
        try {
            // 获取当前登录用户信息
            LoginUser loginUser = SessionUtils.getLoginUser();
            if (loginUser == null || loginUser.getUser() == null) {
                log.warn("无法获取当前登录用户信息，跳过操作日志记录");
                return;
            }

            // 调用带用户信息参数的方法
            recordOperateLogAsync(moduleType, operateType, content, miscDesc,
                    loginUser.getUser().getId(),
                    loginUser.getUser().getNickname(),
                    loginUser.getUser().getMobile(),
                    loginUser.getUser().getTenantId());

        } catch (Exception e) {
            log.error("记录操作日志失败：模块={}, 操作={}, 内容={}",
                     moduleType.getName(), operateType.getName(), content, e);
        }
    }

    @Override
    @Async
    public void recordOperateLogAsync(OperateLogModuleTypeEnum moduleType,
                                     OperateLogOperateTypeEnum operateType,
                                     String content) {
        recordOperateLogAsync(moduleType, operateType, content, null);
    }

    @Override
    @Async
    public void recordOperateLogAsync(OperateLogModuleTypeEnum moduleType,
                                     OperateLogOperateTypeEnum operateType,
                                     String content,
                                     String miscDesc,
                                     Long userId,
                                     String nickname,
                                     String mobile,
                                     Long tenantId) {
        try {
            // 验证必要的用户信息
            if (userId == null || tenantId == null) {
                log.warn("用户信息不完整，跳过操作日志记录：userId={}, tenantId={}",
                        userId, tenantId);
                return;
            }

            // 格式化用户显示名称
            String formattedUserName = formatUserDisplayName(nickname, mobile);

            // 创建操作日志对象
            SysOperateLog operateLog = new SysOperateLog();
            operateLog.setTenantId(tenantId);
            operateLog.setModuleType(moduleType.getCode());
            operateLog.setOperateType(operateType.getCode());
            operateLog.setContent(content);
            operateLog.setMiscDesc(miscDesc);
            operateLog.setStatus(1); // 有效状态

            // 异步记录操作日志，使用格式化后的用户名称
            tableSysOperateLogService.insert(operateLog, formattedUserName);

            log.debug("操作日志记录成功：模块={}, 操作={}, 内容={}, 操作人={}, 租户ID={}",
                     moduleType.getName(), operateType.getName(), content, formattedUserName, tenantId);

        } catch (Exception e) {
            log.error("记录操作日志失败：模块={}, 操作={}, 内容={}, 昵称={}, 手机号={}, 租户ID={}",
                     moduleType.getName(), operateType.getName(), content, nickname, mobile, tenantId, e);
        }
    }

    @Override
    @Async
    public void recordOperateLogAsync(OperateLogModuleTypeEnum moduleType,
                                     OperateLogOperateTypeEnum operateType,
                                     String content,
                                     Long userId,
                                     String nickname,
                                     String mobile,
                                     Long tenantId) {
        recordOperateLogAsync(moduleType, operateType, content, null, userId, nickname, mobile, tenantId);
    }
}
