package com.extracme.saas.autocare.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.model.entity.MtcVehicleInfo;
import com.extracme.saas.autocare.repository.TableVehicleInfoService;
import com.extracme.saas.autocare.service.VehicleInfoService;

import lombok.extern.slf4j.Slf4j;

/**
 * 车辆信息服务实现类
 */
@Slf4j
@Service
public class VehicleInfoServiceImpl implements VehicleInfoService {

    @Autowired
    private TableVehicleInfoService tableVehicleInfoService;

    @Override
    @Transactional(readOnly = true)
    public MtcVehicleInfo getVehicleInfoByVin(String vin) {
        try {
            MtcVehicleInfo vehicleInfo = tableVehicleInfoService.findByVin(vin);
            return vehicleInfo ;
        } catch (Exception e) {
            log.error("根据车架号查询车辆详情失败", e);
            throw new RuntimeException("查询车辆详情失败", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<MtcVehicleInfo> getVehicleInfoList(String vin, String vehicleNo) {
        try {
            // 限制最多返回20条数据
            return tableVehicleInfoService.findByCondition(vin, vehicleNo, 20);
        } catch (Exception e) {
            log.error("根据车架号或车牌号查询车辆列表失败", e);
            throw new RuntimeException("查询车辆列表失败", e);
        }
    }
}
