package com.extracme.saas.autocare.service.impl;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.extracme.saas.autocare.enums.ActivityDefinitionEnum;
import com.extracme.saas.autocare.enums.ActivityStatusEnum;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.workflow.ActivityNodeDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityStatusTransitionDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionCreateDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionDeleteDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionQueryDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionUpdateDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowInstanceQueryDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowProcessDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowStartDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowTemplateCreateDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowTemplateQueryDTO;
import com.extracme.saas.autocare.model.entity.ActivityDefinition;
import com.extracme.saas.autocare.model.entity.ActivityInstance;
import com.extracme.saas.autocare.model.entity.ActivityStatusTransition;
import com.extracme.saas.autocare.model.entity.ActivityTransition;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.model.entity.WorkflowActivity;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.model.entity.WorkflowTemplate;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.workflow.ActivityDefinitionVO;
import com.extracme.saas.autocare.model.vo.workflow.ActivityInstanceVO;
import com.extracme.saas.autocare.model.vo.workflow.ActivityStatusTransitionVO;
import com.extracme.saas.autocare.model.vo.workflow.ActivityTransitionDetailVO;
import com.extracme.saas.autocare.model.vo.workflow.ActivityTransitionVO;
import com.extracme.saas.autocare.model.vo.workflow.ProgressNodeVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowInstanceDetailVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowInstanceVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowProgressVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowTemplateDetailVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowTemplateVO;
import com.extracme.saas.autocare.repository.TableActivityDefinitionService;
import com.extracme.saas.autocare.repository.TableActivityInstanceService;
import com.extracme.saas.autocare.repository.TableActivityStatusTransitionService;
import com.extracme.saas.autocare.repository.TableActivityTransitionService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.repository.TableWorkflowActivityService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.repository.TableWorkflowTemplateService;
import com.extracme.saas.autocare.service.WorkflowService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.workflow.core.WorkflowContext;
import com.extracme.saas.autocare.workflow.service.EventHandlerService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * 工作流服务实现类
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Slf4j
@Service
public class WorkflowServiceImpl implements WorkflowService {

    @Autowired
    private TableActivityDefinitionService tableActivityDefinitionService;
    @Autowired
    private TableWorkflowTemplateService tableWorkflowTemplateService;
    @Autowired
    private TableWorkflowActivityService tableWorkflowActivityService;
    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;
    @Autowired
    private TableActivityInstanceService tableActivityInstanceService;
    @Autowired
    private TableActivityTransitionService tableActivityTransitionService;
    @Autowired
    private TableActivityStatusTransitionService tableActivityStatusTransitionService;
    @Autowired
    private TableTenantService tableTenantService;
    @Autowired
    private EventHandlerService eventHandlerService;

    @Override
    @Transactional
    public Long createTemplate(WorkflowTemplateCreateDTO createDTO) {
        log.info("创建工作流模板，参数: {}", createDTO);

        try {
            // 1. 创建工作流模板
            WorkflowTemplate template = new WorkflowTemplate();
            template.setWorkflowName(createDTO.getWorkflowName());
            template.setDescription(createDTO.getDescription());
            template.setTaskType(createDTO.getTaskType());
            template.setRepairFactoryType(createDTO.getRepairFactoryType());
            template.setSubProductLine(createDTO.getSubProductLine());
            template.setIsActive(createDTO.getIsActive());
            template.setTenantId(SessionUtils.getTenantId().intValue());

            // 2. 保存工作流模板，使用当前登录用户作为操作人
            String operator = SessionUtils.getUsername();
            tableWorkflowTemplateService.insert(template, operator);
            Long templateId = template.getId();

            // 3. 处理活动节点
            if (createDTO.getActivityCodeList() != null && !createDTO.getActivityCodeList().isEmpty()) {
                List<WorkflowActivity> workflowActivities = new ArrayList<>();

                // 3.1 一次性查询所有相关的ActivityDefinition记录
                List<String> activityCodes = createDTO.getActivityCodeList();

                // 3.2 将查询结果存储在Map中，以activityCode为键
                Map<String, ActivityDefinition> activityDefinitionMap = new HashMap<>();

                // 对每个活动编码进行查询，并存入Map
                for (String activityCode : activityCodes) {
                    ActivityDefinition definition = tableActivityDefinitionService.selectByActivityCode(activityCode);
                    if (definition != null) {
                        activityDefinitionMap.put(activityCode, definition);
                    }
                }

                // 3.3 处理每个活动节点
                for (String activityCode : activityCodes) {
                    // 从Map中获取ActivityDefinition，而不是每次都查询数据库
                    ActivityDefinition activityDefinition = activityDefinitionMap.get(activityCode);

                    // 保留原有的空值检查逻辑
                    if (activityDefinition == null) {
                        throw new BusinessException("活动节点定义不存在");
                    }

                    // 3.4 创建工作流与活动节点的关联
                    WorkflowActivity workflowActivity = new WorkflowActivity();
                    workflowActivity.setWorkflowId(templateId);
                    workflowActivity.setActivityCode(activityDefinition.getActivityCode());

                    workflowActivities.add(workflowActivity);
                }

                // 3.5 批量保存工作流与活动节点的关联
                if (!workflowActivities.isEmpty()) {
                    tableWorkflowActivityService.batchInsert(workflowActivities);
                }
            }

            log.info("工作流模板创建成功，ID: {}", templateId);
            return templateId;
        } catch (Exception e) {
            log.error("创建工作流模板失败", e);
            throw new BusinessException("创建工作流模板失败: " + e.getMessage());
        }
    }

    // 根据业务需求，删除了工作流模板更新方法，工作流模板创建后不允许修改，只能创建新的模板

    @Override
    public WorkflowTemplateDetailVO getTemplateDetail(Long templateId) {
        log.info("获取工作流模板详情，ID: {}", templateId);

        try {
            // 1. 查询工作流模板
            WorkflowTemplate template = tableWorkflowTemplateService.selectById(templateId);
            if (template == null) {
                throw new BusinessException("工作流模板不存在");
            }

            // 2. 创建详情VO对象
            WorkflowTemplateDetailVO detailVO = new WorkflowTemplateDetailVO();
            BeanUtils.copyProperties(template, detailVO);

            // 3. 查询关联的活动节点
            List<ActivityDefinition> activityDefinitions = tableActivityDefinitionService.selectByWorkflowId(templateId);
            List<ActivityDefinitionVO> activityDefinitionVOs = convertToActivityDefinitionVOs(activityDefinitions);
            detailVO.setActivityDefinitions(activityDefinitionVOs);

            // 4. 转换为活动节点DTO
            List<ActivityNodeDTO> activityNodes = new ArrayList<>();
            for (ActivityDefinition definition : activityDefinitions) {
                ActivityNodeDTO nodeDTO = new ActivityNodeDTO();
                nodeDTO.setActivityCode(definition.getActivityCode());
                nodeDTO.setActivityName(definition.getActivityName());
                nodeDTO.setDescription(definition.getDescription());
                nodeDTO.setSequence(definition.getSequence());
                nodeDTO.setIsEnabled(definition.getIsEnabled() != null && definition.getIsEnabled() == 1);

                activityNodes.add(nodeDTO);
            }
            detailVO.setActivityNodes(activityNodes);

            log.info("获取工作流模板详情成功");
            return detailVO;
        } catch (Exception e) {
            log.error("获取工作流模板详情失败", e);
            throw new BusinessException("获取工作流模板详情失败: " + e.getMessage());
        }
    }

    @Override
    public List<WorkflowTemplateVO> listTemplates(WorkflowTemplateQueryDTO queryDTO) {
        log.info("查询工作流模板列表，查询条件: {}", queryDTO);

        try {
            // 查询工作流模板列表
            List<WorkflowTemplate> templates = tableWorkflowTemplateService.findByCondition(queryDTO);
            if (templates.isEmpty()) {
                log.info("未查询到符合条件的工作流模板");
                return new ArrayList<>();
            }

            // 收集所有租户ID，用于批量查询租户信息
            Set<Integer> tenantIds = templates.stream()
                    .map(WorkflowTemplate::getTenantId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            // 批量查询租户信息，构建tenantId到tenantCode的映射
            Map<Integer, String> tenantCodeMap = new HashMap<>();
            if (!tenantIds.isEmpty()) {
                try {
                    // 一次性查询所有租户数据（包括启用和禁用状态的租户）
                    List<SysTenant> allTenants = tableTenantService.findByCondition(null, null, null);

                    // 构建完整的tenantId到tenantCode的映射Map
                    tenantCodeMap = allTenants.stream()
                            .filter(tenant -> tenant.getTenantCode() != null)
                            .collect(Collectors.toMap(
                                    tenant -> tenant.getId().intValue(),
                                    SysTenant::getTenantCode,
                                    (existing, replacement) -> existing // 处理重复key的情况
                            ));

                    log.info("一次性查询到{}个租户信息，构建租户映射完成", allTenants.size());
                } catch (Exception e) {
                    log.warn("批量查询租户信息失败，错误: {}", e.getMessage());
                    // 异常情况下使用空映射，确保程序继续执行
                    tenantCodeMap = new HashMap<>();
                }
            }

            // 转换为VO对象
            final Map<Integer, String> finalTenantCodeMap = tenantCodeMap;
            List<WorkflowTemplateVO> templateVOs = templates.stream().map(t -> {
                WorkflowTemplateVO vo = new WorkflowTemplateVO();
                BeanUtils.copyProperties(t, vo);

                // 设置租户编码
                if (t.getTenantId() != null) {
                    String tenantCode = finalTenantCodeMap.get(t.getTenantId());
                    vo.setTenantCode(tenantCode);
                }

                // 查询关联的活动节点定义
                List<ActivityDefinition> activityDefinitions = tableActivityDefinitionService.selectByWorkflowId(t.getId());
                List<ActivityDefinitionVO> activityDefinitionVOs = convertToActivityDefinitionVOs(activityDefinitions);
                vo.setActivityDefinitions(activityDefinitionVOs);

                return vo;
            }).collect(Collectors.toList());

            log.info("查询到{}个工作流模板", templateVOs.size());
            return templateVOs;
        } catch (Exception e) {
            log.error("查询工作流模板列表异常", e);
            throw new BusinessException("查询工作流模板列表失败: " + e.getMessage());
        }
    }



    @Override
    public BasePageVO<WorkflowTemplateVO> getTemplateList(WorkflowTemplateQueryDTO queryDTO) {
        // 设置分页参数
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 查询数据
        List<WorkflowTemplateVO> list = listTemplates(queryDTO);

        // 创建PageInfo对象
        PageInfo<WorkflowTemplateVO> pageInfo = new PageInfo<>(list);

        // 转换为BasePageVO
        return BasePageVO.of(list, pageInfo);
    }

    /**
     * 启动工作流实例
     *
     * @param startDTO 启动工作流实例的参数
     * @return 创建的工作流实例ID
     */
    @Override
    @Transactional
    public Long startWorkflow(WorkflowStartDTO startDTO) {
        log.info("启动工作流实例，参数: {}", startDTO);

        // 参数验证
        validateStartWorkflowParams(startDTO);

        try {
            // 1. 根据条件查找匹配的工作流模板
            WorkflowTemplate template = findMatchingWorkflowTemplate(
                    startDTO.getTaskType(),
                    startDTO.getRepairFactoryType(),
                    startDTO.getSubProductLine()
            );

            // 2. 检查业务对象是否已存在工作流实例
            checkBusinessIdUniqueness(startDTO.getBusinessId());

            // 3. 获取初始活动节点
            ActivityDefinition firstActivity = getInitialActivityNode(template.getId());

            // 4. 创建工作流实例
            Long instanceId = createWorkflowInstance(startDTO, template, firstActivity);

            // 5. 创建初始活动实例记录
            createInitialActivityInstance(instanceId, firstActivity, startDTO);

            log.info("工作流实例启动成功，ID: {}, 使用模板: {}", instanceId, template.getId());
            return instanceId;
        } catch (BusinessException e) {
            log.error("启动工作流实例失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("启动工作流实例失败", e);
            throw new BusinessException("启动工作流实例失败: " + e.getMessage());
        }
    }

    /**
     * 验证启动工作流实例的参数
     *
     * @param startDTO 启动工作流实例的参数
     */
    private void validateStartWorkflowParams(WorkflowStartDTO startDTO) {
        if (startDTO == null) {
            throw new BusinessException("启动参数不能为空");
        }
        if (startDTO.getTaskType() == null) {
            throw new BusinessException("任务类型不能为空");
        }
        if (startDTO.getRepairFactoryType() == null) {
            throw new BusinessException("修理厂类型不能为空");
        }
        if (startDTO.getSubProductLine() == null) {
            throw new BusinessException("子产品线不能为空");
        }
        if (startDTO.getBusinessId() == null || startDTO.getBusinessId().isEmpty()) {
            throw new BusinessException("业务对象ID不能为空");
        }
        if (startDTO.getOperator() == null || startDTO.getOperator().isEmpty()) {
            throw new BusinessException("操作人不能为空");
        }
    }

    /**
     * 根据条件查找匹配的工作流模板
     *
     * @param taskType 任务类型
     * @param repairFactoryType 修理厂类型
     * @param subProductLine 子产品线
     * @return 匹配的工作流模板
     */
    private WorkflowTemplate findMatchingWorkflowTemplate(Integer taskType, Integer repairFactoryType, Integer subProductLine) {
        // 创建查询条件
        WorkflowTemplateQueryDTO queryDTO = new WorkflowTemplateQueryDTO();
        queryDTO.setTaskType(taskType);
        queryDTO.setRepairFactoryType(repairFactoryType);
        queryDTO.setSubProductLine(subProductLine);
        queryDTO.setIsActive(1); // 只查询启用状态的模板

        // 获取当前租户ID并添加到查询条件中
        Long tenantId = SessionUtils.getTenantId();
        log.info("当前租户ID: {}", tenantId);

        // 查询匹配的模板
        List<WorkflowTemplate> templates = tableWorkflowTemplateService.findByCondition(queryDTO);

        // 根据租户ID过滤模板
        if (tenantId != null) {
            templates = templates.stream()
                    .filter(template -> tenantId.intValue() == template.getTenantId())
                    .collect(Collectors.toList());
            log.info("按租户ID过滤后的模板数量: {}", templates.size());
        }

        // 处理查询结果
        if (templates.isEmpty()) {
            throw new BusinessException(String.format(
                    "未找到匹配的工作流模板，任务类型: %d, 修理厂类型: %d, 子产品线: %d, 租户ID: %s",
                    taskType, repairFactoryType, subProductLine, tenantId));
        }

        // 如果找到多个匹配的模板，抛出业务异常
        if (templates.size() > 1) {
            log.error("找到多个匹配的工作流模板，请确保模板配置唯一性");
            throw new BusinessException("找到多个匹配的工作流模板，请联系管理员检查模板配置");
        }

        WorkflowTemplate template = templates.get(0);
        log.info("找到匹配的工作流模板: ID={}, 名称={}, 租户ID={}", template.getId(), template.getWorkflowName(), template.getTenantId());
        return template;
    }

    /**
     * 检查业务对象是否已存在工作流实例
     *
     * @param businessId 业务对象ID
     */
    private void checkBusinessIdUniqueness(String businessId) {
        WorkflowInstance existingInstance = tableWorkflowInstanceService.selectByBusinessId(businessId);
        if (existingInstance != null) {
            throw new BusinessException("该业务对象已存在工作流实例");
        }
    }

    /**
     * 获取初始活动节点
     *
     * @param workflowId 工作流模板ID
     * @return 初始活动节点
     */
    private ActivityDefinition getInitialActivityNode(Long workflowId) {
        // 直接使用getAllActivityDefinitions方法，该方法已经包含了排序逻辑
        List<ActivityDefinition> activityDefinitions = getAllActivityDefinitions(workflowId);

        // 获取序列号最小的节点
        return activityDefinitions.stream()
                .min(Comparator.comparing(ActivityDefinition::getSequence))
                .orElseThrow(() -> new BusinessException("无法确定初始活动节点"));
    }

    /**
     * 创建工作流实例
     *
     * @param startDTO 启动工作流实例的参数
     * @param template 工作流模板
     * @param firstActivity 初始活动节点
     * @return 工作流实例ID
     */
    private Long createWorkflowInstance(WorkflowStartDTO startDTO, WorkflowTemplate template, ActivityDefinition firstActivity) {
        WorkflowInstance instance = new WorkflowInstance();
        instance.setWorkflowId(template.getId());
        instance.setBusinessId(startDTO.getBusinessId());

        // 固定使用VEHICLE_TRANSFER作为默认起始活动节点
        instance.setCurrentActivityCode(ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode());

        // 设置初始状态码为UNPROCESSED
        instance.setStatusCode(ActivityStatusEnum.UNPROCESSED.getCode());
        log.debug("工作流实例初始状态码设置为: {}", ActivityStatusEnum.UNPROCESSED.getCode());
        log.info("工作流实例默认起始节点设置为: {}", ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode());

        instance.setTenantId(template.getTenantId());

        tableWorkflowInstanceService.insert(instance, startDTO.getOperator());
        return instance.getId();
    }

    /**
     * 创建初始活动实例记录
     *
     * @param instanceId 工作流实例ID
     * @param firstActivity 初始活动节点
     * @param startDTO 启动工作流实例的参数
     */
    private void createInitialActivityInstance(Long instanceId, ActivityDefinition firstActivity, WorkflowStartDTO startDTO) {
        ActivityInstance activityInstance = new ActivityInstance();
        activityInstance.setInstanceId(instanceId);

        // 固定使用VEHICLE_TRANSFER作为默认起始活动节点
        activityInstance.setToActivityCode(ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode());
        activityInstance.setFromActivityCode(null); // 初始节点没有来源节点
        activityInstance.setTransitionId(null); // 初始节点没有转换规则
        activityInstance.setStartTime(new Date());

        // 设置初始状态码为UNPROCESSED
        activityInstance.setCurrentStatusCode(ActivityStatusEnum.UNPROCESSED.getCode());
        log.debug("活动实例初始状态码设置为: {}", ActivityStatusEnum.UNPROCESSED.getCode());
        log.info("活动实例默认起始节点设置为: {}", ActivityDefinitionEnum.VEHICLE_TRANSFER.getCode());

        activityInstance.setOperator(startDTO.getOperator());
        activityInstance.setRemarks(startDTO.getRemarks());

        tableActivityInstanceService.insert(activityInstance, startDTO.getOperator());
    }

    /**
     * 处理工作流节点
     *
     * <p>工作流节点处理的完整流程包括以下步骤：</p>
     * <ol>
     *   <li><strong>参数验证</strong> - 验证工作流实例ID、活动编码、触发事件等必要参数的有效性</li>
     *   <li><strong>实例验证</strong> - 获取并验证工作流实例存在性，确认当前节点与请求节点匹配</li>
     *   <li><strong>转换规则查找</strong> - 根据工作流ID、当前活动节点和触发事件查找匹配的转换规则</li>
     *   <li><strong>活动实例获取</strong> - 获取当前正在执行的活动实例记录</li>
     *   <li><strong>事件处理</strong> - 创建工作流上下文，调用相应的事件处理器执行业务逻辑</li>
     *   <li><strong>当前节点结束</strong> - 设置当前活动实例的结束时间和执行时长</li>
     *   <li><strong>新节点创建</strong> - 根据转换规则创建新的活动实例记录，设置开始时间和状态</li>
     *   <li><strong>实例状态更新</strong> - 更新工作流实例的当前活动节点和状态码</li>
     * </ol>
     *
     * <p>整个处理过程在事务中执行，确保数据一致性。如果任何步骤失败，将回滚所有操作。</p>
     *
     * @param instanceId 工作流实例ID
     * @param processDTO 节点处理参数
     * @throws BusinessException 当参数无效、实例不存在、转换规则不匹配或处理过程中发生业务异常时抛出
     */
    @Override
    @Transactional
    public void processNode(Long instanceId, WorkflowProcessDTO processDTO) {
        log.info("========== 工作流节点处理开始 ==========");
        log.info("处理工作流节点，实例ID: {}, 活动编码: {}, 触发事件: {}, 操作人: {}",
                instanceId, processDTO.getActivityCode(), processDTO.getTriggerEvent(), processDTO.getOperator());
        long startTime = System.currentTimeMillis();

        // 参数验证
        validateProcessNodeParams(instanceId, processDTO);

        // 此时processDTO已经通过验证，不会为null
        try {
            log.info("工作流节点处理事务开始，实例ID: {}", instanceId);

            // 1. 获取并验证工作流实例和当前节点
            WorkflowInstance instance = getAndValidateWorkflowInstance(instanceId, processDTO.getActivityCode());
            log.debug("工作流实例验证通过，实例ID: {}, 当前节点: {}, 业务ID: {}",
                    instance.getId(), instance.getCurrentActivityCode(), instance.getBusinessId());

            // 2. 查找转换规则
            ActivityTransition transition = findTransitionRule(instance.getWorkflowId(),
                    instance.getCurrentActivityCode(), processDTO.getTriggerEvent());
            log.debug("找到匹配的转换规则，规则ID: {}, 从节点: {} 到节点: {}",
                    transition.getId(), transition.getFromActivityCode(), transition.getToActivityCode());

            // 3. 获取当前活动实例记录
            ActivityInstance currentActivityInstance = getCurrentActivityInstance(instanceId, instance.getCurrentActivityCode());
            log.debug("获取当前活动实例记录成功，实例ID: {}, 活动实例ID: {}",
                    instanceId, currentActivityInstance.getId());

            // 3.5 创建工作流上下文并调用事件处理器
            WorkflowContext context = buildWorkflowContext(instance, transition, processDTO);
            String tenantId = instance.getTenantId().toString();
            log.debug("创建工作流上下文，租户ID: {}, 从节点: {}, 到节点: {}",
                    tenantId, context.getFromActivityId(), context.getToActivityId());

            try {
                log.debug("开始调用事件处理器，事件: {}, 租户ID: {}", processDTO.getTriggerEvent(), tenantId);
                boolean handled = eventHandlerService.handleEvent(context, tenantId);
                if (handled) {
                    log.info("事件处理器成功处理事件: {}, 实例ID: {}", processDTO.getTriggerEvent(), instanceId);
                } else {
                    log.info("未找到匹配的事件处理器，继续执行默认处理逻辑");
                }
            } catch (Exception e) {
                log.error("事件处理器执行失败: {}, 异常类型: {}, 异常信息: {}",
                        processDTO.getTriggerEvent(), e.getClass().getName(), e.getMessage(), e);
                throw new BusinessException("事件处理器执行失败: " + e.getMessage());
            }

            // 4. 结束当前活动实例
            Date now = new Date();
            log.debug("开始结束当前活动实例，活动实例ID: {}, 结束时间: {}",
                    currentActivityInstance.getId(), now);
            completeCurrentActivityInstance(currentActivityInstance, now);

            // 5. 创建新的活动实例记录
            log.debug("开始创建新的活动实例记录，实例ID: {}, 从节点: {}, 到节点: {}, 开始时间: {}",
                    instanceId, instance.getCurrentActivityCode(), transition.getToActivityCode(), now);
            createNextActivityInstance(instanceId, instance.getCurrentActivityCode(), transition, processDTO, now);

            // 6. 更新工作流实例状态
            log.debug("开始更新工作流实例状态，实例ID: {}, 新活动节点: {}, 转换规则ID: {}",
                    instance.getId(), transition.getToActivityCode(), transition.getId());
            // 修改updateWorkflowInstanceStatus方法调用，传递transition对象
            updateWorkflowInstanceStatus(instance, transition, processDTO.getOperator());

            long endTime = System.currentTimeMillis();
            log.info("工作流节点处理成功，实例ID: {}, 从节点: {} 转换到节点: {}, 处理耗时: {}ms",
                    instanceId, instance.getCurrentActivityCode(), transition.getToActivityCode(), (endTime - startTime));
            log.info("工作流节点处理事务结束，实例ID: {}", instanceId);
        } catch (BusinessException e) {
            log.error("处理工作流节点失败: {}, 实例ID: {}", e.getMessage(), instanceId);
            throw e;
        } catch (Exception e) {
            log.error("处理工作流节点失败, 实例ID: {}, 异常类型: {}, 异常信息: {}",
                    instanceId, e.getClass().getName(), e.getMessage(), e);
            throw new BusinessException("处理工作流节点失败: " + e.getMessage());
        } finally {
            log.info("========== 工作流节点处理结束 ==========");
        }
    }

    /**
     * 构建工作流上下文
     *
     * @param instance 工作流实例
     * @param transition 转换规则
     * @param processDTO 处理参数
     * @return 工作流上下文
     */
    private WorkflowContext buildWorkflowContext(
            WorkflowInstance instance,
            ActivityTransition transition,
            WorkflowProcessDTO processDTO) {

        return WorkflowContext.builder()
                .workflowInstance(instance)
                .fromActivityId(transition.getFromActivityCode())
                .toActivityId(transition.getToActivityCode())
                .triggerEvent(processDTO.getTriggerEvent())
                .tenantId(instance.getTenantId())
                .operator(processDTO.getOperator())
                .extraParams(processDTO.getRemarks())
                .build();
    }

    /**
     * 验证处理工作流节点的参数
     *
     * @param instanceId 工作流实例ID
     * @param processDTO 节点处理参数
     */
    private void validateProcessNodeParams(Long instanceId, WorkflowProcessDTO processDTO) {
        log.debug("开始验证工作流节点处理参数，实例ID: {}", instanceId);

        if (instanceId == null) {
            log.error("参数验证失败：工作流实例ID为空");
            throw new BusinessException("工作流实例ID不能为空");
        }

        if (processDTO == null) {
            log.error("参数验证失败：处理参数对象为空，实例ID: {}", instanceId);
            throw new BusinessException("处理参数不能为空");
        }

        log.debug("处理参数内容：活动编码: {}, 触发事件: {}, 操作人: {}, 备注: {}",
                processDTO.getActivityCode(),
                processDTO.getTriggerEvent(),
                processDTO.getOperator(),
                processDTO.getRemarks());

        if (processDTO.getActivityCode() == null || processDTO.getActivityCode().isEmpty()) {
            log.error("参数验证失败：活动节点编码为空，实例ID: {}", instanceId);
            throw new BusinessException("活动节点编码不能为空");
        }

        if (processDTO.getTriggerEvent() == null || processDTO.getTriggerEvent().isEmpty()) {
            log.error("参数验证失败：触发事件为空，实例ID: {}, 活动编码: {}",
                    instanceId, processDTO.getActivityCode());
            throw new BusinessException("触发事件不能为空");
        }

        // 从会话中获取当前用户作为操作人
        try {
            String operator = SessionUtils.getLoginUser().getUsername();
            log.debug("从会话中获取当前用户作为操作人: {}", operator);
            processDTO.setOperator(operator);
        } catch (Exception e) {
            log.warn("获取当前用户信息失败，使用默认操作人: system, 异常信息: {}", e.getMessage());
            processDTO.setOperator("system");
        }

        log.debug("参数验证通过，实例ID: {}, 活动编码: {}, 触发事件: {}, 操作人: {}",
                instanceId,
                processDTO.getActivityCode(),
                processDTO.getTriggerEvent(),
                processDTO.getOperator());
    }

    /**
     * 获取并验证工作流实例和当前节点
     *
     * @param instanceId 工作流实例ID
     * @param activityCode 活动节点编码
     * @return 工作流实例
     */
    private WorkflowInstance getAndValidateWorkflowInstance(Long instanceId, String activityCode) {
        WorkflowInstance instance = tableWorkflowInstanceService.selectById(instanceId);
        if (instance == null) {
            throw new BusinessException("工作流实例不存在");
        }

        // 验证当前节点
        String currentActivityCode = instance.getCurrentActivityCode();

        // 直接比较活动节点编码
        if (!currentActivityCode.equals(activityCode)) {
            throw new BusinessException("当前节点与请求节点不匹配");
        }

        return instance;
    }

    /**
     * 查找转换规则
     *
     * @param workflowId 工作流模板ID
     * @param fromActivityCode 起始节点编码
     * @param triggerEvent 触发事件
     * @return 活动节点转换规则
     */
    private ActivityTransition findTransitionRule(Long workflowId, String fromActivityCode, String triggerEvent) {
        ActivityTransition transition = tableActivityTransitionService.selectByWorkflowIdAndFromActivityCodeAndTriggerEvent(
                workflowId, fromActivityCode, triggerEvent);

        if (transition == null) {
            throw new BusinessException("未找到匹配的转换规则");
        }

        return transition;
    }

    /**
     * 获取当前活动实例记录
     *
     * @param instanceId 工作流实例ID
     * @param currentActivityCode 当前活动节点编码
     * @return 活动实例记录
     */
    private ActivityInstance getCurrentActivityInstance(Long instanceId, String currentActivityCode) {
        // 直接使用activityCode查询活动实例记录
        ActivityInstance currentActivityInstance = tableActivityInstanceService.selectByInstanceIdAndToActivityCode(
                instanceId, currentActivityCode);

        if (currentActivityInstance == null) {
            throw new BusinessException("未找到当前活动实例记录");
        }

        return currentActivityInstance;
    }

    /**
     * 结束当前活动实例
     *
     * @param currentActivityInstance 当前活动实例记录
     * @param endTime 结束时间
     */
    private void completeCurrentActivityInstance(ActivityInstance currentActivityInstance, Date endTime) {
        long duration = (endTime.getTime() - currentActivityInstance.getStartTime().getTime()) / 1000;
        tableActivityInstanceService.updateEndTimeAndDuration(
                currentActivityInstance.getId(), endTime, (int) duration);
    }

    /**
     * 创建新的活动实例记录或复用已存在的实例
     *
     * <p>支持工作流回退功能的活动实例管理逻辑：</p>
     * <ul>
     *   <li>如果目标活动节点已存在实例记录，则复用该实例（支持回退场景）</li>
     *   <li>如果目标活动节点不存在实例记录，则创建新的实例记录（正常前进场景）</li>
     * </ul>
     *
     * @param instanceId 工作流实例ID
     * @param fromActivityCode 起始节点编码
     * @param transition 活动节点转换规则
     * @param processDTO 节点处理参数
     * @param startTime 开始时间
     */
    private void createNextActivityInstance(Long instanceId, String fromActivityCode,
            ActivityTransition transition, WorkflowProcessDTO processDTO, Date startTime) {

        String toActivityCode = transition.getToActivityCode();

        log.debug("开始处理活动实例，工作流实例ID: {}, 从节点: {}, 到节点: {}",
                instanceId, fromActivityCode, toActivityCode);

        // 1. 检查目标活动节点是否已存在实例记录
        ActivityInstance existingInstance = tableActivityInstanceService
                .selectByInstanceIdAndToActivityCode(instanceId, toActivityCode);

        if (existingInstance != null) {
            // 2. 复用已存在的活动实例
            reuseExistingActivityInstance(existingInstance, fromActivityCode, transition, processDTO, startTime);
        } else {
            // 3. 创建新的活动实例记录
            createNewActivityInstance(instanceId, fromActivityCode, transition, processDTO, startTime);
        }
    }

    /**
     * 复用已存在的活动实例记录（支持工作流回退功能）
     *
     * @param existingInstance 已存在的活动实例
     * @param fromActivityCode 起始节点编码
     * @param transition 活动节点转换规则
     * @param processDTO 节点处理参数
     * @param startTime 开始时间
     */
    private void reuseExistingActivityInstance(ActivityInstance existingInstance, String fromActivityCode,
            ActivityTransition transition, WorkflowProcessDTO processDTO, Date startTime) {

        Long instanceId = existingInstance.getId();
        String toActivityCode = transition.getToActivityCode();

        log.info("复用已存在的活动实例，实例ID: {}, 活动实例ID: {}, 活动节点: {}",
                existingInstance.getInstanceId(), instanceId, toActivityCode);

        // 记录复用前的状态信息
        logInstanceStateBeforeReuse(existingInstance);

        // 更新活动实例字段
        updateExistingInstanceFields(existingInstance, fromActivityCode, transition, processDTO, startTime);

        // 动态设置状态码
        String statusCode = getTargetStatusCode(transition.getId(), existingInstance.getCurrentStatusCode(), toActivityCode);
        existingInstance.setCurrentStatusCode(statusCode);

        log.info("复用活动实例状态码更新，活动实例ID: {}, 新状态码: {}", instanceId, statusCode);

        // 更新数据库记录
        int updateResult = tableActivityInstanceService.updateSelectiveById(existingInstance, processDTO.getOperator());

        if (updateResult > 0) {
            log.info("活动实例复用成功，活动实例ID: {}, 活动节点: {}, 从节点: {}, 状态码: {}",
                    instanceId, toActivityCode, fromActivityCode, statusCode);
        } else {
            log.error("活动实例复用失败，活动实例ID: {}, 更新影响行数: {}", instanceId, updateResult);
            throw new RuntimeException("活动实例复用失败");
        }
    }

    /**
     * 记录复用前的实例状态信息
     *
     * @param existingInstance 已存在的活动实例
     */
    private void logInstanceStateBeforeReuse(ActivityInstance existingInstance) {
        log.debug("复用前活动实例状态 - ID: {}, 从节点: {}, 到节点: {}, 转换规则ID: {}, 开始时间: {}, " +
                "结束时间: {}, 耗时: {}, 当前状态: {}, 操作人: {}",
                existingInstance.getId(),
                existingInstance.getFromActivityCode(),
                existingInstance.getToActivityCode(),
                existingInstance.getTransitionId(),
                existingInstance.getStartTime(),
                existingInstance.getEndTime(),
                existingInstance.getDuration(),
                existingInstance.getCurrentStatusCode(),
                existingInstance.getOperator());
    }

    /**
     * 更新已存在实例的字段信息
     *
     * @param existingInstance 已存在的活动实例
     * @param fromActivityCode 起始节点编码
     * @param transition 活动节点转换规则
     * @param processDTO 节点处理参数
     * @param startTime 开始时间
     */
    private void updateExistingInstanceFields(ActivityInstance existingInstance, String fromActivityCode,
            ActivityTransition transition, WorkflowProcessDTO processDTO, Date startTime) {

        // 更新来源活动节点
        existingInstance.setFromActivityCode(fromActivityCode);

        // 更新转换规则ID
        existingInstance.setTransitionId(transition.getId());

        // 更新开始时间（重新开始执行）
        existingInstance.setStartTime(startTime);

        // 重置结束时间和耗时（因为重新开始执行）
        existingInstance.setEndTime(null);
        existingInstance.setDuration(null);

        // 更新操作人和备注信息
        existingInstance.setOperator(processDTO.getOperator());
        existingInstance.setRemarks(processDTO.getRemarks());

        log.debug("已更新活动实例字段，活动实例ID: {}, 新的从节点: {}, 转换规则ID: {}, 操作人: {}",
                existingInstance.getId(), fromActivityCode, transition.getId(), processDTO.getOperator());
    }

    /**
     * 创建新的活动实例记录（正常前进流程）
     *
     * @param instanceId 工作流实例ID
     * @param fromActivityCode 起始节点编码
     * @param transition 活动节点转换规则
     * @param processDTO 节点处理参数
     * @param startTime 开始时间
     */
    private void createNewActivityInstance(Long instanceId, String fromActivityCode,
            ActivityTransition transition, WorkflowProcessDTO processDTO, Date startTime) {

        String toActivityCode = transition.getToActivityCode();

        log.info("创建新的活动实例记录，工作流实例ID: {}, 从节点: {}, 到节点: {}",
                instanceId, fromActivityCode, toActivityCode);

        ActivityInstance newActivityInstance = new ActivityInstance();
        newActivityInstance.setInstanceId(instanceId);
        newActivityInstance.setToActivityCode(toActivityCode);
        newActivityInstance.setFromActivityCode(fromActivityCode);
        newActivityInstance.setTransitionId(transition.getId());
        newActivityInstance.setStartTime(startTime);

        // 动态设置状态码
        String statusCode = getTargetStatusCode(transition.getId(), null, toActivityCode);
        newActivityInstance.setCurrentStatusCode(statusCode);

        log.info("新活动实例状态码设置，活动节点: {}, 状态码: {}", toActivityCode, statusCode);

        newActivityInstance.setOperator(processDTO.getOperator());
        newActivityInstance.setRemarks(processDTO.getRemarks());

        // 插入新记录
        tableActivityInstanceService.insert(newActivityInstance, processDTO.getOperator());

        log.info("新活动实例创建成功，工作流实例ID: {}, 活动实例ID: {}, 活动节点: {}, 状态码: {}",
                instanceId, newActivityInstance.getId(), toActivityCode, statusCode);
    }

    /**
     * 更新工作流实例状态
     *
     * @param instance 工作流实例
     * @param transition 活动节点转换规则
     * @param operator 操作人
     */
    private void updateWorkflowInstanceStatus(WorkflowInstance instance, ActivityTransition transition, String operator) {
        // 目标节点的activityCode已经是String类型，直接使用
        String toActivityCode = transition.getToActivityCode();
        instance.setCurrentActivityCode(toActivityCode);

        // 动态设置工作流实例状态码
        // 根据转换规则ID和当前状态码获取目标状态码
        String statusCode = getTargetStatusCode(transition.getId(), instance.getStatusCode(), toActivityCode);
        instance.setStatusCode(statusCode);
        log.debug("工作流实例状态码更新为: {}", statusCode);

        // 更新工作流实例
        tableWorkflowInstanceService.updateSelectiveById(instance, operator);

        // 处理一个activity_transition对应多条activity_status_transition的情况
        updateRelatedActivityInstances(instance.getId(), transition, toActivityCode, operator);
    }

    /**
     * 更新与工作流实例相关的所有活动实例记录
     *
     * @param instanceId 工作流实例ID
     * @param transition 活动节点转换规则
     * @param toActivityCode 目标活动节点编码
     * @param operator 操作人
     */
    private void updateRelatedActivityInstances(Long instanceId, ActivityTransition transition, String toActivityCode, String operator) {
        log.debug("开始更新与工作流实例相关的活动实例记录，实例ID: {}, 目标活动节点: {}, 转换规则ID: {}",
                instanceId, toActivityCode, transition.getId());

        try {
            // 1. 获取与转换规则关联的所有状态转换规则
            List<ActivityStatusTransition> statusTransitions = getStatusTransitions(transition.getId());
            if (statusTransitions.isEmpty()) {
                return;
            }

            // 2. 查询所有活动实例记录
            List<ActivityInstance> activityInstances = getActivityInstances(instanceId);
            if (activityInstances.isEmpty()) {
                return;
            }

            // 3. 按活动编码分组活动实例，便于快速查找
            Map<String, List<ActivityInstance>> activityInstanceMap = groupActivityInstancesByCode(activityInstances);

            // 4. 遍历状态转换规则，更新对应的活动实例
            int updatedCount = processStatusTransitions(statusTransitions, activityInstanceMap);

            log.info("更新与工作流实例相关的活动实例记录完成，实例ID: {}, 成功更新: {} 条", instanceId, updatedCount);
        } catch (Exception e) {
            log.error("更新与工作流实例相关的活动实例记录失败，实例ID: {}, 异常: {}", instanceId, e.getMessage(), e);
            throw new RuntimeException("更新活动实例记录失败", e);
        }
    }

    /**
     * 获取状态转换规则列表
     *
     * @param transitionId 转换规则ID
     * @return 状态转换规则列表
     */
    private List<ActivityStatusTransition> getStatusTransitions(Long transitionId) {
        List<ActivityStatusTransition> statusTransitions =
                tableActivityStatusTransitionService.selectByActivityTransitionId(transitionId);

        if (statusTransitions == null || statusTransitions.isEmpty()) {
            log.warn("未找到与转换规则关联的状态转换规则，转换规则ID: {}", transitionId);
            return Collections.emptyList();
        }

        log.debug("找到 {} 条状态转换规则，转换规则ID: {}", statusTransitions.size(), transitionId);
        return statusTransitions;
    }

    /**
     * 获取活动实例列表
     *
     * @param instanceId 工作流实例ID
     * @return 活动实例列表
     */
    private List<ActivityInstance> getActivityInstances(Long instanceId) {
        List<ActivityInstance> activityInstances = tableActivityInstanceService.selectByInstanceId(instanceId);

        if (activityInstances.isEmpty()) {
            log.warn("未找到与工作流实例相关的活动实例记录，实例ID: {}", instanceId);
            return Collections.emptyList();
        }

        log.debug("找到 {} 条活动实例记录，实例ID: {}", activityInstances.size(), instanceId);
        return activityInstances;
    }

    /**
     * 按活动编码分组活动实例
     *
     * @param activityInstances 活动实例列表
     * @return 按活动编码分组的活动实例Map
     */
    private Map<String, List<ActivityInstance>> groupActivityInstancesByCode(List<ActivityInstance> activityInstances) {
        Map<String, List<ActivityInstance>> activityInstanceMap = activityInstances.stream()
                .filter(instance -> instance.getToActivityCode() != null && !instance.getToActivityCode().isEmpty())
                .collect(Collectors.groupingBy(ActivityInstance::getToActivityCode));

        log.debug("活动实例分组结果: {}", activityInstanceMap.keySet());
        return activityInstanceMap;
    }

    /**
     * 处理状态转换规则，更新对应的活动实例
     *
     * @param statusTransitions 状态转换规则列表
     * @param activityInstanceMap 按活动编码分组的活动实例Map
     * @return 成功更新的记录数
     */
    private int processStatusTransitions(List<ActivityStatusTransition> statusTransitions,
                                       Map<String, List<ActivityInstance>> activityInstanceMap) {
        int updatedCount = 0;

        for (ActivityStatusTransition statusTransition : statusTransitions) {
            String activityCode = statusTransition.getActivityCode();

            // 跳过无效的活动编码
            if (activityCode == null || activityCode.isEmpty()) {
                log.debug("跳过无效的活动编码，状态转换规则ID: {}", statusTransition.getId());
                continue;
            }

            // 获取对应活动编码的活动实例列表
            List<ActivityInstance> matchedInstances = activityInstanceMap.getOrDefault(activityCode, Collections.emptyList());
            if (matchedInstances.isEmpty()) {
                log.debug("未找到活动编码 {} 对应的活动实例", activityCode);
                continue;
            }

            // 更新匹配的活动实例
            updatedCount += updateMatchedActivityInstances(statusTransition, matchedInstances);
        }

        return updatedCount;
    }

    /**
     * 更新匹配的活动实例列表
     *
     * @param statusTransition 状态转换规则
     * @param activityInstances 匹配的活动实例列表
     * @return 成功更新的记录数
     */
    private int updateMatchedActivityInstances(ActivityStatusTransition statusTransition,
                                             List<ActivityInstance> activityInstances) {
        int updatedCount = 0;
        String activityCode = statusTransition.getActivityCode();
        String fromStatusCode = statusTransition.getFromStatusCode();
        String toStatusCode = statusTransition.getToStatusCode();

        log.debug("处理活动编码 {} 的状态转换，从状态: {}, 到状态: {}, 匹配实例数: {}",
                activityCode, fromStatusCode, toStatusCode, activityInstances.size());

        for (ActivityInstance activityInstance : activityInstances) {
            if (shouldUpdateActivityInstance(activityInstance, fromStatusCode)) {
                if (updateActivityInstanceStatus(activityInstance, toStatusCode)) {
                    updatedCount++;
                }
            }
        }

        return updatedCount;
    }

    /**
     * 判断是否应该更新活动实例
     *
     * @param activityInstance 活动实例
     * @param fromStatusCode 源状态编码
     * @return 是否应该更新
     */
    private boolean shouldUpdateActivityInstance(ActivityInstance activityInstance, String fromStatusCode) {
        String currentStatusCode = activityInstance.getCurrentStatusCode();

        // 如果from_status_code为空，表示初始状态设置，直接更新
        if (fromStatusCode == null || fromStatusCode.isEmpty()) {
            log.debug("初始状态设置，活动实例ID: {}, 当前状态: {}", activityInstance.getId(), currentStatusCode);
            return true;
        }

        // 如果当前状态与from_status_code匹配，则更新
        if (fromStatusCode.equals(currentStatusCode)) {
            log.debug("状态匹配，活动实例ID: {}, 当前状态: {}, 匹配状态: {}",
                    activityInstance.getId(), currentStatusCode, fromStatusCode);
            return true;
        }

        log.debug("状态不匹配，跳过更新，活动实例ID: {}, 当前状态: {}, 期望状态: {}",
                activityInstance.getId(), currentStatusCode, fromStatusCode);
        return false;
    }

    /**
     * 更新活动实例状态
     *
     * @param activityInstance 活动实例
     * @param newStatusCode 新状态编码
     * @return 是否更新成功
     */
    private boolean updateActivityInstanceStatus(ActivityInstance activityInstance, String newStatusCode) {
        Long instanceId = activityInstance.getId();
        String currentStatusCode = activityInstance.getCurrentStatusCode();

        // 如果状态相同，无需更新
        if (newStatusCode.equals(currentStatusCode)) {
            log.debug("活动实例状态无需更新，ID: {}, 状态: {}", instanceId, currentStatusCode);
            return false;
        }

        log.info("准备更新活动实例记录状态，活动实例ID: {}, 从 {} 更新为 {}",
                instanceId, currentStatusCode, newStatusCode);

        // 记录更新前状态用于验证
        if (!validateBeforeUpdate(instanceId)) {
            return false;
        }

        // 执行更新操作
        int updateResult = tableActivityInstanceService.updateStatusCode(instanceId, newStatusCode);
        log.info("活动实例状态更新结果，ID: {}, 影响行数: {}", instanceId, updateResult);

        if (updateResult <= 0) {
            log.error("活动实例状态更新失败，ID: {}, 影响行数: {}", instanceId, updateResult);
            return false;
        }

        // 验证更新结果
        return validateAfterUpdate(instanceId, newStatusCode);
    }

    /**
     * 更新前验证
     *
     * @param instanceId 活动实例ID
     * @return 验证是否通过
     */
    private boolean validateBeforeUpdate(Long instanceId) {
        ActivityInstance beforeUpdate = tableActivityInstanceService.selectById(instanceId);
        if (beforeUpdate == null) {
            log.error("更新前查询活动实例失败，ID: {}", instanceId);
            return false;
        }
        log.debug("更新前活动实例状态，ID: {}, 状态: {}", beforeUpdate.getId(), beforeUpdate.getCurrentStatusCode());
        return true;
    }

    /**
     * 更新后验证
     *
     * @param instanceId 活动实例ID
     * @param expectedStatusCode 期望的状态编码
     * @return 验证是否通过
     */
    private boolean validateAfterUpdate(Long instanceId, String expectedStatusCode) {
        ActivityInstance afterUpdate = tableActivityInstanceService.selectById(instanceId);
        if (afterUpdate == null) {
            log.error("更新后查询活动实例失败，ID: {}", instanceId);
            return false;
        }

        String actualStatusCode = afterUpdate.getCurrentStatusCode();
        boolean isSuccess = expectedStatusCode.equals(actualStatusCode);

        log.info("更新后活动实例状态验证，ID: {}, 期望状态: {}, 实际状态: {}, 更新成功: {}",
                afterUpdate.getId(), expectedStatusCode, actualStatusCode, isSuccess);

        if (!isSuccess) {
            log.error("活动实例状态更新验证失败，ID: {}, 期望: {}, 实际: {}",
                    afterUpdate.getId(), expectedStatusCode, actualStatusCode);
        }

        return isSuccess;
    }



    /**
     * 获取工作流实例详情
     *
     * @param instanceId 工作流实例ID
     * @return 工作流实例详情视图对象
     */
    @Override
    public WorkflowInstanceDetailVO getInstanceDetail(Long instanceId) {
        log.info("获取工作流实例详情，ID: {}", instanceId);

        if (instanceId == null) {
            throw new BusinessException("工作流实例ID不能为空");
        }

        try {
            // 1. 获取工作流实例和相关信息
            WorkflowInstance instance = getWorkflowInstanceById(instanceId);
            WorkflowTemplate template = getWorkflowTemplateById(instance.getWorkflowId());

            // 根据activityCode查询ActivityDefinition
            ActivityDefinition currentActivity = tableActivityDefinitionService.selectByActivityCode(instance.getCurrentActivityCode());

            // 2. 创建详情VO对象
            WorkflowInstanceDetailVO detailVO = createInstanceDetailVO(instance, template, currentActivity);

            // 3. 查询并处理活动实例记录
            List<ActivityInstanceVO> activityInstanceVOs = getActivityInstanceVOs(instanceId);
            detailVO.setActivityInstances(activityInstanceVOs);

            log.info("获取工作流实例详情成功");
            return detailVO;
        } catch (BusinessException e) {
            log.error("获取工作流实例详情失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("获取工作流实例详情失败", e);
            throw new BusinessException("获取工作流实例详情失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取工作流实例
     *
     * @param instanceId 工作流实例ID
     * @return 工作流实例
     */
    private WorkflowInstance getWorkflowInstanceById(Long instanceId) {
        WorkflowInstance instance = tableWorkflowInstanceService.selectById(instanceId);
        if (instance == null) {
            throw new BusinessException("工作流实例不存在");
        }
        return instance;
    }

    /**
     * 根据ID获取工作流模板
     *
     * @param templateId 工作流模板ID
     * @return 工作流模板
     */
    private WorkflowTemplate getWorkflowTemplateById(Long templateId) {
        WorkflowTemplate template = tableWorkflowTemplateService.selectById(templateId);
        if (template == null) {
            throw new BusinessException("工作流模板不存在");
        }
        return template;
    }

    /**
     * 创建工作流实例详情视图对象
     *
     * @param instance 工作流实例
     * @param template 工作流模板
     * @param currentActivity 当前活动节点
     * @return 工作流实例详情视图对象
     */
    private WorkflowInstanceDetailVO createInstanceDetailVO(WorkflowInstance instance,
            WorkflowTemplate template, ActivityDefinition currentActivity) {
        WorkflowInstanceDetailVO detailVO = new WorkflowInstanceDetailVO();
        detailVO.setId(instance.getId());
        detailVO.setWorkflowId(instance.getWorkflowId());
        detailVO.setWorkflowName(template.getWorkflowName());
        detailVO.setBusinessId(instance.getBusinessId());

        // 如果currentActivity不为null，使用其ID
        if (currentActivity != null) {
            detailVO.setCurrentActivityId(currentActivity.getId());
            detailVO.setCurrentActivityName(currentActivity.getActivityName());
        } else {
            // 否则尝试根据activityCode查找对应的ActivityDefinition
            ActivityDefinition activityDefinition = tableActivityDefinitionService.selectByActivityCode(instance.getCurrentActivityCode());
            if (activityDefinition != null) {
                detailVO.setCurrentActivityId(activityDefinition.getId());
                detailVO.setCurrentActivityName(activityDefinition.getActivityName());
            } else {
                detailVO.setCurrentActivityName("未知节点");
            }
        }

        detailVO.setStatus(instance.getStatusCode());
        detailVO.setCreateTime(instance.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        detailVO.setCreateBy(instance.getCreateBy());
        detailVO.setUpdateTime(instance.getUpdateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        detailVO.setUpdateBy(instance.getUpdateBy());

        return detailVO;
    }

    /**
     * 获取活动实例视图对象列表
     *
     * @param instanceId 工作流实例ID
     * @return 活动实例视图对象列表
     */
    private List<ActivityInstanceVO> getActivityInstanceVOs(Long instanceId) {
        // 1. 获取所有活动实例记录
        List<ActivityInstance> activityInstances = tableActivityInstanceService.selectByInstanceId(instanceId);
        if (activityInstances.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 收集所有需要查询的活动节点编码
        Set<String> activityCodes = new HashSet<>();
        for (ActivityInstance instance : activityInstances) {
            if (instance.getToActivityCode() != null) {
                activityCodes.add(instance.getToActivityCode());
            }
            if (instance.getFromActivityCode() != null) {
                activityCodes.add(instance.getFromActivityCode());
            }
        }

        // 3. 一次性查询所有需要的ActivityDefinition
        Map<String, ActivityDefinition> activityDefinitionMap = new HashMap<>();
        for (String activityCode : activityCodes) {
            ActivityDefinition definition = tableActivityDefinitionService.selectByActivityCode(activityCode);
            if (definition != null) {
                activityDefinitionMap.put(activityCode, definition);
            }
        }

        // 4. 转换为VO对象
        List<ActivityInstanceVO> activityInstanceVOs = new ArrayList<>();
        for (ActivityInstance activityInstance : activityInstances) {
            ActivityInstanceVO vo = new ActivityInstanceVO();
            vo.setId(activityInstance.getId());

            // 设置目标活动节点信息
            ActivityDefinition toActivity = activityDefinitionMap.get(activityInstance.getToActivityCode());
            if (toActivity != null) {
                vo.setToActivityId(toActivity.getId());
                vo.setToActivityName(toActivity.getActivityName());
            } else {
                vo.setToActivityName("未知节点");
            }

            // 设置来源活动节点信息
            if (activityInstance.getFromActivityCode() != null) {
                ActivityDefinition fromActivity = activityDefinitionMap.get(activityInstance.getFromActivityCode());
                if (fromActivity != null) {
                    vo.setFromActivityId(fromActivity.getId());
                    vo.setFromActivityName(fromActivity.getActivityName());
                } else {
                    vo.setFromActivityName("未知节点");
                }
            }

            vo.setTransitionId(activityInstance.getTransitionId());

            // 设置时间信息
            if (activityInstance.getStartTime() != null) {
                vo.setStartTime(activityInstance.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
            }
            if (activityInstance.getEndTime() != null) {
                vo.setEndTime(activityInstance.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
            }

            vo.setDuration(activityInstance.getDuration());
            vo.setCurrentStatusId(null); // 状态ID暂不设置
            vo.setCurrentStatusName(activityInstance.getCurrentStatusCode());
            vo.setOperator(activityInstance.getOperator());
            vo.setRemarks(activityInstance.getRemarks());

            activityInstanceVOs.add(vo);
        }

        // 5. 按开始时间排序
        activityInstanceVOs.sort(Comparator.comparing(ActivityInstanceVO::getStartTime,
                Comparator.nullsLast(Comparator.naturalOrder())));

        return activityInstanceVOs;
    }



    /**
     * 查询工作流实例列表
     *
     * @param queryDTO 查询条件
     * @return 工作流实例视图对象列表
     */
    @Override
    public List<WorkflowInstanceVO> listInstances(WorkflowInstanceQueryDTO queryDTO) {
        log.info("查询工作流实例列表，查询条件: {}", queryDTO);

        if (queryDTO == null) {
            throw new BusinessException("查询条件不能为空");
        }

        try {
            // 1. 使用优化后的findByCondition方法查询工作流实例
            List<WorkflowInstance> instances = tableWorkflowInstanceService.findByCondition(queryDTO);

            if (instances.isEmpty()) {
                log.info("未查询到符合条件的工作流实例");
                return new ArrayList<>();
            }

            // 2. 转换为VO对象
            List<WorkflowInstanceVO> instanceVOs = convertToWorkflowInstanceVOs(instances);

            log.info("查询到{}个工作流实例", instanceVOs.size());
            return instanceVOs;
        } catch (BusinessException e) {
            log.error("查询工作流实例列表失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("查询工作流实例列表异常", e);
            throw new BusinessException("查询工作流实例列表失败: " + e.getMessage());
        }
    }

    /**
     * 将工作流实例列表转换为视图对象列表
     *
     * @param instances 工作流实例列表
     * @return 工作流实例视图对象列表
     */
    private List<WorkflowInstanceVO> convertToWorkflowInstanceVOs(List<WorkflowInstance> instances) {
        List<WorkflowInstanceVO> instanceVOs = new ArrayList<>();

        // 预先获取所有相关的工作流模板和活动节点，减少数据库查询次数
        Map<Long, WorkflowTemplate> templateCache = new HashMap<>();
        Map<String, ActivityDefinition> activityCodeCache = new HashMap<>();

        // 收集所有需要查询的ID和编码
        Set<Long> templateIds = new HashSet<>();
        Set<String> activityCodes = new HashSet<>();

        for (WorkflowInstance instance : instances) {
            templateIds.add(instance.getWorkflowId());
            if (instance.getCurrentActivityCode() != null) {
                activityCodes.add(instance.getCurrentActivityCode());
            }
        }

        // 批量查询工作流模板
        for (Long templateId : templateIds) {
            WorkflowTemplate template = tableWorkflowTemplateService.selectById(templateId);
            if (template != null) {
                templateCache.put(templateId, template);
            }
        }

        // 批量查询活动节点
        for (String activityCode : activityCodes) {
            ActivityDefinition activity = tableActivityDefinitionService.selectByActivityCode(activityCode);
            if (activity != null) {
                activityCodeCache.put(activityCode, activity);
            }
        }

        // 转换为VO对象
        for (WorkflowInstance instance : instances) {
            WorkflowInstanceVO vo = new WorkflowInstanceVO();
            vo.setId(instance.getId());
            vo.setWorkflowId(instance.getWorkflowId());

            // 从缓存中获取工作流模板名称
            WorkflowTemplate template = templateCache.get(instance.getWorkflowId());
            if (template != null) {
                vo.setWorkflowName(template.getWorkflowName());
            }

            vo.setBusinessId(instance.getBusinessId());

            // 从缓存中获取当前活动节点信息
            if (instance.getCurrentActivityCode() != null) {
                ActivityDefinition currentActivity = activityCodeCache.get(instance.getCurrentActivityCode());
                if (currentActivity != null) {
                    vo.setCurrentActivityId(currentActivity.getId());
                    vo.setCurrentActivityName(currentActivity.getActivityName());
                }
            }

            vo.setStatus(instance.getStatusCode());
            vo.setCreateTime(instance.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
            vo.setCreateBy(instance.getCreateBy());
            vo.setUpdateTime(instance.getUpdateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
            vo.setUpdateBy(instance.getUpdateBy());

            instanceVOs.add(vo);
        }

        return instanceVOs;
    }

    /**
     * 分页查询工作流实例列表
     *
     * @param queryDTO 查询条件
     * @return 分页工作流实例视图对象
     */
    @Override
    public BasePageVO<WorkflowInstanceVO> getInstanceList(WorkflowInstanceQueryDTO queryDTO) {
        log.info("分页查询工作流实例列表，查询条件: {}", queryDTO);

        if (queryDTO == null) {
            throw new BusinessException("查询条件不能为空");
        }

        try {
            // 设置分页参数
            PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

            // 查询数据
            List<WorkflowInstanceVO> list = listInstances(queryDTO);

            // 创建PageInfo对象
            PageInfo<WorkflowInstanceVO> pageInfo = new PageInfo<>(list);

            // 转换为BasePageVO
            BasePageVO<WorkflowInstanceVO> result = BasePageVO.of(list, pageInfo);

            log.info("分页查询工作流实例列表成功，总记录数: {}, 总页数: {}",
                    result.getTotal(), result.getPages());

            return result;
        } catch (BusinessException e) {
            log.error("分页查询工作流实例列表失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("分页查询工作流实例列表异常", e);
            throw new BusinessException("分页查询工作流实例列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取工作流实例进度
     *
     * @param instanceId 工作流实例ID
     * @return 工作流进度视图对象
     */
    @Override
    public WorkflowProgressVO getInstanceProgress(Long instanceId) {
        log.info("获取工作流实例进度，ID: {}", instanceId);

        if (instanceId == null) {
            throw new BusinessException("工作流实例ID不能为空");
        }

        try {
            // 1. 获取工作流实例和相关信息
            WorkflowInstance instance = getWorkflowInstanceById(instanceId);
            WorkflowTemplate template = getWorkflowTemplateById(instance.getWorkflowId());

            // 根据activityCode查询ActivityDefinition
            ActivityDefinition currentActivity = tableActivityDefinitionService.selectByActivityCode(instance.getCurrentActivityCode());

            // 2. 获取所有活动节点并排序
            List<ActivityDefinition> allActivities = getAllActivityDefinitions(instance.getWorkflowId());

            // 3. 查询活动实例记录
            List<ActivityInstance> activityInstances = tableActivityInstanceService.selectByInstanceId(instanceId);

            // 4. 创建进度VO对象
            WorkflowProgressVO progressVO = createProgressVO(instanceId, template, currentActivity, instance);

            // 5. 构建进度节点列表
            List<ProgressNodeVO> progressNodes = buildProgressNodes(allActivities, instance, currentActivity, activityInstances);
            progressVO.setProgressNodes(progressNodes);

            log.info("获取工作流实例进度成功");
            return progressVO;
        } catch (BusinessException e) {
            log.error("获取工作流实例进度失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("获取工作流实例进度失败", e);
            throw new BusinessException("获取工作流实例进度失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有活动节点定义并排序
     *
     * @param workflowId 工作流模板ID
     * @return 排序后的活动节点定义列表
     */
    private List<ActivityDefinition> getAllActivityDefinitions(Long workflowId) {
        List<ActivityDefinition> allActivities = tableActivityDefinitionService.selectByWorkflowId(workflowId);
        if (allActivities.isEmpty()) {
            throw new BusinessException("工作流模板未配置活动节点");
        }

        // 按照顺序排序
        allActivities.sort(Comparator.comparing(ActivityDefinition::getSequence));
        return allActivities;
    }



    /**
     * 创建工作流进度视图对象
     *
     * @param instanceId 工作流实例ID
     * @param template 工作流模板
     * @param currentActivity 当前活动节点
     * @param instance 工作流实例
     * @return 工作流进度视图对象
     */
    private WorkflowProgressVO createProgressVO(Long instanceId, WorkflowTemplate template,
            ActivityDefinition currentActivity, WorkflowInstance instance) {
        WorkflowProgressVO progressVO = new WorkflowProgressVO();
        progressVO.setInstanceId(instanceId);
        progressVO.setWorkflowName(template.getWorkflowName());
        progressVO.setCurrentActivityName(currentActivity != null ? currentActivity.getActivityName() : "未知节点");
        progressVO.setStatus(instance.getStatusCode());
        return progressVO;
    }

    /**
     * 构建进度节点列表
     *
     * @param allActivities 所有活动节点
     * @param instance 工作流实例
     * @param currentActivity 当前活动节点
     * @param activityInstances 活动实例记录列表
     * @return 进度节点视图对象列表
     */
    private List<ProgressNodeVO> buildProgressNodes(List<ActivityDefinition> allActivities,
            WorkflowInstance instance, ActivityDefinition currentActivity,
            List<ActivityInstance> activityInstances) {
        List<ProgressNodeVO> progressNodes = new ArrayList<>();

        // 不需要额外的映射，直接使用activityCode

        // 使用Map缓存活动实例记录，以toActivityCode为键，提高查找效率
        Map<String, ActivityInstance> activityInstanceMap = activityInstances.stream()
                .collect(Collectors.toMap(
                    ActivityInstance::getToActivityCode,
                    ai -> ai,
                    (existing, replacement) -> existing  // 如果有重复键，保留第一个
                ));

        for (ActivityDefinition activity : allActivities) {
            ProgressNodeVO nodeVO = createProgressNodeVO(activity);

            // 设置节点状态
            String activityCode = activity.getActivityCode();
            String statusCode;

            if (activityCode.equals(instance.getCurrentActivityCode())) {
                // 当前节点 - 从状态转换规则中获取当前节点状态码
                ActivityInstance currentActivityInstance = activityInstanceMap.get(activityCode);
                if (currentActivityInstance != null && currentActivityInstance.getCurrentStatusCode() != null) {
                    // 如果有活动实例记录且有状态码，使用该状态码
                    statusCode = currentActivityInstance.getCurrentStatusCode();
                    log.debug("当前节点[{}]使用活动实例状态码: {}", activityCode, statusCode);
                } else {
                    // 否则使用工作流实例的状态码
                    statusCode = instance.getStatusCode();
                    log.debug("当前节点[{}]使用工作流实例状态码: {}", activityCode, statusCode);
                }
            } else {
                // 非当前节点
                ActivityInstance activityInstance = activityInstanceMap.get(activityCode);

                if (activityInstance != null) {
                    // 节点有活动实例记录
                    if (activityInstance.getEndTime() != null) {
                        // 已完成节点 - 使用活动实例的状态码
                        statusCode = activityInstance.getCurrentStatusCode();
                        log.debug("已完成节点[{}]使用活动实例状态码: {}", activityCode, statusCode);
                    } else {
                        // 处理中节点 - 使用活动实例的状态码
                        statusCode = activityInstance.getCurrentStatusCode();
                        log.debug("处理中节点[{}]使用活动实例状态码: {}", activityCode, statusCode);
                    }
                } else {
                    // 节点没有活动实例记录
                    if (currentActivity != null && activity.getSequence() < currentActivity.getSequence()) {
                        // 已跳过节点 - 使用默认的跳过状态码
                        statusCode = ActivityStatusEnum.SKIPPED.getCode();
                        log.debug("已跳过节点[{}]使用默认状态码: {}", activityCode, statusCode);
                    } else {
                        // 待处理节点 - 使用默认的待处理状态码
                        statusCode = ActivityStatusEnum.PENDING.getCode();
                        log.debug("待处理节点[{}]使用默认状态码: {}", activityCode, statusCode);
                    }
                }
            }

            nodeVO.setStatus(statusCode);
            progressNodes.add(nodeVO);
        }

        return progressNodes;
    }

    /**
     * 创建进度节点视图对象
     *
     * @param activity 活动节点定义
     * @return 进度节点视图对象
     */
    private ProgressNodeVO createProgressNodeVO(ActivityDefinition activity) {
        ProgressNodeVO nodeVO = new ProgressNodeVO();
        nodeVO.setActivityId(activity.getId());
        nodeVO.setActivityName(activity.getActivityName());
        nodeVO.setSequence(activity.getSequence());
        return nodeVO;
    }



    /**
     * 更新工作流模板状态
     *
     * @param templateId 工作流模板ID
     * @param isActive 是否启用（1:启用, 0:禁用）
     */
    @Override
    @Transactional
    public void updateTemplateStatus(Long templateId, Integer isActive) {
        log.info("更新工作流模板状态，模板ID: {}, 状态: {}", templateId, isActive);

        // 参数验证
        validateUpdateTemplateStatusParams(templateId, isActive);

        try {
            // 1. 获取工作流模板
            WorkflowTemplate workflowTemplate = getWorkflowTemplateById(templateId);

            // 2. 更新模板状态
            updateTemplateActiveStatus(workflowTemplate, isActive);

            log.info("工作流模板[{}]状态已更新为: {}", templateId, isActive);
        } catch (BusinessException e) {
            log.error("更新工作流模板状态失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("更新工作流模板状态异常", e);
            throw new BusinessException("更新工作流模板状态失败: " + e.getMessage());
        }
    }

    /**
     * 验证更新工作流模板状态的参数
     *
     * @param templateId 工作流模板ID
     * @param isActive 是否启用
     */
    private void validateUpdateTemplateStatusParams(Long templateId, Integer isActive) {
        if (templateId == null) {
            throw new BusinessException("工作流模板ID不能为空");
        }
        if (isActive == null) {
            throw new BusinessException("状态值不能为空");
        }
        if (isActive != 0 && isActive != 1) {
            throw new BusinessException("状态值无效，只能为0或1");
        }
    }

    /**
     * 更新工作流模板的启用状态
     *
     * @param workflowTemplate 工作流模板
     * @param isActive 是否启用
     */
    private void updateTemplateActiveStatus(WorkflowTemplate workflowTemplate, Integer isActive) {
        workflowTemplate.setIsActive(isActive);
        String username = SessionUtils.getUsername();
        int rows = tableWorkflowTemplateService.updateSelectiveById(workflowTemplate, username);
        if (rows != 1) {
            throw new BusinessException("更新工作流模板状态失败");
        }
    }

    /**
     * 转换为活动节点定义VO列表
     *
     * @param activityDefinitions 活动节点定义列表
     * @return 活动节点定义VO列表
     */
    private List<ActivityDefinitionVO> convertToActivityDefinitionVOs(List<ActivityDefinition> activityDefinitions) {
        if (activityDefinitions == null || activityDefinitions.isEmpty()) {
            return new ArrayList<>();
        }

        // 使用Stream API进行转换，更简洁高效
        return activityDefinitions.stream()
                .map(this::convertToActivityDefinitionVO)
                .collect(Collectors.toList());
    }

    /**
     * 将活动节点定义转换为视图对象
     *
     * @param definition 活动节点定义
     * @return 活动节点定义视图对象
     */
    private ActivityDefinitionVO convertToActivityDefinitionVO(ActivityDefinition definition) {
        ActivityDefinitionVO vo = new ActivityDefinitionVO();
        vo.setId(definition.getId());
        vo.setActivityCode(definition.getActivityCode());
        vo.setActivityName(definition.getActivityName());
        return vo;
    }

    /**
     * 创建活动节点转换规则
     *
     * @param createDTO 创建活动节点转换规则请求对象
     * @return 创建成功的活动节点转换规则ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createActivityTransition(ActivityTransitionCreateDTO createDTO) {
        log.info("创建活动节点转换规则，参数: {}", createDTO);

        // 参数验证
        if (createDTO == null) {
            throw new IllegalArgumentException("创建参数不能为空");
        }
        if (createDTO.getActivityTransition() == null) {
            throw new IllegalArgumentException("活动节点转换规则不能为空");
        }
        // 允许状态转换规则列表为空

        try {
            // 1. 创建活动节点转换规则
            ActivityTransition transition = new ActivityTransition();
            BeanUtils.copyProperties(createDTO.getActivityTransition(), transition);

            // 2. 设置租户ID
            transition.setTenantId(SessionUtils.getTenantId().intValue());

            // 3. 保存活动节点转换规则
            String operator = SessionUtils.getUsername();
            tableActivityTransitionService.insert(transition, operator);

            Long transitionId = transition.getId();
            log.info("活动节点转换规则创建成功，ID: {}", transitionId);

            // 4. 创建节点状态转换规则（如果有）
            if (createDTO.getStatusTransitions() != null && !createDTO.getStatusTransitions().isEmpty()) {
                List<ActivityStatusTransition> statusTransitions = new ArrayList<>();
                for (ActivityStatusTransitionDTO statusTransitionDTO : createDTO.getStatusTransitions()) {
                    ActivityStatusTransition statusTransition = new ActivityStatusTransition();
                    BeanUtils.copyProperties(statusTransitionDTO, statusTransition);
                    statusTransition.setActivityTransitionId(transitionId);

                    // 如果没有设置活动编码，则使用转换规则中的fromActivityCode
                    if (statusTransition.getActivityCode() == null || statusTransition.getActivityCode().isEmpty()) {
                        statusTransition.setActivityCode(transition.getFromActivityCode());
                    }

                    statusTransitions.add(statusTransition);
                }

                // 5. 批量保存节点状态转换规则
                tableActivityStatusTransitionService.batchInsert(statusTransitions, operator);
                log.info("节点状态转换规则创建成功，数量: {}", statusTransitions.size());
            } else {
                log.info("未提供节点状态转换规则，跳过创建");
            }

            return transitionId;
        } catch (BusinessException e) {
            log.error("创建活动节点转换规则失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("创建活动节点转换规则失败", e);
            throw new BusinessException("创建活动节点转换规则失败: " + e.getMessage());
        }
    }

    /**
     * 更新活动节点转换规则
     *
     * @param updateDTO 更新活动节点转换规则请求对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateActivityTransition(ActivityTransitionUpdateDTO updateDTO) {
        log.info("更新活动节点转换规则，参数: {}", updateDTO);

        // 参数验证
        if (updateDTO == null) {
            throw new IllegalArgumentException("更新参数不能为空");
        }
        if (updateDTO.getActivityTransition() == null) {
            throw new IllegalArgumentException("活动节点转换规则不能为空");
        }
        if (updateDTO.getId() == null) {
            throw new IllegalArgumentException("活动节点转换规则ID不能为空");
        }
        // 允许状态转换规则列表为空

        try {
            // 1. 获取并验证活动节点转换规则
            ActivityTransition existingTransition = tableActivityTransitionService.selectById(updateDTO.getId());
            if (existingTransition == null) {
                throw new BusinessException("活动节点转换规则不存在，ID: " + updateDTO.getId());
            }

            // 2. 更新活动节点转换规则
            // 使用已获取的原始记录，保留基础字段值

            // 创建一个新的对象，保留原始记录中的基础字段值
            ActivityTransition transition = new ActivityTransition();

            // 先复制原始记录中的所有字段
            BeanUtils.copyProperties(existingTransition, transition);

            // 再复制DTO中的业务字段，覆盖原始值
            ActivityTransitionDTO transitionDTO = updateDTO.getActivityTransition();
            if (transitionDTO != null) {
                // 只更新业务字段，保留基础字段
                transition.setWorkflowId(transitionDTO.getWorkflowId());
                transition.setFromActivityCode(transitionDTO.getFromActivityCode());
                transition.setToActivityCode(transitionDTO.getToActivityCode());
                transition.setTriggerEvent(transitionDTO.getTriggerEvent());
                transition.setConditionHandler(transitionDTO.getConditionHandler());
                transition.setHandlerClass(transitionDTO.getHandlerClass());
                transition.setDescription(transitionDTO.getDescription());
            }

            // 更新修改时间和修改人
            String operator = SessionUtils.getUsername();

            // 使用updateByPrimaryKey方法，确保null值字段也会被更新
            tableActivityTransitionService.updateByPrimaryKey(transition, operator);

            log.info("活动节点转换规则更新成功，ID: {}，保留了基础字段值", transition.getId());

            // 3. 删除原有的节点状态转换规则
            tableActivityStatusTransitionService.deleteByActivityTransitionId(transition.getId());
            log.info("删除原有节点状态转换规则成功");

            // 4. 创建新的节点状态转换规则（如果有）
            if (updateDTO.getStatusTransitions() != null && !updateDTO.getStatusTransitions().isEmpty()) {
                List<ActivityStatusTransition> statusTransitions = new ArrayList<>();
                for (ActivityStatusTransitionDTO statusTransitionDTO : updateDTO.getStatusTransitions()) {
                    ActivityStatusTransition statusTransition = new ActivityStatusTransition();
                    BeanUtils.copyProperties(statusTransitionDTO, statusTransition);
                    statusTransition.setActivityTransitionId(transition.getId());
                    statusTransitions.add(statusTransition);
                }

                // 5. 批量保存节点状态转换规则
                tableActivityStatusTransitionService.batchInsert(statusTransitions, operator);
                log.info("新节点状态转换规则创建成功，数量: {}", statusTransitions.size());
            } else {
                log.info("未提供节点状态转换规则，跳过创建");
            }
        } catch (BusinessException e) {
            log.error("更新活动节点转换规则失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("更新活动节点转换规则失败", e);
            throw new BusinessException("更新活动节点转换规则失败: " + e.getMessage());
        }
    }

    /**
     * 删除活动节点转换规则
     *
     * @param deleteDTO 删除活动节点转换规则请求对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteActivityTransition(ActivityTransitionDeleteDTO deleteDTO) {
        log.info("删除活动节点转换规则，参数: {}", deleteDTO);

        // 参数验证
        if (deleteDTO == null || deleteDTO.getId() == null) {
            throw new IllegalArgumentException("活动节点转换规则ID不能为空");
        }

        try {
            // 1. 获取并验证活动节点转换规则
            ActivityTransition existingTransition = tableActivityTransitionService.selectById(deleteDTO.getId());
            if (existingTransition == null) {
                throw new BusinessException("活动节点转换规则不存在，ID: " + deleteDTO.getId());
            }

            // 2. 如果需要级联删除，先删除关联的节点状态转换规则
            if (deleteDTO.getCascadeDelete() != null && deleteDTO.getCascadeDelete()) {
                tableActivityStatusTransitionService.deleteByActivityTransitionId(deleteDTO.getId());
                log.info("级联删除节点状态转换规则成功");
            }

            // 3. 删除活动节点转换规则
            tableActivityTransitionService.deleteById(deleteDTO.getId());
            log.info("活动节点转换规则删除成功，ID: {}", deleteDTO.getId());
        } catch (BusinessException e) {
            log.error("删除活动节点转换规则失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("删除活动节点转换规则失败", e);
            throw new BusinessException("删除活动节点转换规则失败: " + e.getMessage());
        }
    }

    /**
     * 获取活动节点转换规则详情
     *
     * @param transitionId 转换规则ID
     * @return 活动节点转换规则详情视图对象
     */
    @Override
    public ActivityTransitionDetailVO getActivityTransitionDetail(Long transitionId) {
        log.info("获取活动节点转换规则详情，ID: {}", transitionId);

        // 参数验证
        if (transitionId == null) {
            throw new IllegalArgumentException("活动节点转换规则ID不能为空");
        }

        try {
            // 1. 获取活动节点转换规则
            ActivityTransition transition = tableActivityTransitionService.selectById(transitionId);
            if (transition == null) {
                throw new BusinessException("活动节点转换规则不存在，ID: " + transitionId);
            }

            // 2. 转换为VO对象
            ActivityTransitionVO transitionVO = new ActivityTransitionVO();
            BeanUtils.copyProperties(transition, transitionVO);

            // 3. 获取关联的节点状态转换规则
            List<ActivityStatusTransition> statusTransitions = tableActivityStatusTransitionService.selectByActivityTransitionId(transitionId);
            List<ActivityStatusTransitionVO> statusTransitionVOs = statusTransitions.stream()
                    .map(statusTransition -> {
                        ActivityStatusTransitionVO statusTransitionVO = new ActivityStatusTransitionVO();
                        BeanUtils.copyProperties(statusTransition, statusTransitionVO);
                        return statusTransitionVO;
                    })
                    .collect(Collectors.toList());

            // 4. 设置关联的状态转换规则
            transitionVO.setStatusTransitions(statusTransitionVOs);

            // 5. 创建详情VO对象
            ActivityTransitionDetailVO detailVO = new ActivityTransitionDetailVO();
            detailVO.setActivityTransition(transitionVO);

            log.info("获取活动节点转换规则详情成功，ID: {}", transitionId);
            return detailVO;
        } catch (BusinessException e) {
            log.error("获取活动节点转换规则详情失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("获取活动节点转换规则详情失败", e);
            throw new BusinessException("获取活动节点转换规则详情失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询活动节点转换规则列表
     *
     * @param queryDTO 查询条件对象
     * @return 活动节点转换规则分页列表
     */
    @Override
    public BasePageVO<ActivityTransitionVO> getActivityTransitionList(ActivityTransitionQueryDTO queryDTO) {
        log.info("分页查询活动节点转换规则列表，参数: {}", queryDTO);

        // 参数验证
        if (queryDTO == null) {
            throw new IllegalArgumentException("查询条件不能为空");
        }
        if (queryDTO.getPageNum() == null || queryDTO.getPageNum() < 1) {
            queryDTO.setPageNum(1);
        }
        if (queryDTO.getPageSize() == null || queryDTO.getPageSize() < 1) {
            queryDTO.setPageSize(10);
        }

        try {
            // 1. 设置分页参数
            PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

            // 2. 构建查询条件
            List<ActivityTransition> transitions = new ArrayList<>();

            if (queryDTO.getWorkflowId() != null) {
                // 根据工作流ID查询
                transitions = tableActivityTransitionService.selectByWorkflowId(queryDTO.getWorkflowId());

                // 如果有其他条件，进行过滤
                if (StringUtils.hasText(queryDTO.getFromActivityCode())) {
                    transitions = transitions.stream()
                            .filter(t -> queryDTO.getFromActivityCode().equals(t.getFromActivityCode()))
                            .collect(Collectors.toList());
                }

                if (StringUtils.hasText(queryDTO.getToActivityCode())) {
                    transitions = transitions.stream()
                            .filter(t -> queryDTO.getToActivityCode().equals(t.getToActivityCode()))
                            .collect(Collectors.toList());
                }

                if (StringUtils.hasText(queryDTO.getTriggerEvent())) {
                    transitions = transitions.stream()
                            .filter(t -> queryDTO.getTriggerEvent().equals(t.getTriggerEvent()))
                            .collect(Collectors.toList());
                }
            } else {
                // 没有工作流ID，无法查询
                log.warn("未提供工作流ID，无法查询活动节点转换规则");
            }

            if (transitions.isEmpty()) {
                // 如果没有查询到数据，直接返回空结果
                PageInfo<ActivityTransition> emptyPageInfo = new PageInfo<>(transitions);
                return BasePageVO.of(Collections.emptyList(), emptyPageInfo);
            }

            // 3. 提取所有活动节点转换规则ID
            List<Long> transitionIds = transitions.stream()
                    .map(ActivityTransition::getId)
                    .collect(Collectors.toList());

            // 4. 一次性查询所有关联的状态转换规则
            List<ActivityStatusTransition> statusTransitions =
                    tableActivityStatusTransitionService.selectByActivityTransitionIds(transitionIds);

            // 5. 按活动节点转换规则ID分组状态转换规则
            Map<Long, List<ActivityStatusTransition>> statusTransitionMap = statusTransitions.stream()
                    .collect(Collectors.groupingBy(ActivityStatusTransition::getActivityTransitionId));

            // 6. 转换为VO对象并关联状态转换规则
            List<ActivityTransitionVO> transitionVOs = transitions.stream().map(transition -> {
                // 转换活动节点转换规则
                ActivityTransitionVO vo = new ActivityTransitionVO();
                BeanUtils.copyProperties(transition, vo);

                // 获取并转换关联的状态转换规则
                List<ActivityStatusTransition> relatedStatusTransitions =
                        statusTransitionMap.getOrDefault(transition.getId(), Collections.emptyList());

                List<ActivityStatusTransitionVO> statusTransitionVOs = relatedStatusTransitions.stream()
                        .map(statusTransition -> {
                            ActivityStatusTransitionVO statusVo = new ActivityStatusTransitionVO();
                            BeanUtils.copyProperties(statusTransition, statusVo);
                            return statusVo;
                        })
                        .collect(Collectors.toList());

                // 设置关联的状态转换规则
                vo.setStatusTransitions(statusTransitionVOs);

                return vo;
            }).collect(Collectors.toList());

            // 7. 创建分页结果
            PageInfo<ActivityTransition> pageInfo = new PageInfo<>(transitions);
            BasePageVO<ActivityTransitionVO> pageVO = BasePageVO.of(transitionVOs, pageInfo);

            log.info("分页查询活动节点转换规则列表成功，总数: {}", pageVO.getTotal());
            return pageVO;
        } catch (Exception e) {
            log.error("分页查询活动节点转换规则列表失败", e);
            throw new BusinessException("分页查询活动节点转换规则列表失败: " + e.getMessage());
        }
    }



    /**
     * 根据工作流ID查询活动节点转换规则列表
     *
     * @param workflowId 工作流ID
     * @return 活动节点转换规则列表
     */
    @Override
    public List<ActivityTransitionVO> listActivityTransitionsByWorkflowId(Long workflowId) {
        log.info("根据工作流ID查询活动节点转换规则列表，workflowId: {}", workflowId);

        // 参数验证
        if (workflowId == null) {
            throw new IllegalArgumentException("工作流ID不能为空");
        }

        try {
            // 1. 根据工作流ID查询活动节点转换规则
            List<ActivityTransition> transitions = tableActivityTransitionService.selectByWorkflowId(workflowId);
            if (transitions.isEmpty()) {
                return Collections.emptyList();
            }

            // 2. 提取所有活动节点转换规则ID
            List<Long> transitionIds = transitions.stream()
                    .map(ActivityTransition::getId)
                    .collect(Collectors.toList());

            // 3. 一次性查询所有关联的状态转换规则
            List<ActivityStatusTransition> statusTransitions =
                    tableActivityStatusTransitionService.selectByActivityTransitionIds(transitionIds);

            // 4. 按活动节点转换规则ID分组状态转换规则
            Map<Long, List<ActivityStatusTransition>> statusTransitionMap = statusTransitions.stream()
                    .collect(Collectors.groupingBy(ActivityStatusTransition::getActivityTransitionId));

            // 5. 转换为VO对象并关联状态转换规则
            List<ActivityTransitionVO> transitionVOs = transitions.stream().map(transition -> {
                // 转换活动节点转换规则
                ActivityTransitionVO vo = new ActivityTransitionVO();
                BeanUtils.copyProperties(transition, vo);

                // 获取并转换关联的状态转换规则
                List<ActivityStatusTransition> relatedStatusTransitions =
                        statusTransitionMap.getOrDefault(transition.getId(), Collections.emptyList());

                List<ActivityStatusTransitionVO> statusTransitionVOs = relatedStatusTransitions.stream()
                        .map(statusTransition -> {
                            ActivityStatusTransitionVO statusVo = new ActivityStatusTransitionVO();
                            BeanUtils.copyProperties(statusTransition, statusVo);
                            return statusVo;
                        })
                        .collect(Collectors.toList());

                // 设置关联的状态转换规则
                vo.setStatusTransitions(statusTransitionVOs);

                return vo;
            }).collect(Collectors.toList());

            // 6. 根据ActivityDefinitionEnum的sequence字段对转换规则进行排序
            transitionVOs.sort((t1, t2) -> {
                // 获取from_activity_code对应的sequence
                Integer fromSequence1 = getActivitySequence(t1.getFromActivityCode());
                Integer fromSequence2 = getActivitySequence(t2.getFromActivityCode());

                // 首先根据from_activity_code的sequence进行升序排序
                int fromComparison = fromSequence1.compareTo(fromSequence2);
                if (fromComparison != 0) {
                    return fromComparison;
                }

                // 如果from_activity_code相同，则根据to_activity_code的sequence进行升序排序
                Integer toSequence1 = getActivitySequence(t1.getToActivityCode());
                Integer toSequence2 = getActivitySequence(t2.getToActivityCode());
                return toSequence1.compareTo(toSequence2);
            });

            log.info("根据工作流ID查询活动节点转换规则列表成功，总数: {}，已按执行顺序排序", transitionVOs.size());
            return transitionVOs;
        } catch (Exception e) {
            log.error("根据工作流ID查询活动节点转换规则列表失败", e);
            throw new BusinessException("根据工作流ID查询活动节点转换规则列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据转换规则ID和当前状态获取目标状态码
     *
     * 状态转换逻辑：
     * 1. 如果转换规则ID为空，尝试根据活动节点编码和当前状态码查找匹配的状态转换规则
     * 2. 如果找到状态转换规则：
     *    a. 如果from_status_code为空（初始状态），返回to_status_code
     *    b. 如果from_status_code不为空，检查当前状态是否匹配from_status_code，匹配则返回to_status_code
     * 3. 如果未找到匹配的状态转换规则，对于特殊情况返回相应的状态码：
     *    a. 对于已跳过节点，返回ActivityStatusEnum.SKIPPED
     *    b. 对于待处理节点，返回ActivityStatusEnum.PENDING
     *    c. 其他情况返回默认状态码ActivityStatusEnum.PROCESSING
     *
     * @param transitionId 活动节点转换规则ID，可为null
     * @param currentStatusCode 当前状态码，可为null
     * @param activityCode 活动节点编码
     * @return 目标状态码
     */
    private String getTargetStatusCode(Long transitionId, String currentStatusCode, String activityCode) {
        log.debug("获取目标状态码，转换规则ID: {}, 当前状态码: {}, 活动节点编码: {}", transitionId, currentStatusCode, activityCode);

        try {
            List<ActivityStatusTransition> statusTransitions;

            // 根据转换规则ID查询状态转换规则
            if (transitionId != null) {
                statusTransitions = tableActivityStatusTransitionService.selectByActivityTransitionId(transitionId);
            } else {
                // 如果转换规则ID为空，尝试根据活动节点编码查找所有相关的状态转换规则
                // 这里需要实现一个新的查询方法，暂时返回空列表
                statusTransitions = Collections.emptyList();
                log.debug("转换规则ID为空，无法查询状态转换规则");
            }

            // 如果找到状态转换规则
            if (statusTransitions != null && !statusTransitions.isEmpty()) {
                // 过滤出匹配当前活动节点的状态转换规则
                List<ActivityStatusTransition> matchedTransitions = statusTransitions.stream()
                        .filter(st -> activityCode.equals(st.getActivityCode()))
                        .collect(Collectors.toList());

                // 如果没有匹配当前活动节点的规则，使用所有规则
                if (matchedTransitions.isEmpty()) {
                    matchedTransitions = statusTransitions;
                }

                // 查找初始状态设置规则（from_status_code为空）
                for (ActivityStatusTransition statusTransition : matchedTransitions) {
                    if (statusTransition.getFromStatusCode() == null || statusTransition.getFromStatusCode().isEmpty()) {
                        log.debug("找到初始状态设置规则，目标状态码: {}", statusTransition.getToStatusCode());
                        return statusTransition.getToStatusCode();
                    }
                }

                // 如果当前状态码不为空，查找匹配的状态转换规则
                if (currentStatusCode != null && !currentStatusCode.isEmpty()) {
                    for (ActivityStatusTransition statusTransition : matchedTransitions) {
                        if (currentStatusCode.equals(statusTransition.getFromStatusCode())) {
                            log.debug("找到匹配的状态转换规则，从 {} 转换到 {}",
                                    statusTransition.getFromStatusCode(), statusTransition.getToStatusCode());
                            return statusTransition.getToStatusCode();
                        }
                    }
                }

                // 如果没有找到匹配的规则，但有状态转换规则，使用第一个规则的目标状态
                if (!matchedTransitions.isEmpty()) {
                    String toStatusCode = matchedTransitions.get(0).getToStatusCode();
                    log.debug("未找到完全匹配的状态转换规则，使用第一个规则的目标状态码: {}", toStatusCode);
                    return toStatusCode;
                }
            }

            // 如果未找到匹配的状态转换规则，根据情况返回相应的状态码
            // 默认状态码
            final String DEFAULT_STATUS_CODE = ActivityStatusEnum.UNPROCESSED.getCode();
            log.debug("无法确定目标状态码，使用默认状态码: {}", DEFAULT_STATUS_CODE);
            return DEFAULT_STATUS_CODE;
        } catch (Exception e) {
            log.error("获取目标状态码失败", e);
            // 默认状态码
            final String DEFAULT_STATUS_CODE = ActivityStatusEnum.UNPROCESSED.getCode();
            return DEFAULT_STATUS_CODE;
        }
    }

    /**
     * 获取活动节点编码对应的sequence值
     * 如果在枚举中不存在，返回Integer.MAX_VALUE使其排在最后
     *
     * @param activityCode 活动节点编码
     * @return sequence值，如果不存在则返回Integer.MAX_VALUE
     */
    private Integer getActivitySequence(String activityCode) {
        if (activityCode == null || activityCode.trim().isEmpty()) {
            return Integer.MAX_VALUE;
        }

        ActivityDefinitionEnum activityEnum = ActivityDefinitionEnum.getByCode(activityCode);
        if (activityEnum != null) {
            return activityEnum.getSequence();
        } else {
            // 如果在枚举中不存在，返回最大值使其排在最后
            log.debug("活动节点编码 {} 在ActivityDefinitionEnum中不存在，将排在最后", activityCode);
            return Integer.MAX_VALUE;
        }
    }
}