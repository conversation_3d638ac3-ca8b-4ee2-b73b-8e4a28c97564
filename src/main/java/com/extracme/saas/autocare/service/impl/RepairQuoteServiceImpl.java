package com.extracme.saas.autocare.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.enums.RepairPicTypeEnum;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.RepairQuoteSubmitDTO;
import com.extracme.saas.autocare.model.dto.RepairQuoteUpdateDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskUpdateDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowProcessDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.service.RepairQuoteService;
import com.extracme.saas.autocare.service.RepairTaskService;
import com.extracme.saas.autocare.service.WorkflowService;
import com.extracme.saas.autocare.util.SessionUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 维修报价服务实现类
 */
@Slf4j
@Service
public class RepairQuoteServiceImpl implements RepairQuoteService {

    @Autowired
    private RepairTaskService repairTaskService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private WorkflowService workflowService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRepairQuote(RepairQuoteUpdateDTO repairQuoteDTO) {
        log.info("保存维修报价信息: {}", repairQuoteDTO);

        // 获取维修任务
        MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(repairQuoteDTO.getTaskNo());
        if (mtcRepairTask == null) {
            throw new BusinessException("未找到维修任务: " + repairQuoteDTO.getTaskNo());
        }

        // 获取工作流实例
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(repairQuoteDTO.getTaskNo());
        if (workflowInstance == null) {
            throw new BusinessException("未找到工作流实例: " + repairQuoteDTO.getTaskNo());
        }
        if (!workflowInstance.getCurrentActivityCode().equals("REPAIR_QUOTATION")
                && !workflowInstance.getStatusCode().equals("PROCESSING")) {
            throw new BusinessException("维修任务节点状态不是处理中, 无法保存报价信息: " + repairQuoteDTO.getTaskNo());
        }

        // 检查图片数量是否超出限制
        if (repairQuoteDTO.pictureSizeOutOfRange()) {
            throw new BusinessException("图片数量超出限制！");
        }

        // 处理预计修理天数和预计完成时间
        if (repairQuoteDTO.getExpectedRepairDays() != null && repairQuoteDTO.getExpectedRepairDays() > 0) {
            calculateExpectedRepairCompleteTime(mtcRepairTask, repairQuoteDTO.getExpectedRepairDays());
        }

        // 更新维修任务报价信息
        RepairTaskUpdateDTO updateDTO = new RepairTaskUpdateDTO();
        BeanUtils.copyProperties(repairQuoteDTO, updateDTO);
        // 保存维修任务
        repairTaskService.saveRepairTask(mtcRepairTask.getId(), updateDTO);

        // 保存图片逻辑
        saveRepairQuoteImages(repairQuoteDTO);
    }

    /**
     * 保存维修报价相关图片
     * 
     * @param repairQuoteDTO 维修报价DTO
     * @throws BusinessException 业务异常
     */
    private void saveRepairQuoteImages(RepairQuoteUpdateDTO repairQuoteDTO) {
        try {
            // 构建媒体类型映射
            Map<BigDecimal, List<String>> mediaTypeMap = new HashMap<>();
            
            // 添加各种类型的图片到映射中
            if (repairQuoteDTO.getDamagedPartPicture() != null) {
                mediaTypeMap.put(RepairPicTypeEnum.DAMAGED_PART_PICTURE.getTypeId(), repairQuoteDTO.getDamagedPartPicture());
            }
            
            if (repairQuoteDTO.getDamagedPartVideo() != null) {
                mediaTypeMap.put(RepairPicTypeEnum.DAMAGED_PART_VIDEO.getTypeId(), repairQuoteDTO.getDamagedPartVideo());
            }
            
            if (repairQuoteDTO.getAccidentLiabilityConfirmationPicture() != null) {
                mediaTypeMap.put(RepairPicTypeEnum.ACCIDENT_LIABILITY_CONFIRMATION_PICTURE.getTypeId(), 
                        repairQuoteDTO.getAccidentLiabilityConfirmationPicture());
            }
            
            if (repairQuoteDTO.getInsuranceCompanyLossOrderPicture() != null) {
                mediaTypeMap.put(RepairPicTypeEnum.INSURANCE_COMPANY_LOSS_ORDER_PICTURE.getTypeId(), 
                        repairQuoteDTO.getInsuranceCompanyLossOrderPicture());
            }
            
            if (repairQuoteDTO.getOurDriverLicensePicture() != null) {
                mediaTypeMap.put(RepairPicTypeEnum.OUR_DRIVER_LICENSE_PICTURE.getTypeId(), 
                        repairQuoteDTO.getOurDriverLicensePicture());
            }
            
            // 如果有图片需要处理，则调用processMediaFiles方法
            if (!mediaTypeMap.isEmpty()) {
                String operatorName = SessionUtils.getUsername();
                repairTaskService.processMediaFiles(repairQuoteDTO.getTaskNo(), mediaTypeMap, operatorName);
                log.info("维修报价图片保存成功，任务编号: {}", repairQuoteDTO.getTaskNo());
            }
        } catch (Exception e) {
            log.error("保存维修报价图片失败: {}", e.getMessage(), e);
            throw new BusinessException("保存维修报价图片失败: " + e.getMessage());
        }
    }

    /**
     * 计算预计维修完成时间
     * 
     * @param mtcRepairTask      维修任务对象
     * @param expectedRepairDays 预计修理天数
     */
    private void calculateExpectedRepairCompleteTime(MtcRepairTask mtcRepairTask, Long expectedRepairDays) {
        Date vehicleReceiveTime = mtcRepairTask.getVehicleReciveTime();
        if (vehicleReceiveTime != null) {
            try {
                // 使用Java 8日期时间API计算预计完成时间
                LocalDateTime receiveDateTime = vehicleReceiveTime.toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();
                LocalDateTime expectedCompleteDateTime = receiveDateTime
                        .plusDays(expectedRepairDays);
                Date expectedRepairComplete = Date.from(expectedCompleteDateTime
                        .atZone(ZoneId.systemDefault())
                        .toInstant());

                // 设置预计完成时间
                mtcRepairTask.setExpectedRepairComplete(expectedRepairComplete);
                log.info("设置维修任务预计完成时间: {}, 任务编号: {}",
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(expectedCompleteDateTime),
                        mtcRepairTask.getTaskNo());
            } catch (Exception e) {
                log.error("计算预计完成时间失败, 任务编号: {}, 错误: {}", mtcRepairTask.getTaskNo(), e.getMessage());
            }
        } else {
            log.warn("维修任务车辆接收时间为空, 任务编号: {}", mtcRepairTask.getTaskNo());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitRepairQuote(RepairQuoteSubmitDTO submitDTO) {
        log.info("提交维修报价审核: {}", submitDTO);

        // 1. 保存维修报价信息
        RepairQuoteUpdateDTO updateDTO = new RepairQuoteUpdateDTO();
        BeanUtils.copyProperties(submitDTO, updateDTO);
        saveRepairQuote(updateDTO);

        // TODO 使用本地配件库
        // TODO 使用精友配件库

        // 2. 获取工作流实例
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(submitDTO.getTaskNo());
        if (workflowInstance == null) {
            throw new BusinessException("未找到工作流实例: " + submitDTO.getTaskNo());
        }

        // 3. 调用工作流服务处理节点
        WorkflowProcessDTO processDTO = new WorkflowProcessDTO();
        processDTO.setTriggerEvent("SUBMIT_QUOTE");
        processDTO.setActivityCode(workflowInstance.getCurrentActivityCode());
        processDTO.setOperator(SessionUtils.getUsername());

        try {
            workflowService.processNode(workflowInstance.getId(), processDTO);
            log.info("提交维修报价审核成功, 任务编号: {}", submitDTO.getTaskNo());
        } catch (Exception e) {
            log.error("提交维修报价审核失败: {}", e.getMessage(), e);
            throw new BusinessException("提交维修报价审核失败: " + e.getMessage());
        }
    }
}
