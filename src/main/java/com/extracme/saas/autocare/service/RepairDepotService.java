package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.RepairDepotCreateDTO;
import com.extracme.saas.autocare.model.dto.RepairDepotQueryDTO;
import com.extracme.saas.autocare.model.dto.RepairDepotUpdateDTO;
import com.extracme.saas.autocare.model.dto.RepairDepotUpdateStatusDTO;
import com.extracme.saas.autocare.model.vo.RepairDepotDetailsVO;
import com.extracme.saas.autocare.model.vo.RepairDepotListVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;

import java.util.List;

/**
 * 修理厂服务接口
 */
public interface RepairDepotService {

    /**
     * 分页查询列表
     *
     * @param queryDTO 查询参数
     * @return 维修厂列表
     */
    BasePageVO<RepairDepotListVO> queryRepairDepotList(RepairDepotQueryDTO queryDTO);

    /**
     * 获取修理厂详情
     *
     * @param id 维修厂ID
     * @return 维修厂详情
     */
    RepairDepotDetailsVO getRepairDepotDetails(Long id);

    /**
     * 创建非合作修理厂
     *
     * @param createDTO 创建参数
     * @return 维修厂ID
     */
    void createRepairDepot(RepairDepotCreateDTO createDTO);

    /**
     * 修改修理厂
     *
     * @param updateDTO 修改参数
     */
    void updateRepairDepot(RepairDepotUpdateDTO updateDTO);

    /**
     * 删除修理厂
     *
     * @param id 维修厂ID
     */
    void deleteRepairDepot(Long id);

    /**
     * 更新修理厂状态
     *
     * @param updateStatusDTO 更新参数
     */
    void updateRepairDepotStatus(RepairDepotUpdateStatusDTO updateStatusDTO);


    /**
     * 获取修理厂下拉列表
     * @return 修理厂下拉数据列表，ID类型为String
     */
    List<ComboVO<String>> getRepairDepotCombo();

    /**
     * 根据租户ID获取修理厂下拉列表
     * 如果租户ID为空，则使用当前登录用户的租户ID
     *
     * @param tenantId 租户ID，可为空
     * @return 修理厂下拉数据列表，ID类型为String
     */
    List<ComboVO<String>> getRepairDepotComboByTenantId(Long tenantId);
}