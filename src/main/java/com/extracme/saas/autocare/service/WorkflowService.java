package com.extracme.saas.autocare.service;

import java.util.List;

import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionCreateDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionDeleteDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionQueryDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionUpdateDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowInstanceQueryDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowProcessDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowStartDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowTemplateCreateDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowTemplateQueryDTO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.workflow.ActivityTransitionDetailVO;
import com.extracme.saas.autocare.model.vo.workflow.ActivityTransitionVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowInstanceDetailVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowInstanceVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowProgressVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowTemplateDetailVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowTemplateVO;

/**
 * 工作流服务接口
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
public interface WorkflowService {

    /**
     * 创建工作流模板
     *
     * @param createDTO 创建工作流模板请求对象
     * @return 创建成功的模板ID
     */
    Long createTemplate(WorkflowTemplateCreateDTO createDTO);


    /**
     * 获取工作流模板详情
     *
     * @param templateId 模板ID
     * @return 工作流模板详情视图对象
     */
    WorkflowTemplateDetailVO getTemplateDetail(Long templateId);

    /**
     * 查询工作流模板列表
     *
     * @param queryDTO 查询条件对象
     * @return 工作流模板列表
     */
    List<WorkflowTemplateVO> listTemplates(WorkflowTemplateQueryDTO queryDTO);

    /**
     * 分页查询工作流模板列表
     *
     * @param queryDTO 查询条件对象
     * @return 工作流模板分页列表
     */
    BasePageVO<WorkflowTemplateVO> getTemplateList(WorkflowTemplateQueryDTO queryDTO);

    /**
     * 启动工作流实例
     *
     * @param startDTO 启动工作流实例请求对象
     * @return 创建的工作流实例ID
     */
    Long startWorkflow(WorkflowStartDTO startDTO);

    /**
     * 处理工作流节点
     *
     * @param instanceId 实例ID
     * @param processDTO 节点处理请求对象
     */
    void processNode(Long instanceId, WorkflowProcessDTO processDTO);

    /**
     * 获取工作流实例详情
     *
     * @param instanceId 实例ID
     * @return 工作流实例详情视图对象
     */
    WorkflowInstanceDetailVO getInstanceDetail(Long instanceId);

    /**
     * 查询工作流实例列表
     *
     * @param queryDTO 查询条件对象
     * @return 工作流实例列表
     */
    List<WorkflowInstanceVO> listInstances(WorkflowInstanceQueryDTO queryDTO);

    /**
     * 分页查询工作流实例列表
     *
     * @param queryDTO 查询条件对象
     * @return 工作流实例分页列表
     */
    BasePageVO<WorkflowInstanceVO> getInstanceList(WorkflowInstanceQueryDTO queryDTO);

    /**
     * 获取工作流实例进度
     *
     * @param instanceId 实例ID
     * @return 工作流进度视图对象
     */
    WorkflowProgressVO getInstanceProgress(Long instanceId);

    /**
     * 更新工作流模板状态
     *
     * @param templateId 模板ID
     * @param isActive 是否启用（1:启用, 0:禁用）
     */
    void updateTemplateStatus(Long templateId, Integer isActive);

    /**
     * 创建活动节点转换规则
     *
     * @param createDTO 创建活动节点转换规则请求对象
     * @return 创建成功的活动节点转换规则ID
     */
    Long createActivityTransition(ActivityTransitionCreateDTO createDTO);

    /**
     * 更新活动节点转换规则
     *
     * @param updateDTO 更新活动节点转换规则请求对象
     */
    void updateActivityTransition(ActivityTransitionUpdateDTO updateDTO);

    /**
     * 删除活动节点转换规则
     *
     * @param deleteDTO 删除活动节点转换规则请求对象
     */
    void deleteActivityTransition(ActivityTransitionDeleteDTO deleteDTO);

    /**
     * 获取活动节点转换规则详情
     *
     * @param transitionId 转换规则ID
     * @return 活动节点转换规则详情视图对象
     */
    ActivityTransitionDetailVO getActivityTransitionDetail(Long transitionId);

    /**
     * 分页查询活动节点转换规则列表
     *
     * @param queryDTO 查询条件对象
     * @return 活动节点转换规则分页列表
     */
    BasePageVO<ActivityTransitionVO> getActivityTransitionList(ActivityTransitionQueryDTO queryDTO);

    /**
     * 根据工作流ID查询活动节点转换规则列表
     *
     * @param workflowId 工作流ID
     * @return 活动节点转换规则列表
     */
    List<ActivityTransitionVO> listActivityTransitionsByWorkflowId(Long workflowId);


}