package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.dto.*;
import com.extracme.saas.autocare.model.vo.RepairItemLibraryCheckListVO;
import com.extracme.saas.autocare.model.vo.RepairItemLibraryListVO;
import com.extracme.saas.autocare.model.vo.RepairItemLibraryLocalVO;
import com.extracme.saas.autocare.model.vo.RepairItemLibraryVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;

/**
 * 维修项目服务接口
 */
public interface RepairItemLibraryService {

    /**
     * 分页查询列表
     *
     * @param queryDTO 查询参数
     * @return 维修项目列表
     */
    BasePageVO<RepairItemLibraryListVO> queryItemLibraryList(RepairItemLibraryQueryDTO queryDTO);

    /**
     * 获取维修项目详情
     *
     * @param id 维修项目ID
     * @return 维修项目详情
     */
    RepairItemLibraryVO getItemLibraryDetails(Long id);

    /**
     * 创建维修项目
     *
     * @param createDTO 创建参数
     * @return 维修项目ID
     */
    void createItemLibrary(RepairItemLibraryCreateDTO createDTO);

    /**
     * 修改维修项目
     *
     * @param updateDTO 修改参数
     */
    void updateItemLibrary(RepairItemLibraryUpdateDTO updateDTO);

    /**
     * 更新维修项目状态
     *
     * @param updateStatusDTO 更新参数
     */
    void updateItemLibraryStatus(RepairItemLibraryUpdateStatusDTO updateStatusDTO);


    /**
     * 分页查询本地维修项目列表
     *
     * @param queryDTO 查询参数
     * @return 维修项目列表
     */
    BasePageVO<RepairItemLibraryLocalVO> queryLocalList(RepairItemLibraryLocalQueryDTO queryDTO);

    /**
     * 获取本地维修项目详情
     *
     * @param id 维修项目ID
     * @return 维修项目详情
     */
    RepairItemLibraryLocalVO getItemLibraryLocalDetails(Long id);

    /**
     * 创建本地维修项目
     *
     * @param createDTO 创建参数
     * @return 维修项目ID
     */
    void createItemLibraryLocal(RepairItemLibraryLocalCreateDTO createDTO);

    /**
     * 修改本地维修项目
     *
     * @param updateDTO 修改参数
     */
    void updateItemLibraryLocal(RepairItemLibraryLocalUpdateDTO updateDTO);

    /**
     * 更新本地维修项目状态
     *
     * @param updateStatusDTO 更新参数
     */
    void updateItemLibraryLocalStatus(RepairItemLibraryUpdateStatusDTO updateStatusDTO);

    /**
     * 查询待定损配件库列表
     *
     * @param queryDTO 查询参数
     * @return 待定损配件列表
     */
    BasePageVO<RepairItemLibraryCheckListVO> queryLibraryCheckList(RepairItemLibraryCheckQueryDTO queryDTO);

}