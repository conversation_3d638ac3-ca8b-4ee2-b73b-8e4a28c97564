package com.extracme.saas.autocare.service;

import javax.servlet.http.HttpServletRequest;

import com.extracme.saas.autocare.model.dto.RepairRemarkDTO;
import com.extracme.saas.autocare.model.dto.TokenDTO;
import com.extracme.saas.autocare.model.jingyou.PacketBeanAssBack;
import com.extracme.saas.autocare.model.jingyou.PacketBeanEvaBack;
import com.extracme.saas.autocare.model.jingyou.PacketResponse;

public interface InsuranceQuoteService {

    /**
     * 定损报价添加备注
     * 
     * @param repairRemarkDTO
     * @param comModel
     * @return
     */
    void insertRepairRemark(RepairRemarkDTO repairRemarkDTO, TokenDTO tokneDto);

    /**
     * 定损报价删除备注
     * 
     * @param id
     * @param comModel
     * @return
     */
    void deleteRepairRemark(String id, String repairStage, TokenDTO tokneDto);

    // /**
    // * 定损报价查看或占据任务
    // *
    // * @param id
    // * 任务id
    // * @param type
    // * 操作类型
    // * @return
    // */
    // DefaultServiceRespDTO insuranceQuoteView(Long id, Integer type,
    // HttpServletRequest request);

    /**
     * 定损报价（请求定损系统）
     * 
     * @param id
     *                任务id
     * @param request
     *                .
     * @return .
     */
    String lossAssessment(Long id, HttpServletRequest request);

    /**
     * 定损报价（请求定损系统）
     *
     * @param id      任务id
     * @param request .
     * @return .
     */
    String lossAssessment(Long id, String parameterString, HttpServletRequest request);

    /**
     * 核损核价（请求定损系统）
     * 
     * @param id
     *                任务id
     * @param request
     *                .
     * @return .
     */
    String evaluateDamage(Long id, HttpServletRequest request);

    /**
     * 核损核价（请求定损系统）
     *
     * @param id              任务id
     * @param parameterString 回调参数
     * @param request
     *                        .
     * @return .
     */
    String evaluateDamage(Long id, String parameterString, HttpServletRequest request);

    /**
     * 定损查看（请求定损系统）
     * 
     * @param id       任务id
     * @param comModel
     * @return .
     */
    String viewLoss(Long id, TokenDTO tokenDto);

    /**
     * 定损完成返回
     * 
     * @param packetBeanAssBack
     *                          定损系统返回数据
     * @return .
     */
    PacketResponse lossAssessmentBack(PacketBeanAssBack packetBeanAssBack);

    /**
     * 核损完成返回
     * 
     * @param packetBeanEvaBack
     *                          定损系统返回数据
     * @return .
     */
    PacketResponse evaluateLossBack(PacketBeanEvaBack packetBeanEvaBack);
}
