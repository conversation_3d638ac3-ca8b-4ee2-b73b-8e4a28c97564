package com.extracme.saas.autocare.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;
import com.extracme.saas.autocare.model.dto.RepairDepotCreateDTO;
import com.extracme.saas.autocare.model.dto.RepairDepotQueryDTO;
import com.extracme.saas.autocare.model.dto.RepairDepotUpdateDTO;
import com.extracme.saas.autocare.model.dto.RepairDepotUpdateStatusDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairDepotCooperativeBranch;
import com.extracme.saas.autocare.model.entity.MtcRepairDepotInfo;
import com.extracme.saas.autocare.model.entity.MtcRepairDepotVehicleModelInfo;
import com.extracme.saas.autocare.model.entity.MtcRepairDepotWarrantyVehicleModelInfo;
import com.extracme.saas.autocare.model.vo.RepairDepotDetailsVO;
import com.extracme.saas.autocare.model.vo.RepairDepotListVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TableRepairDepotCooperativeBranchService;
import com.extracme.saas.autocare.repository.TableRepairDepotInfoService;
import com.extracme.saas.autocare.repository.TableRepairDepotVehicleModelService;
import com.extracme.saas.autocare.repository.TableRepairDepotWarrantyVehicleModelService;
import com.extracme.saas.autocare.service.RepairDepotService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * 修理厂服务实现类
 */
@Slf4j
@Service
public class RepairDepotServiceImpl implements RepairDepotService {

    @Autowired
    private TableRepairDepotInfoService repairDepotRepository;

    @Autowired
    private TableRepairDepotCooperativeBranchService cooperativeBranchRepository;

    @Autowired
    private TableRepairDepotVehicleModelService vehicleModelRepository;

    @Autowired
    private TableRepairDepotWarrantyVehicleModelService warrantyVehicleModelRepository;

    @Override
    public BasePageVO<RepairDepotListVO> queryRepairDepotList(RepairDepotQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<MtcRepairDepotInfo> list = repairDepotRepository.queryRepairDepotList(queryDTO);
        PageInfo<MtcRepairDepotInfo> pageInfo = new PageInfo<>(list);
        List<RepairDepotListVO> voList = list.stream().map(repairDepotInfo -> {
            RepairDepotListVO vo = new RepairDepotListVO();
            BeanUtils.copyProperties(repairDepotInfo, vo);
            return vo;
        }).collect(Collectors.toList());
        // 构建返回结果
        return BasePageVO.of(voList, pageInfo);
    }

    @Override
    public RepairDepotDetailsVO getRepairDepotDetails(Long id) {
        MtcRepairDepotInfo repairDepot = repairDepotRepository.selectById(id);
        if (repairDepot == null) {
            return null;
        }
        RepairDepotDetailsVO vo = new RepairDepotDetailsVO();
        BeanUtils.copyProperties(repairDepot, vo);
        // 获取合作分公司信息
        List<MtcRepairDepotCooperativeBranch> cooperativeBranchList = cooperativeBranchRepository.queryCooperativeBranchList(repairDepot.getRepairDepotId());
        if (CollectionUtils.isNotEmpty(cooperativeBranchList)){
            vo.setOtherOrgIdList(cooperativeBranchList.stream().map(MtcRepairDepotCooperativeBranch::getOrgId).collect(Collectors.toList()));
        }
        // 获取可保修车型信息
        List<MtcRepairDepotVehicleModelInfo> vehicleModelList = vehicleModelRepository.queryVehicleModelList(repairDepot.getRepairDepotId());
        if (CollectionUtils.isNotEmpty(vehicleModelList)){
            vo.setVehicleModelList(vehicleModelList.stream().map(MtcRepairDepotVehicleModelInfo::getVehicleModelSeq).collect(Collectors.toList()));
        }
        // 获取可保修车型信息
        List<MtcRepairDepotWarrantyVehicleModelInfo> warrantyVehicleModelList = warrantyVehicleModelRepository.queryVehicleModelList(repairDepot.getRepairDepotId());
        if (CollectionUtils.isNotEmpty(warrantyVehicleModelList)){
            vo.setWarrantyVehicleModelList(warrantyVehicleModelList.stream().map(MtcRepairDepotWarrantyVehicleModelInfo::getVehicleModelSeq).collect(Collectors.toList()));
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createRepairDepot(RepairDepotCreateDTO createDTO) {
        // 判断姓名是否存在
        MtcRepairDepotInfo repairDepot = repairDepotRepository.selectByRepairDepotName(createDTO.getRepairDepotName());
        if (repairDepot != null) {
            throw new BusinessException("修理厂名称已存在");
        }

        MtcRepairDepotInfo repairDepotInfo = new MtcRepairDepotInfo();
        BeanUtils.copyProperties(createDTO, repairDepotInfo);
        //TODO Redis 设置唯一ID
        repairDepotInfo.setRepairDepotId(SessionUtils.getTenantId() + "_" + System.currentTimeMillis());

        // 是否保修点(0-否 1-是)
        int warrantyPoint = createDTO.getWarrantyPoint();
        if (warrantyPoint == 1) {
            List<Long> warrantyVehicleModelList = createDTO.getWarrantyVehicleModelList();
            if (CollectionUtils.isEmpty(warrantyVehicleModelList)) {
                throw new BusinessException("请选择可保修车型");
            }
            // 新增可保修车型信息
            List<MtcRepairDepotWarrantyVehicleModelInfo> repairDepotWarrantyVehicleModelList = warrantyVehicleModelList.stream().map(otherOrgId -> {
                MtcRepairDepotWarrantyVehicleModelInfo warrantyVehicleModelInfo = new MtcRepairDepotWarrantyVehicleModelInfo();
                warrantyVehicleModelInfo.setRepairDepotId(repairDepotInfo.getRepairDepotId());
                warrantyVehicleModelInfo.setVehicleModelSeq(otherOrgId);
                return warrantyVehicleModelInfo;
            }).collect(Collectors.toList());
            warrantyVehicleModelRepository.batchInsert(repairDepotWarrantyVehicleModelList);
        }

        // 添加其他组织机构信息
        List<String> otherOrgIdList = createDTO.getOtherOrgIdList();
        List<MtcRepairDepotCooperativeBranch> cooperativeBranchList = otherOrgIdList.stream().map(otherOrgId -> {
            MtcRepairDepotCooperativeBranch cooperativeBranch = new MtcRepairDepotCooperativeBranch();
            cooperativeBranch.setRepairDepotId(repairDepotInfo.getRepairDepotId());
            cooperativeBranch.setOrgId(otherOrgId);
            return cooperativeBranch;
        }).collect(Collectors.toList());
        cooperativeBranchRepository.batchInsert(cooperativeBranchList);

        // 添加可修理车型
        List<Long> repairVehicleModelList = createDTO.getRepairVehicleModelList();
        List<MtcRepairDepotVehicleModelInfo> vehicleModelList = repairVehicleModelList.stream().map(vehicleModelId -> {
            MtcRepairDepotVehicleModelInfo vehicleModel = new MtcRepairDepotVehicleModelInfo();
            vehicleModel.setRepairDepotId(repairDepotInfo.getRepairDepotId());
            vehicleModel.setVehicleModelSeq(vehicleModelId);
            return vehicleModel;
        }).collect(Collectors.toList());
        vehicleModelRepository.batchInsert(vehicleModelList);

        repairDepotRepository.insert(repairDepotInfo);

        //TODO 添加日志
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRepairDepot(RepairDepotUpdateDTO updateDTO) {
        MtcRepairDepotInfo repairDepotInfo = repairDepotRepository.selectById(updateDTO.getId());
        if (repairDepotInfo == null) {
            throw new BusinessException("修理厂信息不存在");
        }
        // 判断名称是否已存在
        if (!StringUtils.equals(updateDTO.getRepairDepotName(), repairDepotInfo.getRepairDepotName())) {
            MtcRepairDepotInfo repairDepot = repairDepotRepository.selectByRepairDepotName(updateDTO.getRepairDepotName());
            if (repairDepot != null) {
                throw new BusinessException("修理厂名称已存在");
            }
        }
        BeanUtils.copyProperties(updateDTO, repairDepotInfo);

        warrantyVehicleModelRepository.deleteByRepairDepotId(repairDepotInfo.getRepairDepotId());
        // 是否保修点(0-否 1-是)
        int warrantyPoint = updateDTO.getWarrantyPoint();
        if (warrantyPoint == 1) {
            List<Long> warrantyVehicleModelList = updateDTO.getWarrantyVehicleModelList();
            if (CollectionUtils.isEmpty(warrantyVehicleModelList)) {
                throw new BusinessException("请选择可保修车型");
            }
            // 新增可保修车型信息
            List<MtcRepairDepotWarrantyVehicleModelInfo> repairDepotWarrantyVehicleModelList = warrantyVehicleModelList.stream().map(otherOrgId -> {
                MtcRepairDepotWarrantyVehicleModelInfo warrantyVehicleModelInfo = new MtcRepairDepotWarrantyVehicleModelInfo();
                warrantyVehicleModelInfo.setRepairDepotId(repairDepotInfo.getRepairDepotId());
                warrantyVehicleModelInfo.setVehicleModelSeq(otherOrgId);
                return warrantyVehicleModelInfo;
            }).collect(Collectors.toList());
            warrantyVehicleModelRepository.batchInsert(repairDepotWarrantyVehicleModelList);
        }

        cooperativeBranchRepository.deleteByRepairDepotId(repairDepotInfo.getRepairDepotId());
        // 添加其他组织机构信息
        List<String> otherOrgIdList = updateDTO.getOtherOrgIdList();
        List<MtcRepairDepotCooperativeBranch> cooperativeBranchList = otherOrgIdList.stream().map(otherOrgId -> {
            MtcRepairDepotCooperativeBranch cooperativeBranch = new MtcRepairDepotCooperativeBranch();
            cooperativeBranch.setRepairDepotId(repairDepotInfo.getRepairDepotId());
            cooperativeBranch.setOrgId(otherOrgId);
            return cooperativeBranch;
        }).collect(Collectors.toList());
        cooperativeBranchRepository.batchInsert(cooperativeBranchList);

        vehicleModelRepository.deleteByRepairDepotId(repairDepotInfo.getRepairDepotId());
        // 添加可修理车型
        List<Long> repairVehicleModelList = updateDTO.getRepairVehicleModelList();
        List<MtcRepairDepotVehicleModelInfo> vehicleModelList = repairVehicleModelList.stream().map(vehicleModelId -> {
            MtcRepairDepotVehicleModelInfo vehicleModel = new MtcRepairDepotVehicleModelInfo();
            vehicleModel.setRepairDepotId(repairDepotInfo.getRepairDepotId());
            vehicleModel.setVehicleModelSeq(vehicleModelId);
            return vehicleModel;
        }).collect(Collectors.toList());
        vehicleModelRepository.batchInsert(vehicleModelList);

        //TODO 添加日志
        repairDepotRepository.updateSelectiveById(repairDepotInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRepairDepot(Long id) {
        MtcRepairDepotInfo repairDepot = repairDepotRepository.selectById(id);
        if (repairDepot == null) {
            return;
        }
        // TODO 判断是否可以删除
        // if (false) {
        //     throw new BusinessException("无法删除，此维修厂还有维修任务没完成");
        // }

        repairDepot.setDelFlag(1);
        repairDepotRepository.updateSelectiveById(repairDepot);
        //TODO 记录日志
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRepairDepotStatus(RepairDepotUpdateStatusDTO updateStatusDTO) {
        MtcRepairDepotInfo repairDepot = repairDepotRepository.selectById(updateStatusDTO.getId());
        if (repairDepot == null) {
            throw new BusinessException(-1, "修理厂信息不存在");
        }
        // 维修厂状态(0-无效 1-有效)
        int repairDepotStatus = updateStatusDTO.getRepairDepotStatus();
        if (repairDepotStatus == 0){
            // TODO 判断该维修厂是否有未完成的维修任务，无法关闭
        }
        repairDepot.setStatus(repairDepotStatus);
        repairDepotRepository.updateSelectiveById(repairDepot);
        //TODO 记录日志
    }

    @Override
    @Transactional(readOnly = true)
    public List<ComboVO<String>> getRepairDepotCombo() {
        // 调用根据租户ID查询的方法，传入null表示使用当前登录用户的租户ID
        return getRepairDepotComboByTenantId(null);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ComboVO<String>> getRepairDepotComboByTenantId(Long tenantId) {
        try {
            // 保存当前租户上下文
            Long currentTenantId = TenantContextHolder.getTenantId();
            String currentSchema = TenantContextHolder.getTenantSchema();

            try {
                // 如果传入的租户ID不为空，则设置租户上下文
                if (tenantId != null) {
                    log.debug("使用指定的租户ID查询修理厂: {}", tenantId);
                    TenantContextHolder.setTenant(tenantId);
                } else {
                    // 如果未传入租户ID，则使用当前登录用户的租户ID
                    log.debug("使用当前登录用户的租户ID查询修理厂");
                }

                String schema = TenantContextHolder.getTenantSchema();

                // 如果是默认租户（auto_care_saas），返回空列表，因为公共架构不包含修理厂数据
                if ("auto_care_saas".equals(schema)) {
                    log.debug("当前使用默认租户[{}]，不包含修理厂数据，返回空列表", schema);
                    return new ArrayList<>();
                }

                // 查询修理厂
                log.debug("正在获取租户[{}]的修理厂列表", schema);
                List<MtcRepairDepotInfo> depots = repairDepotRepository.findValidDepots();
                List<ComboVO<String>> result = new ArrayList<>();

                for (MtcRepairDepotInfo depot : depots) {
                    ComboVO<String> vo = new ComboVO<>();
                    vo.setId(depot.getRepairDepotId());
                    vo.setValue(depot.getRepairDepotName());
                    result.add(vo);
                }

                log.debug("成功获取租户[{}]的修理厂列表，共{}条数据", schema, result.size());
                return result;
            } finally {
                // 恢复原始租户上下文
                if (tenantId != null && !tenantId.equals(currentTenantId)) {
                    if (currentTenantId != null) {
                        TenantContextHolder.setTenant(currentTenantId);
                    } else {
                        TenantContextHolder.clear();
                    }
                    log.debug("已恢复原始租户上下文: {}", currentSchema);
                }
            }
        } catch (Exception e) {
            log.error("获取修理厂下拉列表失败", e);
            throw new RuntimeException("获取修理厂下拉列表失败");
        }
    }
}