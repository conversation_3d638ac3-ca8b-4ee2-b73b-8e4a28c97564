package com.extracme.saas.autocare.service;

import com.extracme.saas.autocare.model.vo.base.ComboVO;

import java.util.List;

/**
 * 组织机构服务接口
 */
public interface OrgService {

    /**
     * 获取组织机构下拉列表
     * 根据当前登录用户的租户信息，返回对应的组织机构列表
     *
     * @return 组织机构下拉数据列表，ID类型为String，value为org_name
     */
    List<ComboVO<String>> getOrgCombo();

    /**
     * 根据租户ID获取组织机构下拉列表
     * 如果租户ID为空，则使用当前登录用户的租户ID
     *
     * @param tenantId 租户ID，可为空
     * @return 组织机构下拉数据列表，ID类型为String，value为org_name
     */
    List<ComboVO<String>> getOrgComboByTenantId(Long tenantId);
}
