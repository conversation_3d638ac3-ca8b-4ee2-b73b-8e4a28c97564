package com.extracme.saas.autocare.service.impl;

import java.util.Calendar;
import java.util.Date;
import java.util.Optional;
import java.util.Random;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.model.entity.SmsSendRecord;
import com.extracme.saas.autocare.model.entity.SysVerificationCode;
import com.extracme.saas.autocare.repository.TableSmsSendRecordService;
import com.extracme.saas.autocare.repository.TableSysVerificationCodeService;
import com.extracme.saas.autocare.service.SmsService;

/**
 * 短信服务实现类
 */
@Service
public class SmsServiceImpl implements SmsService {
    
    private static final Logger logger = LoggerFactory.getLogger(SmsServiceImpl.class);
    private static final String LOGIN_TYPE = "LOGIN";
    private static final String MOBILE_PATTERN = "^1[3-9]\\d{9}$";
    
    @Value("${sms.code.expiration:300}") // 默认5分钟
    private int codeExpiration;
    
    @Value("${sms.send.interval:60}") // 默认60秒
    private int sendInterval;
    
    @Value("${sms.daily.limit.mobile:10}") // 默认每天每手机号10次
    private int dailyLimitMobile;
    
    @Value("${sms.daily.limit.ip:20}") // 默认每天每IP 20次
    private int dailyLimitIP;
    
    @Value("${spring.profiles.active:prod}")
    private String activeProfile;
    
    @Autowired
    private TableSysVerificationCodeService verificationCodeService;
    
    @Autowired
    private TableSmsSendRecordService smsSendRecordService;
    
    /**
     * 发送登录验证码
     *
     * @param mobile 手机号
     * @param ipAddress 请求IP地址
     * @return 发送结果：true-成功，false-失败
     * @throws IllegalArgumentException 手机号格式不正确、发送频率限制等异常
     */
    @Override
    @Transactional
    public boolean sendLoginVerificationCode(String mobile, String ipAddress) {
        // 1. 验证手机号格式
        if (!Pattern.matches(MOBILE_PATTERN, mobile)) {
            logger.warn("手机号格式不正确: {}", mobile);
            throw new IllegalArgumentException("手机号格式不正确");
        }
        
        // 2. 检查发送频率限制
        checkSendLimits(mobile, ipAddress);
        
        // 3. 生成验证码
        String code = generateVerificationCode();
        
        // 4. 保存验证码到数据库
        SysVerificationCode verificationCode = new SysVerificationCode();
        verificationCode.setMobile(mobile);
        verificationCode.setCode(code);
        verificationCode.setType(LOGIN_TYPE);
        // 设置过期时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.SECOND, codeExpiration);
        verificationCode.setExpireTime(calendar.getTime());
        verificationCode.setCreatedTime(new Date());
        
        verificationCodeService.insert(verificationCode).getId();
        
        // 5. 记录发送记录
        SmsSendRecord sendRecord = new SmsSendRecord();
        sendRecord.setPhoneNumber(mobile);
        sendRecord.setIpAddress(ipAddress);
        sendRecord.setType(LOGIN_TYPE);
        sendRecord.setCreateTime(new Date());
        smsSendRecordService.save(sendRecord);
        
        // 6. 调用短信发送接口（在实际环境中，这里需要集成三方短信服务）
        boolean sendResult = sendSms(mobile, code);
        
        logger.info("发送登录验证码，手机号：{}，IP：{}，结果：{}", mobile, ipAddress, sendResult ? "成功" : "失败");
        return sendResult;
    }
    
    /**
     * 验证验证码是否有效
     *
     * @param mobile 手机号
     * @param code 验证码
     * @param type 验证码类型：LOGIN-登录
     * @return 验证结果：true-验证通过，false-验证失败
     */
    @Override
    public boolean verifyCode(String mobile, String code, String type) {
        if (mobile == null || code == null || type == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        // 查询数据库中最新的有效验证码
        Optional<SysVerificationCode> optionalCode = verificationCodeService.findLatestUnused(mobile, type);
        if (!optionalCode.isPresent()) {
            logger.warn("未找到有效的验证码, 手机号: {}, 类型: {}", mobile, type);
            throw new IllegalArgumentException("验证码已失效");
        }
        
        SysVerificationCode verificationCode = optionalCode.get();
        
        // 检查验证码是否过期
        if (verificationCode.getExpireTime().before(new Date())) {
            logger.warn("验证码已过期, 手机号: {}, 类型: {}", mobile, type);
            throw new IllegalArgumentException("验证码已过期");
        }
        
        // 检查验证码错误次数
        if (verificationCode.getFailCount() != null && verificationCode.getFailCount() >= 2) {
            logger.warn("验证码错误次数过多, 手机号: {}, 类型: {}", mobile, type);
            verificationCodeService.incrementFailCount(verificationCode.getId());
            throw new IllegalArgumentException("验证码错误次数过多，请15分钟后重试");
        }
        
        // 检查验证码是否正确
        if (!code.equals(verificationCode.getCode())) {
            logger.warn("验证码不匹配, 手机号: {}, 类型: {}", mobile, type);
            // 增加失败次数
            verificationCodeService.incrementFailCount(verificationCode.getId());
            throw new IllegalArgumentException("验证码错误");
        }
        
        logger.info("验证码验证成功, 手机号: {}, 类型: {}", mobile, type);
        return true;
    }
    
    /**
     * 标记验证码为已使用
     *
     * @param mobile 手机号
     * @param code 验证码
     * @param type 验证码类型：LOGIN-登录
     * @return 操作结果：true-成功，false-失败
     */
    @Override
    @Transactional
    public boolean markCodeAsUsed(String mobile, String code, String type) {
        if (mobile == null || code == null || type == null) {
            return false;
        }
        
        // 查询数据库中最新的有效验证码
        Optional<SysVerificationCode> optionalCode = verificationCodeService.findLatestUnused(mobile, type);
        if (!optionalCode.isPresent()) {
            logger.warn("未找到有效的验证码, 手机号: {}, 类型: {}", mobile, type);
            return false;
        }
        
        SysVerificationCode verificationCode = optionalCode.get();
        
        // 检查验证码是否正确
        if (!code.equals(verificationCode.getCode())) {
            logger.warn("验证码不匹配, 手机号: {}, 类型: {}", mobile, type);
            return false;
        }
        
        // 标记为已使用
        boolean result = verificationCodeService.updateStatus(verificationCode.getId(), true);
        
        logger.info("标记验证码为已使用, 手机号: {}, 类型: {}, 结果: {}", mobile, type, result ? "成功" : "失败");
        return result;
    }
    
    /**
     * 清理过期验证码
     * 
     * @return 清理数量
     */
    @Override
    @Transactional
    public int cleanExpiredCodes() {
        // 标记过期的验证码
        Date now = new Date();
        int count = verificationCodeService.markExpiredCodes(now);
        
        // 删除旧验证码和发送记录（例如7天前的）
        verificationCodeService.deleteOldCodes(7);
        smsSendRecordService.deleteOldRecords(7);
        
        logger.info("清理过期验证码, 标记数量: {}", count);
        return count;
    }
    
    /**
     * 检查发送频率限制
     * 
     * @param mobile 手机号
     * @param ipAddress IP地址
     * @throws IllegalArgumentException 发送频率超限时抛出异常
     */
    private void checkSendLimits(String mobile, String ipAddress) {
        // 检查最后一次发送时间
        Date lastSendTime = smsSendRecordService.findLastSendTime(mobile, LOGIN_TYPE);
        if (lastSendTime != null) {
            long interval = (new Date().getTime() - lastSendTime.getTime()) / 1000;
            if (interval < sendInterval) {
                logger.warn("发送太频繁, 手机号: {}, IP: {}, 剩余时间: {}秒", mobile, ipAddress, sendInterval - interval);
                throw new IllegalArgumentException("请求过于频繁，请在" + (sendInterval - interval) + "秒后重试");
            }
        }
        
        // 计算今天的开始时间
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date todayStart = calendar.getTime();
        
        // 检查手机号当天发送次数
        int mobileSendCount = smsSendRecordService.countByPhoneAndTime(mobile, LOGIN_TYPE, todayStart);
        if (mobileSendCount >= dailyLimitMobile) {
            logger.warn("手机号当天发送次数超限, 手机号: {}, 次数: {}", mobile, mobileSendCount);
            throw new IllegalArgumentException("当天发送次数已达上限，请明天再试");
        }
        
        // 检查IP当天发送次数
        int ipSendCount = smsSendRecordService.countByIpAndTime(ipAddress, LOGIN_TYPE, todayStart);
        if (ipSendCount >= dailyLimitIP) {
            logger.warn("IP当天发送次数超限, IP: {}, 次数: {}", ipAddress, ipSendCount);
            throw new IllegalArgumentException("当前IP发送次数过多，请稍后再试");
        }
    }
    
    /**
     * 生成6位数字验证码
     * 在非生产环境下返回固定验证码123456
     * 在生产环境下返回随机6位数字
     * 
     * @return 验证码
     */
    private String generateVerificationCode() {
        if (!"prod".equals(activeProfile)) {
            return "123456";
        }
        Random random = new Random();
        int code = 100000 + random.nextInt(900000); // 生成6位随机数
        return String.valueOf(code);
    }
    
    /**
     * 发送短信（实际环境中需要对接第三方短信服务）
     * 
     * @param mobile 手机号
     * @param code 验证码
     * @return 发送结果
     */
    private boolean sendSms(String mobile, String code) {
        // 这里仅作为示例，实际实现需要调用第三方短信服务
        logger.info("模拟发送短信验证码, 手机号: {}, 验证码: {}", mobile, code);
        return true;
    }
} 