package com.extracme.saas.autocare.service;

import java.util.List;

import com.extracme.saas.autocare.model.dto.TokenDTO;
import com.extracme.saas.autocare.model.dto.repairTask.AccidentTaskDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskUpdateDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;

public interface InsurancePreReviewService {

    /**
     * 进保预审保存
     *
     * @param id                  任务id
     * @param repairTaskUpdateDTO 任务修改信息bo
     * @param tokenDTO
     */
    void saveRepairTask(String id, RepairTaskUpdateDTO repairTaskUpdateDTO, TokenDTO tokenDTO);

    /**
     * 获取事故任务信息列表
     *
     * @param vin     车架号
     * @param request request
     * @return 处理结果
     */
    List<AccidentTaskDTO> getAccidentTaskList(String vin);

    /**
     * 关联事故编号
     *
     * @param taskId     维修任务id
     * @param accidentNo 事故编号
     * @param request    request
     * @return 处理结果
     */
    void relateAccidentTask(Long taskId, String accidentNo);

    /**
     * 验证事故编号
     *
     * @param repairTask 维修任务信息
     * @param accidentNo 事故任务编号
     * @return 验证结果
     */
    void validateAccidentNo(MtcRepairTask repairTask, String accidentNo);
}
