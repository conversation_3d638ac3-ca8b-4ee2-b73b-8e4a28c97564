package com.extracme.saas.autocare.service.impl;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;
import com.extracme.saas.autocare.model.dto.ApprovalLevelsCreateDTO;
import com.extracme.saas.autocare.model.dto.ApprovalLevelsUpdateDTO;
import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import com.extracme.saas.autocare.model.entity.MtcApprovalLevels;
import com.extracme.saas.autocare.model.vo.ApprovalLevelsVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TableApprovalLevelsService;
import com.extracme.saas.autocare.service.ApprovalLevelsService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 审批层级服务实现类
 */
@Slf4j
@Service
public class ApprovalLevelsServiceImpl implements ApprovalLevelsService {

    @Autowired
    private TableApprovalLevelsService tableApprovalLevelsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createApprovalLevels(ApprovalLevelsCreateDTO createDTO) {
        // 获取当前登录用户信息
        String currentUser = SessionUtils.getUsername();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }

        // 获取当前租户ID
        Long tenantId = SessionUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException("未获取到租户信息");
        }

        try {
            // 设置租户上下文
            TenantContextHolder.setTenant(tenantId);
            log.debug("创建审批层级 - 设置租户上下文，租户ID: {}", tenantId);

            // 校验审批层级是否已存在
            if (tableApprovalLevelsService.existsByApprovalLevel(createDTO.getApprovalLevel())) {
                throw new BusinessException("该审批层级已存在");
            }

            // 创建实体对象
            MtcApprovalLevels entity = new MtcApprovalLevels();
            BeanUtils.copyProperties(createDTO, entity);

            // 使用统一的insert方法处理审计字段
            tableApprovalLevelsService.insert(entity, currentUser);

            log.debug("成功创建审批层级 - 租户ID: {}, 审批层级: {}", tenantId, createDTO.getApprovalLevel());
        } finally {
            // 清理租户上下文
            TenantContextHolder.clear();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalLevels(ApprovalLevelsUpdateDTO updateDTO) {
        // 获取当前登录用户信息
        String currentUser = SessionUtils.getUsername();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }

        // 获取当前租户ID
        Long tenantId = SessionUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException("未获取到租户信息");
        }

        try {
            // 设置租户上下文
            TenantContextHolder.setTenant(tenantId);
            log.debug("更新审批层级 - 设置租户上下文，租户ID: {}", tenantId);

            // 检查记录是否存在
            MtcApprovalLevels existing = tableApprovalLevelsService.selectById(updateDTO.getId());
            if (existing == null) {
                throw new BusinessException("审批层级记录不存在");
            }

            // 校验审批层级是否已存在（排除当前记录）
            if (tableApprovalLevelsService.existsByApprovalLevelExcludeId(updateDTO.getApprovalLevel(),
                    updateDTO.getId())) {
                throw new BusinessException("该审批层级已存在");
            }

            // 更新实体对象
            BeanUtils.copyProperties(updateDTO, existing);
            existing.setUpdateBy(currentUser);

            // 使用updateSelectiveById方法更新
            tableApprovalLevelsService.updateSelectiveById(existing);

            log.debug("成功更新审批层级 - 租户ID: {}, 记录ID: {}, 审批层级: {}", tenantId, updateDTO.getId(),
                    updateDTO.getApprovalLevel());
        } finally {
            // 清理租户上下文
            TenantContextHolder.clear();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteApprovalLevels(Long id) {
        // 检查记录是否存在
        MtcApprovalLevels existing = tableApprovalLevelsService.selectById(id);
        if (existing == null) {
            throw new BusinessException("审批层级记录不存在");
        }

        // 物理删除
        tableApprovalLevelsService.deleteById(id);
    }

    @Override
    public BasePageVO<ApprovalLevelsVO> getApprovalLevelsList(BasePageDTO pageDTO) {
        // 开启分页
        PageHelper.startPage(pageDTO.getPageNum(), pageDTO.getPageSize());

        // 查询所有审批层级数据，按ID降序排序
        List<MtcApprovalLevels> entities = tableApprovalLevelsService.findAll();

        // 获取分页信息
        PageInfo<MtcApprovalLevels> pageInfo = new PageInfo<>(entities);

        // 转换为VO
        List<ApprovalLevelsVO> voList = entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return BasePageVO.of(voList, pageInfo);
    }

    @Override
    public List<ComboVO<Long>> getApprovalLevelsCombo(Long tenantId) {
        // 如果没有传入租户ID，使用当前登录用户的租户ID
        if (tenantId == null) {
            tenantId = SessionUtils.getTenantId();
        }

        if (tenantId == null) {
            return Collections.emptyList();
        }

        return tableApprovalLevelsService.findComboByTenant(tenantId);
    }

    /**
     * 将实体转换为VO
     *
     * @param entity 实体对象
     * @return VO对象
     */
    private ApprovalLevelsVO convertToVO(MtcApprovalLevels entity) {
        ApprovalLevelsVO vo = new ApprovalLevelsVO();
        BeanUtils.copyProperties(entity, vo);

        // 设置审批层级名称
        vo.setApprovalLevelName(getApprovalLevelName(entity.getApprovalLevel()));

        return vo;
    }

    /**
     * 获取审批层级名称
     *
     * @param approvalLevel 审批层级
     * @return 审批层级名称
     */
    private String getApprovalLevelName(Integer approvalLevel) {
        if (approvalLevel == null) {
            return "未知";
        }
        switch (approvalLevel) {
            case 0:
                return "无审批";
            case 1:
                return "一级审批";
            case 2:
                return "二级审批";
            case 3:
                return "三级审批";
            case 4:
                return "四级审批";
            default:
                return "未知";
        }
    }

    @Override
    public ApprovalLevelsVO getApprovalLevelsByLevel(Integer approvalLevel) {
        // 处理参数为null的情况
        if (approvalLevel == null) {
            log.warn("查询审批层级信息 - 审批层级级别参数为null");
            return null;
        }

        // 获取当前租户ID
        Long tenantId = SessionUtils.getTenantId();
        if (tenantId == null) {
            log.warn("查询审批层级信息 - 未获取到租户信息");
            return null;
        }

        log.debug("查询审批层级信息 - 设置租户上下文，租户ID: {}, 审批层级: {}", tenantId, approvalLevel);

        // 查询所有审批层级数据
        List<MtcApprovalLevels> allLevels = tableApprovalLevelsService.findAll();

        // 筛选出与入参approvalLevel匹配的审批层级记录
        MtcApprovalLevels currentLevel = allLevels.stream()
                .filter(level -> approvalLevel.equals(level.getApprovalLevel()))
                .findFirst()
                .orElse(null);

        // 如果没有找到记录，返回null
        if (currentLevel == null) {
            log.debug("查询审批层级信息 - 未找到审批层级记录，审批层级: {}", approvalLevel);
            return null;
        }

        // 转换为VO对象
        ApprovalLevelsVO vo = convertToVO(currentLevel);

        // 判断是否存在下一级审批层级
        boolean hasNextLevel = allLevels.stream()
                .anyMatch(level -> level.getApprovalLevel() != null &&
                        level.getApprovalLevel().equals(approvalLevel + 1));

        // 设置是否有下一级审批层级
        vo.setHasNextLevel(hasNextLevel);

        log.debug("查询审批层级信息 - 成功查询到审批层级记录，ID: {}, 是否有下一级: {}",
                vo.getId(), vo.getHasNextLevel());
        return vo;
    }
}
