package com.extracme.saas.autocare.service.impl;

import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.VehicleRepairActivityUpdateDTO;
import com.extracme.saas.autocare.model.dto.VehicleRepairRecordQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskUpdateDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowProcessDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.model.vo.VehicleRepairRecordVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.service.RepairTaskService;
import com.extracme.saas.autocare.service.VehicleRepairActivityService;
import com.extracme.saas.autocare.service.WorkflowService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * 车辆维修活动节点服务实现类
 */
@Slf4j
@Service
public class VehicleRepairActivityServiceImpl implements VehicleRepairActivityService {

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private RepairTaskService repairTaskService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeVehicleCheck(VehicleRepairActivityUpdateDTO updateDTO) {
        log.info("车辆维修申请验收，参数: {}", updateDTO);
        try {
            // 1. 查询维修任务
            MtcRepairTask repairTaskView = tableRepairTaskService.selectByTaskNo(updateDTO.getTaskNo());
            if (repairTaskView == null) {
                throw new BusinessException("未找到维修任务：" + updateDTO.getTaskNo());
            }

            // 2. 获取工作流实例
            WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(updateDTO.getTaskNo());
            if (workflowInstance == null) {
                throw new BusinessException("未找到工作流实例：" + updateDTO.getTaskNo());
            }

            // 3. 构建维修任务更新DTO
            RepairTaskUpdateDTO updateTaskDTO = new RepairTaskUpdateDTO();
            BeanUtils.copyProperties(updateDTO, updateTaskDTO);

            // 4. 检查任务是否超时
            // String overTime = tableRepairTaskService.getOverTime(repairTaskView.getId());
            // updateTaskDTO.setOverTime(overTime);

            // 5. 保存维修任务
            repairTaskService.saveRepairTask(repairTaskView.getId(), updateTaskDTO);

            // 6. 处理图片和视频文件
            // TODO: 需要根据VehicleRepairActivityUpdateDTO的实际字段来处理图片和视频
            // if (updateDTO.getMediaTypeMap() != null && !updateDTO.getMediaTypeMap().isEmpty()) {
            //     String operatorName = SessionUtils.getUsername();
            //     repairTaskService.processMediaFiles(updateDTO.getTaskNo(), updateDTO.getMediaTypeMap(), operatorName);
            // }

            // 7. 调用工作流服务处理节点
            WorkflowProcessDTO processDTO = new WorkflowProcessDTO();
            processDTO.setTriggerEvent("APPLY_INSPECTION");
            processDTO.setActivityCode(workflowInstance.getCurrentActivityCode());
            processDTO.setOperator(SessionUtils.getUsername());

            workflowService.processNode(workflowInstance.getId(), processDTO);

            log.info("车辆维修申请验收成功，任务编号: {}", updateDTO.getTaskNo());
        } catch (BusinessException e) {
            log.error("车辆维修申请验收失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("车辆维修申请验收失败: {}", e.getMessage(), e);
            throw new BusinessException("车辆维修申请验收失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public BasePageVO<VehicleRepairRecordVO> queryVehicleRepairRecord(VehicleRepairRecordQueryDTO queryDTO) {
        log.info("查询车辆历次维修记录开始，查询条件：{}", queryDTO);

        // 使用PageHelper进行分页
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // TODO: 实现查询车辆历次维修记录逻辑
        // 1. 根据查询条件查询维修任务列表
        // 2. 转换为视图对象列表

        // 临时返回空列表
        List<VehicleRepairRecordVO> resultList = java.util.Collections.emptyList();
        PageInfo<VehicleRepairRecordVO> pageInfo = new PageInfo<>(resultList);

        log.info("查询车辆历次维修记录完成");
        return BasePageVO.of(resultList, pageInfo);
    }
}
