package com.extracme.saas.autocare.service.impl;

import com.extracme.saas.autocare.enums.ErrorCode;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.vo.PermissionTreeVO;
import com.extracme.saas.autocare.repository.TablePermissionService;
import com.extracme.saas.autocare.service.PermissionService;
import com.extracme.saas.autocare.util.SessionUtils;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 权限服务实现类
 */
@Service
@RequiredArgsConstructor
public class PermissionServiceImpl implements PermissionService {

    private final TablePermissionService permissionRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPermission(SysPermission permission) {
        // 检查权限编码是否已存在
        if (permissionRepository.existsByCode(permission.getPermissionCode())) {
            throw new BusinessException(ErrorCode.PERMISSION_CODE_EXISTS);
        }
        // 使用当前登录用户作为操作人
        String operator = SessionUtils.getUsername();
        // 保存权限
        return permissionRepository.insert(permission, operator).getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePermission(SysPermission permission) {
        // 检查权限是否存在
        SysPermission existingPermission = getPermission(permission.getId());
        if (existingPermission == null) {
            throw new BusinessException(ErrorCode.PERMISSION_NOT_FOUND);
        }

        // 如果修改了权限编码，需要检查是否重复
        if (!existingPermission.getPermissionCode().equals(permission.getPermissionCode())
            && permissionRepository.existsByCode(permission.getPermissionCode())) {
            throw new BusinessException(ErrorCode.PERMISSION_CODE_EXISTS);
        }

        // 使用当前登录用户作为操作人
        String operator = SessionUtils.getUsername();
        // 更新权限
        permissionRepository.updateSelectiveById(permission, operator);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePermission(Long id) {
        // 检查权限是否存在
        SysPermission permission = getPermission(id);
        if (permission == null) {
            throw new BusinessException(ErrorCode.PERMISSION_NOT_FOUND);
        }

        // 检查权限是否被角色使用
        if (permissionRepository.isUsedByRoles(id)) {
            throw new BusinessException(ErrorCode.PERMISSION_IN_USE);
        }

        // 物理删除权限
        permissionRepository.deleteById(id);
    }

    @Override
    public SysPermission getPermission(Long id) {
        return permissionRepository.selectById(id);
    }

    @Override
    public List<SysPermission> getPermissionList(Integer type) {
        return permissionRepository.findByType(type);
    }

    @Override
    public List<SysPermission> getUserPermissions(Long userId) {
        return permissionRepository.findByUserId(userId);
    }

    @Override
    public List<SysPermission> getRolePermissions(Long roleId) {
        return permissionRepository.findByRoleId(roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignRolePermissions(Long roleId, List<Long> permissionIds) {
        permissionRepository.assignRolePermissions(roleId, permissionIds);
    }

    @Override
    public boolean hasPermission(Long userId, String permissionCode) {
        return permissionRepository.hasPermission(userId, permissionCode);
    }

    @Override
    public List<PermissionTreeVO> getPermissionTree() {
        // 获取所有权限
        List<SysPermission> allPermissions = permissionRepository.findAll();

        // 按父ID分组
        Map<Long, List<SysPermission>> permissionMap = allPermissions.stream()
                .collect(Collectors.groupingBy(p -> p.getParentId() == null ? 0L : p.getParentId()));

        // 构建树形结构（从根节点开始）
        return buildPermissionTree(permissionMap, 0L);
    }

    /**
     * 递归构建权限树
     *
     * @param permissionMap 按父ID分组的权限Map
     * @param parentId 父ID
     * @return 权限树节点列表
     */
    private List<PermissionTreeVO> buildPermissionTree(Map<Long, List<SysPermission>> permissionMap, Long parentId) {
        List<PermissionTreeVO> result = new ArrayList<>();

        // 获取当前父ID下的所有权限
        List<SysPermission> permissions = permissionMap.get(parentId);
        if (permissions == null || permissions.isEmpty()) {
            return result;
        }

        // 排序
        permissions.sort(Comparator.comparing(SysPermission::getSort, Comparator.nullsLast(Comparator.naturalOrder())));

        // 转换为VO并递归构建子节点
        for (SysPermission permission : permissions) {
            PermissionTreeVO node = new PermissionTreeVO();

            // 复制基本属性
            node.setId(permission.getId());
            node.setPermissionName(permission.getPermissionName());
            node.setPermissionCode(permission.getPermissionCode());
            node.setPermissionType(permission.getPermissionType());
            node.setParentId(permission.getParentId());
            node.setPath(permission.getPath());
            node.setComponent(permission.getComponent());
            node.setIcon(permission.getIcon());
            node.setSort(permission.getSort());
            node.setStatus(permission.getStatus());

            // 递归构建子节点
            node.setChildren(buildPermissionTree(permissionMap, permission.getId()));

            result.add(node);
        }

        return result;
    }
}