package com.extracme.saas.autocare.service.impl;

import java.math.BigDecimal;
import java.util.Date;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.enums.ActivityDefinitionEnum;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.LossAssessmentApproveDTO;
import com.extracme.saas.autocare.model.dto.LossAssessmentDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskUpdateDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowProcessDTO;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.model.vo.ApprovalLevelsVO;
import com.extracme.saas.autocare.repository.TableOperatorLogService;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.service.ApprovalLevelsService;
import com.extracme.saas.autocare.service.LossAssessmentService;
import com.extracme.saas.autocare.service.RepairTaskService;
import com.extracme.saas.autocare.service.WorkflowService;
import com.extracme.saas.autocare.util.SessionUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 核损核价服务实现类
 */
@Slf4j
@Service
public class LossAssessmentServiceImpl implements LossAssessmentService {

    @Autowired
    private RepairTaskService repairTaskService;

    @Autowired
    private TableRepairTaskService tableRepairTaskService;

    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private TableOperatorLogService tableOperatorLogService;

    @Autowired
    private ApprovalLevelsService approvalLevelsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLossAssessment(LossAssessmentDTO lossAssessmentDTO) {
        log.info("保存核损核价信息: {}", lossAssessmentDTO);

        // 获取维修任务
        MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(lossAssessmentDTO.getTaskNo());
        if (mtcRepairTask == null) {
            throw new BusinessException("未找到维修任务: " + lossAssessmentDTO.getTaskNo());
        }

        // 更新维修任务核损核价信息
        RepairTaskUpdateDTO updateDTO = new RepairTaskUpdateDTO();
        BeanUtils.copyProperties(lossAssessmentDTO, updateDTO);
        // 保存维修任务
        repairTaskService.saveRepairTask(mtcRepairTask.getId(), updateDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveLossAssessment(LossAssessmentApproveDTO approveDTO) {
        log.info("审核完成核损核价: {}", approveDTO);
        // 获取登录用户
        LoginUser loginUser = SessionUtils.getLoginUser();

        // 1. 获取维修任务
        MtcRepairTask mtcRepairTask = tableRepairTaskService.selectByTaskNo(approveDTO.getTaskNo());
        if (mtcRepairTask == null) {
            throw new BusinessException("未找到维修任务: " + approveDTO.getTaskNo());
        }

        // 2. 获取工作流实例
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(approveDTO.getTaskNo());
        if (workflowInstance == null) {
            throw new BusinessException("未找到工作流实例: " + approveDTO.getTaskNo());
        }

        // 3. 保存核损核价信息
        LossAssessmentDTO lossAssessmentDTO = new LossAssessmentDTO();
        BeanUtils.copyProperties(approveDTO, lossAssessmentDTO);
        saveLossAssessment(lossAssessmentDTO);

        // 3. 验证当前工作流状态是否允许核损核价审核通过
        validateWorkflowStatus(workflowInstance);

        // 4. 验证用户核损核价级别
        Integer lossAssessmentLevel = validateUserLossAssessmentLevel(loginUser, mtcRepairTask);

        // 5. 验证定损数据
        validateLossData(approveDTO.getTaskNo());

        // 6. 验证核价项目是否全部通过
        validateRepairItemCheckStatus(approveDTO.getTaskNo());

        // 7. 验证复勘部位
        validateResurveyPart(approveDTO);

        // 8. 验证价格异议状态
        validatePriceObjection(approveDTO);

        // 9. 验证核损金额
        validateLossAmount(mtcRepairTask, approveDTO, lossAssessmentLevel);

        // 获取下一审核级别配置
        ApprovalLevelsVO approvalLevelsVO = approvalLevelsService.getApprovalLevelsByLevel(lossAssessmentLevel);
        if (null == approvalLevelsVO || approvalLevelsVO.getSelfApprovalAmount() == null
                || approvalLevelsVO.getHasNextLevel() == null) {
            throw new BusinessException("未找到核损核价级别配置");
        }

        // 存在下一级配置且核损金额大于等于配置金额 需进入下一级审核
        boolean intoNextStageAudit = approvalLevelsVO.getHasNextLevel()
                && mtcRepairTask.getVehicleInsuranceTotalAmount()
                        .compareTo(approvalLevelsVO.getSelfApprovalAmount()) >= 0;
        if (intoNextStageAudit) {
            // 更新维修任务核损核价级别
            RepairTaskUpdateDTO repairTaskUpdateDTO = new RepairTaskUpdateDTO();
            repairTaskUpdateDTO.setAdvancedAuditLeve(lossAssessmentLevel + 1);
            repairTaskService.saveRepairTask(mtcRepairTask.getId(), repairTaskUpdateDTO);
        } else {
            // 调用工作流服务处理节点
            WorkflowProcessDTO processDTO = new WorkflowProcessDTO();
            processDTO.setTriggerEvent("APPROVE_QUOTE");
            processDTO.setActivityCode(workflowInstance.getCurrentActivityCode());
            processDTO.setOperator(loginUser.getUsername());

            try {
                workflowService.processNode(workflowInstance.getId(), processDTO);
                log.info("审核完成核损核价成功, 任务编号: {}", approveDTO.getTaskNo());

                // 15. 记录操作日志
                recordOperationLog(mtcRepairTask, loginUser, approveDTO);
            } catch (Exception e) {
                log.error("审核完成核损核价失败: {}", e.getMessage(), e);
                throw new BusinessException("审核完成核损核价失败: " + e.getMessage());
            }
        }
    }

    /**
     * 验证工作流状态
     * 
     * @param workflowInstance 工作流实例
     */
    private void validateWorkflowStatus(WorkflowInstance workflowInstance) {
        // 验证当前节点是否为核损核价节点
        if (!"LOSS_ASSESSMENT".equals(workflowInstance.getCurrentActivityCode())) {
            throw new BusinessException("当前工作流节点不是核损核价节点");
        }

        // 验证当前状态是否允许审核通过
        if (!"PROCESSING".equals(workflowInstance.getStatusCode())) {
            throw new BusinessException("当前状态不允许审核通过");
        }
    }

    /**
     * 验证核损金额
     * 
     * @param mtcRepairTask       维修任务
     * @param approveDTO          审核参数
     * @param lossAssessmentLevel 用户核损核价级别
     */
    private void validateLossAmount(MtcRepairTask mtcRepairTask, LossAssessmentApproveDTO approveDTO,
            Integer lossAssessmentLevel) {
        // 如果是事故维修任务且不是转自费的情况
        if (mtcRepairTask.getRepairTypeId() != null &&
                mtcRepairTask.getRepairTypeId() == 1 &&
                (mtcRepairTask.getReviewToSelFeeFlag() == null || mtcRepairTask.getReviewToSelFeeFlag() == 0)) {

            // 检查是否有预审金额
            if (mtcRepairTask.getRepairReviewTotalAmount() != null) {
                // 核损金额超过预审金额130%的检查
                BigDecimal maxAllowedAmount = mtcRepairTask.getRepairReviewTotalAmount()
                        .multiply(new BigDecimal("1.3"));

                if (approveDTO.getRepairInsuranceTotalAmount() != null &&
                        maxAllowedAmount.compareTo(approveDTO.getRepairInsuranceTotalAmount()) < 0) {

                    // 根据核损核价级别判断是否允许超额
                    // 一级核损核价人员不允许超额
                    if (lossAssessmentLevel == 1) {
                        throw new BusinessException("核损金额超过预审金额130%，不可审核通过");
                    }

                    // 检查是否允许超额审核通过（针对高级别核损核价人员）
                    if (approveDTO.getCheckFlag() == null || approveDTO.getCheckFlag() == 0) {
                        throw new BusinessException("核损金额超过预审金额130%，请确认是否继续审核通过");
                    }
                }
            }
        }
    }

    /**
     * 记录操作日志
     * 
     * @param mtcRepairTask 维修任务
     * @param loginUser     登录用户
     * @param approveDTO    审核参数
     */
    private void recordOperationLog(MtcRepairTask mtcRepairTask, LoginUser loginUser,
            LossAssessmentApproveDTO approveDTO) {
        MtcOperatorLog operatorLog = new MtcOperatorLog();
        operatorLog.setRecordId(mtcRepairTask.getId());
        operatorLog.setTableName("mtc_repair_task");
        operatorLog.setOpeContent("审核通过核损核价，金额：" + approveDTO.getRepairInsuranceTotalAmount());
        operatorLog.setCreateBy(loginUser.getUsername());
        operatorLog.setCreatedTime(new Date());
        operatorLog.setUpdateBy(loginUser.getUsername());
        operatorLog.setUpdatedTime(new Date());
        operatorLog.setRemark(approveDTO.getApprovalRemark());

        tableOperatorLogService.insertSelective(operatorLog);

        log.info("用户[{}]审核通过了维修任务[{}]的核损核价，金额：{}，备注：{}",
                loginUser.getUsername(),
                mtcRepairTask.getTaskNo(),
                approveDTO.getRepairInsuranceTotalAmount(),
                approveDTO.getApprovalRemark());
    }

    /**
     * 核损核价-平级移交
     * 将当前处理中的核损核价任务移交给其他人处理
     *
     * @param approveDTO 审核参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTransferTask(String taskNo) {
        log.info("核损核价-平级移交: {}", taskNo);

        // 获取登录用户
        LoginUser loginUser = SessionUtils.getLoginUser();

        // 1. 获取维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(taskNo);
        if (repairTask == null) {
            throw new BusinessException("未找到维修任务: " + taskNo);
        }

        // 2. 校验任务状态
        // 取得任务状态详情
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(taskNo);
        if (workflowInstance == null || !ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode()
                .equals(workflowInstance.getCurrentActivityCode())) {
            throw new BusinessException("任务已经不处于核损核价环节，请刷新页面重试");
        }

        // 检查当前环节是否处于核损核价环节
        if (!ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode().equals(workflowInstance.getCurrentActivityCode())) {
            throw new BusinessException("任务已经不处于核损核价环节，请刷新页面重试");
        }

        // 3. 校验任务占有人
        if (!loginUser.getUser().getId().equals(repairTask.getVerificationLossTaskOperId())) {
            throw new BusinessException("你没有占据此任务，不可移交");
        }

        // 4. 调用工作流服务处理节点
        WorkflowProcessDTO processDTO = new WorkflowProcessDTO();
        processDTO.setTriggerEvent("CLEAR_LOSS_ASSESSMENT_OWNER");
        processDTO.setActivityCode(workflowInstance.getCurrentActivityCode());
        processDTO.setOperator(loginUser.getUsername());
        processDTO.setRemarks("平级移交");

        log.info("核损核价-平级移交成功: {}", taskNo);
    }

    /**
     * 核损核价-清除任务占据人
     * 管理员可以清除任务占据人，使任务可以被其他人处理
     *
     * @param approveDTO 审核参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void clearOwner(String taskNo) {
        log.info("核损核价-清除任务占据人: {}", taskNo);

        // 获取登录用户
        LoginUser loginUser = SessionUtils.getLoginUser();

        // 1. 获取维修任务
        MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(taskNo);
        if (repairTask == null) {
            throw new BusinessException("未找到维修任务: " + taskNo);
        }

        // 2. 校验任务状态
        // 取得任务状态详情
        WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(taskNo);
        if (workflowInstance == null || !ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode()
                .equals(workflowInstance.getCurrentActivityCode())) {
            throw new BusinessException("任务已经不处于核损核价环节，请刷新页面重试");
        }

        // 检查当前环节是否处于核损核价环节
        if (!ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode().equals(workflowInstance.getCurrentActivityCode())) {
            throw new BusinessException("任务已经不处于核损核价环节，请刷新页面重试");
        }

        // 3. 调用工作流服务处理节点
        WorkflowProcessDTO processDTO = new WorkflowProcessDTO();
        processDTO.setTriggerEvent("CLEAR_LOSS_ASSESSMENT_OWNER");
        processDTO.setActivityCode(workflowInstance.getCurrentActivityCode());
        processDTO.setOperator(loginUser.getUsername());
        processDTO.setRemarks("清除任务占据人");
        workflowService.processNode(workflowInstance.getId(), processDTO);

        log.info("核损核价-清除任务占据人成功: {}", taskNo);
    }

    /**
     * 验证定损数据
     * 
     * @param taskNo 任务编号
     */
    private void validateLossData(String taskNo) {
        // 检查是否存在定损数据
        // 这里需要根据新项目的实际情况实现定损数据的查询逻辑
        boolean hasLossData = false; // 示例，需要替换为实际查询逻辑

        if (!hasLossData) {
            throw new BusinessException("未经过定损系统维修报价，请退回后重新定损");
        }
    }

    /**
     * 验证核价项目是否全部通过
     * 
     * @param taskNo 任务编号
     */
    private void validateRepairItemCheckStatus(String taskNo) {
        // 检查核价项目是否全部通过
        // 这里需要根据新项目的实际情况实现核价项目状态的查询逻辑
        boolean allItemsApproved = true; // 示例，需要替换为实际查询逻辑

        if (!allItemsApproved) {
            throw new BusinessException("核价项目未全部通过，不可审核通过");
        }
    }

    /**
     * 验证复勘部位
     * 
     * @param approveDTO 审核参数
     */
    private void validateResurveyPart(LossAssessmentApproveDTO approveDTO) {
        // 选择"是"，则复勘部位必填
        if (approveDTO.getResurveyFlag() != null && "1".equals(approveDTO.getResurveyFlag())) {
            if (approveDTO.getResurveyPart() == null || approveDTO.getResurveyPart().trim().isEmpty()) {
                throw new BusinessException("复勘部位不能为空");
            }
        }
    }

    /**
     * 验证价格异议状态
     * 
     * @param approveDTO 审核参数
     */
    private void validatePriceObjection(LossAssessmentApproveDTO approveDTO) {
        // 事故维修 价格异议
        if (approveDTO.getVehicleManageViewFlag() != null && approveDTO.getVehicleManageViewFlag() == 1) {
            throw new BusinessException("核价异议,不可审核通过");
        }
    }

    /**
     * 验证用户核损核价级别
     * 
     * @param loginUser     登录用户
     * @param mtcRepairTask 维修任务
     * @return 用户核损核价级别
     */
    private Integer validateUserLossAssessmentLevel(LoginUser loginUser, MtcRepairTask mtcRepairTask) {
        // 获取用户信息
        String username = loginUser.getUsername();

        // 获取用户核损核价级别
        Integer lossAssessmentLevel = loginUser.getApprovalLevel();

        // 如果用户没有核损核价权限
        if (lossAssessmentLevel == null) {
            throw new BusinessException("您没有核损核价权限，不可进行核损核价审核");
        }

        // 验证用户核损核价级别与任务要求的级别是否匹配
        Integer requiredLevel = mtcRepairTask.getAdvancedAuditLeve();
        if (requiredLevel != null && !requiredLevel.equals(lossAssessmentLevel)) {
            throw new BusinessException("该任务不在当前审核级别，请更换相应权限账号");
        }

        log.info("用户[{}]核损核价级别: {}, 任务要求级别: {}", username, lossAssessmentLevel, requiredLevel);

        return lossAssessmentLevel;
    }

}
