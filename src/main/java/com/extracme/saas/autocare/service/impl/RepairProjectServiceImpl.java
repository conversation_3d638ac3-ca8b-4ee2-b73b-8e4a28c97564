package com.extracme.saas.autocare.service.impl;

import com.extracme.saas.autocare.model.dto.RepairProjectCreateDTO;
import com.extracme.saas.autocare.model.dto.RepairProjectQueryDTO;
import com.extracme.saas.autocare.model.dto.RepairProjectUpdateDTO;
import com.extracme.saas.autocare.model.dto.RepairProjectUpdateStatusDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairItem;
import com.extracme.saas.autocare.model.vo.RepairProjectDetailsVO;
import com.extracme.saas.autocare.model.vo.RepairProjectListVO;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.repository.TableRepairProjectService;
import com.extracme.saas.autocare.service.RepairProjectService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 修理项目服务实现类
 */
@Slf4j
@Service
public class RepairProjectServiceImpl implements RepairProjectService {

    @Autowired
    private TableRepairProjectService tableRepairProjectService;

    @Override
    public BasePageVO<RepairProjectListVO> queryRepairProjectList(RepairProjectQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<MtcRepairItem> list = tableRepairProjectService.queryRepairItemList(queryDTO);
        PageInfo<MtcRepairItem> pageInfo = new PageInfo<>(list);
        List<RepairProjectListVO> voList = list.stream().map(item -> {
            RepairProjectListVO vo = new RepairProjectListVO();
            BeanUtils.copyProperties(item, vo);
            vo.setGroupingName("零件分组名称");
            vo.setOrgName("车辆运营单位名称");
            return vo;
        }).collect(Collectors.toList());
        return BasePageVO.of(voList, pageInfo);
    }

    @Override
    public RepairProjectDetailsVO getRepairProjectDetails(Long id) {
        MtcRepairItem mtcRepairItem = tableRepairProjectService.selectById(id);
        if (mtcRepairItem == null) {
            return null;
        }
        RepairProjectDetailsVO replacePartItemDetailsVO = new RepairProjectDetailsVO();
        BeanUtils.copyProperties(mtcRepairItem, replacePartItemDetailsVO);
        return replacePartItemDetailsVO;
    }

    @Override
    public void createRepairProject(RepairProjectCreateDTO createDTO) {
        MtcRepairItem repairItems = tableRepairProjectService.selectByRepairName(createDTO.getRepairName());
        if (repairItems != null) {
            throw new RuntimeException("该修理名称已存在");
        }
        MtcRepairItem mtcRepairItem = new MtcRepairItem();
        BeanUtils.copyProperties(createDTO, mtcRepairItem);

        //TODO 获取用户对应的组织公司信息
        mtcRepairItem.setOrgId("000T");
        tableRepairProjectService.insert(mtcRepairItem);
        //TODO 添加日志
    }

    @Override
    public void updateRepairProject(RepairProjectUpdateDTO updateDTO) {
        MtcRepairItem repairItem = tableRepairProjectService.selectById(updateDTO.getId());
        if (repairItem == null) {
            throw new RuntimeException("该记录不存在");
        }
        if (!StringUtils.equals(updateDTO.getRepairName(), repairItem.getRepairName())) {
            MtcRepairItem repairItems = tableRepairProjectService.selectByRepairName(updateDTO.getRepairName());
            if (repairItems != null) {
                throw new RuntimeException("该修理名称已存在");
            }
        }
        BeanUtils.copyProperties(updateDTO, repairItem);
        tableRepairProjectService.updateSelectiveById(repairItem);
    }

    @Override
    public void deleteRepairProject(Long id) {
        tableRepairProjectService.deleteById(id);
    }

    @Override
    public void updateRepairProjectStatus(RepairProjectUpdateStatusDTO updateStatusDTO) {
        MtcRepairItem repairItem = tableRepairProjectService.selectById(updateStatusDTO.getId());
        if (repairItem == null) {
            throw new RuntimeException("该记录不存在");
        }
        repairItem.setStatus(updateStatusDTO.getItemStatus());
        tableRepairProjectService.updateSelectiveById(repairItem);
    }
}