package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcOrgInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_org_info")
    public static final MtcOrgInfo mtcOrgInfo = new MtcOrgInfo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_org_info.id")
    public static final SqlColumn<Long> id = mtcOrgInfo.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_org_info.org_id")
    public static final SqlColumn<String> orgId = mtcOrgInfo.orgId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_org_info.org_name")
    public static final SqlColumn<String> orgName = mtcOrgInfo.orgName;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_org_info.create_time")
    public static final SqlColumn<Date> createTime = mtcOrgInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_org_info.create_by")
    public static final SqlColumn<String> createBy = mtcOrgInfo.createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_org_info.update_time")
    public static final SqlColumn<Date> updateTime = mtcOrgInfo.updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_org_info.update_by")
    public static final SqlColumn<String> updateBy = mtcOrgInfo.updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_org_info")
    public static final class MtcOrgInfo extends AliasableSqlTable<MtcOrgInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> orgId = column("org_id", JDBCType.VARCHAR);

        public final SqlColumn<String> orgName = column("org_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public MtcOrgInfo() {
            super("mtc_org_info", MtcOrgInfo::new);
        }
    }
}