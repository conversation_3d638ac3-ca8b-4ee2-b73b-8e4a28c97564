package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class WorkflowTemplateDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    public static final WorkflowTemplate workflowTemplate = new WorkflowTemplate();

    /**
     * Database Column Remarks:
     *   流程模板编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.id")
    public static final SqlColumn<Long> id = workflowTemplate.id;

    /**
     * Database Column Remarks:
     *   流程模板名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.workflow_name")
    public static final SqlColumn<String> workflowName = workflowTemplate.workflowName;

    /**
     * Database Column Remarks:
     *   任务类型
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.task_type")
    public static final SqlColumn<Integer> taskType = workflowTemplate.taskType;

    /**
     * Database Column Remarks:
     *   修理厂类型：-1表示全部，1表示合作，2表示非合作
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.repair_factory_type")
    public static final SqlColumn<Integer> repairFactoryType = workflowTemplate.repairFactoryType;

    /**
     * Database Column Remarks:
     *   产品线
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.sub_product_line")
    public static final SqlColumn<Integer> subProductLine = workflowTemplate.subProductLine;

    /**
     * Database Column Remarks:
     *   是否启用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.is_active")
    public static final SqlColumn<Integer> isActive = workflowTemplate.isActive;

    /**
     * Database Column Remarks:
     *   租户编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.tenant_id")
    public static final SqlColumn<Integer> tenantId = workflowTemplate.tenantId;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.create_time")
    public static final SqlColumn<Date> createTime = workflowTemplate.createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.create_by")
    public static final SqlColumn<String> createBy = workflowTemplate.createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.update_time")
    public static final SqlColumn<Date> updateTime = workflowTemplate.updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.update_by")
    public static final SqlColumn<String> updateBy = workflowTemplate.updateBy;

    /**
     * Database Column Remarks:
     *   流程描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: workflow_template.description")
    public static final SqlColumn<String> description = workflowTemplate.description;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    public static final class WorkflowTemplate extends AliasableSqlTable<WorkflowTemplate> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> workflowName = column("workflow_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> taskType = column("task_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> repairFactoryType = column("repair_factory_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> subProductLine = column("sub_product_line", JDBCType.INTEGER);

        public final SqlColumn<Integer> isActive = column("is_active", JDBCType.INTEGER);

        public final SqlColumn<Integer> tenantId = column("tenant_id", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<String> description = column("description", JDBCType.LONGVARCHAR);

        public WorkflowTemplate() {
            super("workflow_template", WorkflowTemplate::new);
        }
    }
}