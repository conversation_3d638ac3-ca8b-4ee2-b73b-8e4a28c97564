package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcVehicleRepairPicDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_repair_pic")
    public static final MtcVehicleRepairPic mtcVehicleRepairPic = new MtcVehicleRepairPic();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.id")
    public static final SqlColumn<Long> id = mtcVehicleRepairPic.id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.task_no")
    public static final SqlColumn<String> taskNo = mtcVehicleRepairPic.taskNo;

    /**
     * Database Column Remarks:
     *   图片区分 1:损坏部位图片 2:车辆维修图片 10:行驶证图片 11:保单图片 12:事故图片 13:车损图片(标的车) 14:车损图片(三者车) 15:理赔材料 16:其他图片 17:验收视频 18:轮胎更换修复后图片 19:事故责任认定书图片 20:保司定损单 21:我方驾驶证图片 22:维修任务垫付 23:定损视频
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.pic_type")
    public static final SqlColumn<Short> picType = mtcVehicleRepairPic.picType;

    /**
     * Database Column Remarks:
     *   车辆维修图片URL
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.pic_url")
    public static final SqlColumn<String> picUrl = mtcVehicleRepairPic.picUrl;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.create_by")
    public static final SqlColumn<String> createBy = mtcVehicleRepairPic.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.created_time")
    public static final SqlColumn<Date> createdTime = mtcVehicleRepairPic.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.update_by")
    public static final SqlColumn<String> updateBy = mtcVehicleRepairPic.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_repair_pic.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcVehicleRepairPic.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_repair_pic")
    public static final class MtcVehicleRepairPic extends AliasableSqlTable<MtcVehicleRepairPic> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> taskNo = column("task_no", JDBCType.VARCHAR);

        public final SqlColumn<Short> picType = column("pic_type", JDBCType.DECIMAL);

        public final SqlColumn<String> picUrl = column("pic_url", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcVehicleRepairPic() {
            super("mtc_vehicle_repair_pic", MtcVehicleRepairPic::new);
        }
    }
}