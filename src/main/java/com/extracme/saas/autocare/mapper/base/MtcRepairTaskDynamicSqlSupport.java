package com.extracme.saas.autocare.mapper.base;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcRepairTaskDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    public static final MtcRepairTask mtcRepairTask = new MtcRepairTask();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.id")
    public static final SqlColumn<Long> id = mtcRepairTask.id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.task_no")
    public static final SqlColumn<String> taskNo = mtcRepairTask.taskNo;

    /**
     * Database Column Remarks:
     *   任务类型（1：从调度直接推送过来的任务 2：手动刷数据从老平台移植过来的任务 3： 内部生成）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.task_type")
    public static final SqlColumn<Short> taskType = mtcRepairTask.taskType;

    /**
     * Database Column Remarks:
     *   车辆运营机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.org_id")
    public static final SqlColumn<String> orgId = mtcRepairTask.orgId;

    /**
     * Database Column Remarks:
     *   车辆运营机构名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.org_name")
    public static final SqlColumn<String> orgName = mtcRepairTask.orgName;

    /**
     * Database Column Remarks:
     *   车辆所属组织机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.operate_org_id")
    public static final SqlColumn<String> operateOrgId = mtcRepairTask.operateOrgId;

    /**
     * Database Column Remarks:
     *   车辆所属组织机构名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.operate_org_name")
    public static final SqlColumn<String> operateOrgName = mtcRepairTask.operateOrgName;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_no")
    public static final SqlColumn<String> vehicleNo = mtcRepairTask.vehicleNo;

    /**
     * Database Column Remarks:
     *   车型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_model_seq")
    public static final SqlColumn<Long> vehicleModelSeq = mtcRepairTask.vehicleModelSeq;

    /**
     * Database Column Remarks:
     *   车型名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_model_info")
    public static final SqlColumn<String> vehicleModelInfo = mtcRepairTask.vehicleModelInfo;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vin")
    public static final SqlColumn<String> vin = mtcRepairTask.vin;

    /**
     * Database Column Remarks:
     *   车辆保险所属
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_company_name")
    public static final SqlColumn<String> insuranceCompanyName = mtcRepairTask.insuranceCompanyName;

    /**
     * Database Column Remarks:
     *   修理类型ID（0：自建任务 1：进保维修 2：自费维修 3：车辆保养 4：轮胎任务 6：常规保养）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_type_id")
    public static final SqlColumn<Integer> repairTypeId = mtcRepairTask.repairTypeId;

    /**
     * Database Column Remarks:
     *   修理类型名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_type_name")
    public static final SqlColumn<String> repairTypeName = mtcRepairTask.repairTypeName;

    /**
     * Database Column Remarks:
     *   修理级别 A,B,C
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_grade")
    public static final SqlColumn<String> repairGrade = mtcRepairTask.repairGrade;

    /**
     * Database Column Remarks:
     *   修理厂ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_id")
    public static final SqlColumn<String> repairDepotId = mtcRepairTask.repairDepotId;

    /**
     * Database Column Remarks:
     *   修理厂名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_name")
    public static final SqlColumn<String> repairDepotName = mtcRepairTask.repairDepotName;

    /**
     * Database Column Remarks:
     *   修理厂组织机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_org_id")
    public static final SqlColumn<String> repairDepotOrgId = mtcRepairTask.repairDepotOrgId;

    /**
     * Database Column Remarks:
     *   供应商sap编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_sap_code")
    public static final SqlColumn<String> repairDepotSapCode = mtcRepairTask.repairDepotSapCode;

    /**
     * Database Column Remarks:
     *   sap数据发送状态 0未发送 1已发送 2发送失败 3冲销需重新发送 4发送失败需重新发送 5发送中
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sap_send_status")
    public static final SqlColumn<Integer> sapSendStatus = mtcRepairTask.sapSendStatus;

    /**
     * Database Column Remarks:
     *   sap同步标识 0自动同步 1手动同步
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sap_sync_flag")
    public static final SqlColumn<Integer> sapSyncFlag = mtcRepairTask.sapSyncFlag;

    /**
     * Database Column Remarks:
     *   sap发送成功时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sap_success_time")
    public static final SqlColumn<Date> sapSuccessTime = mtcRepairTask.sapSuccessTime;

    /**
     * Database Column Remarks:
     *   配件库类型(1:自有配件库 2:精友配件库)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.parts_library_type")
    public static final SqlColumn<Integer> partsLibraryType = mtcRepairTask.partsLibraryType;

    /**
     * Database Column Remarks:
     *   任务创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.task_create_time")
    public static final SqlColumn<Date> taskCreateTime = mtcRepairTask.taskCreateTime;

    /**
     * Database Column Remarks:
     *   调度任务创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.task_inflow_time")
    public static final SqlColumn<Date> taskInflowTime = mtcRepairTask.taskInflowTime;

    /**
     * Database Column Remarks:
     *   车辆接收时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_recive_time")
    public static final SqlColumn<Date> vehicleReciveTime = mtcRepairTask.vehicleReciveTime;

    /**
     * Database Column Remarks:
     *   车辆验收完成时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_check_time")
    public static final SqlColumn<Date> vehicleCheckTime = mtcRepairTask.vehicleCheckTime;

    /**
     * Database Column Remarks:
     *   车辆修理完成时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_repair_time")
    public static final SqlColumn<Date> vehicleRepairTime = mtcRepairTask.vehicleRepairTime;

    /**
     * Database Column Remarks:
     *   改派审核通过时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.reassignment_pass_time")
    public static final SqlColumn<Date> reassignmentPassTime = mtcRepairTask.reassignmentPassTime;

    /**
     * Database Column Remarks:
     *   结案时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.settle_closing_time")
    public static final SqlColumn<Date> settleClosingTime = mtcRepairTask.settleClosingTime;

    /**
     * Database Column Remarks:
     *   预计修理天数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.expected_repair_days")
    public static final SqlColumn<Long> expectedRepairDays = mtcRepairTask.expectedRepairDays;

    /**
     * Database Column Remarks:
     *   预计修理完成日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.expected_repair_complete")
    public static final SqlColumn<Date> expectedRepairComplete = mtcRepairTask.expectedRepairComplete;

    /**
     * Database Column Remarks:
     *   事故报案号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.accident_report_number")
    public static final SqlColumn<String> accidentReportNumber = mtcRepairTask.accidentReportNumber;

    /**
     * Database Column Remarks:
     *   是否需要维修 0:否 1:是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_flag")
    public static final SqlColumn<Short> repairFlag = mtcRepairTask.repairFlag;

    /**
     * Database Column Remarks:
     *   终端ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.terminal_id")
    public static final SqlColumn<String> terminalId = mtcRepairTask.terminalId;

    /**
     * Database Column Remarks:
     *   总里程数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.total_mileage")
    public static final SqlColumn<BigDecimal> totalMileage = mtcRepairTask.totalMileage;

    /**
     * Database Column Remarks:
     *   终端实时里程数(核损时)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.terminal_mileage")
    public static final SqlColumn<String> terminalMileage = mtcRepairTask.terminalMileage;

    /**
     * Database Column Remarks:
     *   关联订单
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.associated_order")
    public static final SqlColumn<String> associatedOrder = mtcRepairTask.associatedOrder;

    /**
     * Database Column Remarks:
     *   订单类型 1=长租订单 2=门店订单 3=渠道订单 4=内部订单 5=分时订单
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.order_type")
    public static final SqlColumn<Integer> orderType = mtcRepairTask.orderType;

    /**
     * Database Column Remarks:
     *   关联类型： 1=订单匹配 2=手工关联 3=无关联订单
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.relate_type")
    public static final SqlColumn<Integer> relateType = mtcRepairTask.relateType;

    /**
     * Database Column Remarks:
     *   关联订单备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.order_remark")
    public static final SqlColumn<String> orderRemark = mtcRepairTask.orderRemark;

    /**
     * Database Column Remarks:
     *   违章联系人（长租订单）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.violation_name")
    public static final SqlColumn<String> violationName = mtcRepairTask.violationName;

    /**
     * Database Column Remarks:
     *   违章联系人联系方式（长租订单）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.violation_tel_no")
    public static final SqlColumn<String> violationTelNo = mtcRepairTask.violationTelNo;

    /**
     * Database Column Remarks:
     *   是否客户承担维修费	1是 2否（长租订单）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_client_maintenance_fee")
    public static final SqlColumn<Byte> vehicleClientMaintenanceFee = mtcRepairTask.vehicleClientMaintenanceFee;

    /**
     * Database Column Remarks:
     *   是否客户承担年检费 1=是 2=否（长租订单）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.client_inspect_tag")
    public static final SqlColumn<Byte> clientInspectTag = mtcRepairTask.clientInspectTag;

    /**
     * Database Column Remarks:
     *   是否客户承担保养费 1=是 2=否（长租订单）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.client_upkeep_tag")
    public static final SqlColumn<Byte> clientUpkeepTag = mtcRepairTask.clientUpkeepTag;

    /**
     * Database Column Remarks:
     *   会员id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.auth_id")
    public static final SqlColumn<String> authId = mtcRepairTask.authId;

    /**
     * Database Column Remarks:
     *   是否购买不计免赔 0:否 1:是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.no_deductibles_flag")
    public static final SqlColumn<Integer> noDeductiblesFlag = mtcRepairTask.noDeductiblesFlag;

    /**
     * Database Column Remarks:
     *   服务类别 1-基础服务 2-优享服务 3-尊享服务 （门店订单）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.service_type")
    public static final SqlColumn<Integer> serviceType = mtcRepairTask.serviceType;

    /**
     * Database Column Remarks:
     *   保障内容（门店订单）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.service_content")
    public static final SqlColumn<String> serviceContent = mtcRepairTask.serviceContent;

    /**
     * Database Column Remarks:
     *   驾驶员姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.driver_name")
    public static final SqlColumn<String> driverName = mtcRepairTask.driverName;

    /**
     * Database Column Remarks:
     *   驾驶员手机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.driver_tel")
    public static final SqlColumn<String> driverTel = mtcRepairTask.driverTel;

    /**
     * Database Column Remarks:
     *   巡检姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.routing_inspection_name")
    public static final SqlColumn<String> routingInspectionName = mtcRepairTask.routingInspectionName;

    /**
     * Database Column Remarks:
     *   巡检手机号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.routing_inspection_tel")
    public static final SqlColumn<String> routingInspectionTel = mtcRepairTask.routingInspectionTel;

    /**
     * Database Column Remarks:
     *   受损部位描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.damaged_part_describe")
    public static final SqlColumn<String> damagedPartDescribe = mtcRepairTask.damagedPartDescribe;

    /**
     * Database Column Remarks:
     *   事故描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.accident_describe")
    public static final SqlColumn<String> accidentDescribe = mtcRepairTask.accidentDescribe;

    /**
     * Database Column Remarks:
     *   是否有拖车 0:否 1:是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.trailer_flag")
    public static final SqlColumn<Short> trailerFlag = mtcRepairTask.trailerFlag;

    /**
     * Database Column Remarks:
     *   进保预审金额合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_review_total_amount")
    public static final SqlColumn<BigDecimal> repairReviewTotalAmount = mtcRepairTask.repairReviewTotalAmount;

    /**
     * Database Column Remarks:
     *   修理厂换件金额合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_replace_total_amount")
    public static final SqlColumn<BigDecimal> repairReplaceTotalAmount = mtcRepairTask.repairReplaceTotalAmount;

    /**
     * Database Column Remarks:
     *   修理厂修理金额合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_repair_total_amount")
    public static final SqlColumn<BigDecimal> repairRepairTotalAmount = mtcRepairTask.repairRepairTotalAmount;

    /**
     * Database Column Remarks:
     *   修理厂定损总计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_insurance_total_amount")
    public static final SqlColumn<BigDecimal> repairInsuranceTotalAmount = mtcRepairTask.repairInsuranceTotalAmount;

    /**
     * Database Column Remarks:
     *   车管换件金额合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_replace_total_amount")
    public static final SqlColumn<BigDecimal> vehicleReplaceTotalAmount = mtcRepairTask.vehicleReplaceTotalAmount;

    /**
     * Database Column Remarks:
     *   车管修理金额合计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_repair_total_amount")
    public static final SqlColumn<BigDecimal> vehicleRepairTotalAmount = mtcRepairTask.vehicleRepairTotalAmount;

    /**
     * Database Column Remarks:
     *   车管定损总计
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_insurance_total_amount")
    public static final SqlColumn<BigDecimal> vehicleInsuranceTotalAmount = mtcRepairTask.vehicleInsuranceTotalAmount;

    /**
     * Database Column Remarks:
     *   车管核价意见 0:同意报价 1:异议
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_manage_view_flag")
    public static final SqlColumn<Short> vehicleManageViewFlag = mtcRepairTask.vehicleManageViewFlag;

    /**
     * Database Column Remarks:
     *   是否需要复勘 0:否 1:是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.resurvey_flag")
    public static final SqlColumn<Short> resurveyFlag = mtcRepairTask.resurveyFlag;

    /**
     * Database Column Remarks:
     *   复勘部位
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.resurvey_part")
    public static final SqlColumn<String> resurveyPart = mtcRepairTask.resurveyPart;

    /**
     * Database Column Remarks:
     *   是否需要向用户追偿 0:否 1:是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.recovery_flag")
    public static final SqlColumn<Short> recoveryFlag = mtcRepairTask.recoveryFlag;

    /**
     * Database Column Remarks:
     *   责任情况 1:全责 2:主责 3:次责 4:平责 5:无责
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.duty_situation")
    public static final SqlColumn<Integer> dutySituation = mtcRepairTask.dutySituation;

    /**
     * Database Column Remarks:
     *   向用户追偿费用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.recovery_amount")
    public static final SqlColumn<BigDecimal> recoveryAmount = mtcRepairTask.recoveryAmount;

    /**
     * Database Column Remarks:
     *   保险上付费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_amount")
    public static final SqlColumn<BigDecimal> insuranceAmount = mtcRepairTask.insuranceAmount;

    /**
     * Database Column Remarks:
     *   加速折旧费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.acc_dep_amount")
    public static final SqlColumn<BigDecimal> accDepAmount = mtcRepairTask.accDepAmount;

    /**
     * Database Column Remarks:
     *   停运损失费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.outage_loss_amount")
    public static final SqlColumn<BigDecimal> outageLossAmount = mtcRepairTask.outageLossAmount;

    /**
     * Database Column Remarks:
     *   车辆损失费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_loss_amount")
    public static final SqlColumn<BigDecimal> vehicleLossAmount = mtcRepairTask.vehicleLossAmount;

    /**
     * Database Column Remarks:
     *   拖车救援费
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.trailer_rescue_amount")
    public static final SqlColumn<BigDecimal> trailerRescueAmount = mtcRepairTask.trailerRescueAmount;

    /**
     * Database Column Remarks:
     *   保养费用
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.maintain_amount")
    public static final SqlColumn<BigDecimal> maintainAmount = mtcRepairTask.maintainAmount;

    /**
     * Database Column Remarks:
     *   定损单金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.loss_order_amount")
    public static final SqlColumn<BigDecimal> lossOrderAmount = mtcRepairTask.lossOrderAmount;

    /**
     * Database Column Remarks:
     *   改派修理厂组织机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.reassignment_repair_org_id")
    public static final SqlColumn<String> reassignmentRepairOrgId = mtcRepairTask.reassignmentRepairOrgId;

    /**
     * Database Column Remarks:
     *   改派原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.reassignment_reasons")
    public static final SqlColumn<String> reassignmentReasons = mtcRepairTask.reassignmentReasons;

    /**
     * Database Column Remarks:
     *   改派驳回原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.reassignment_reject_reasons")
    public static final SqlColumn<String> reassignmentRejectReasons = mtcRepairTask.reassignmentRejectReasons;

    /**
     * Database Column Remarks:
     *   核损驳回原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_reject_reasons")
    public static final SqlColumn<String> verificationRejectReasons = mtcRepairTask.verificationRejectReasons;

    /**
     * Database Column Remarks:
     *   核损驳回原因详情
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_reject_reasons_detail")
    public static final SqlColumn<String> verificationRejectReasonsDetail = mtcRepairTask.verificationRejectReasonsDetail;

    /**
     * Database Column Remarks:
     *   核损驳回时等级
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_reject_level")
    public static final SqlColumn<String> verificationRejectLevel = mtcRepairTask.verificationRejectLevel;

    /**
     * Database Column Remarks:
     *   车辆验收结果 0:验收通过 1:验收不通过
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.check_result_flag")
    public static final SqlColumn<Short> checkResultFlag = mtcRepairTask.checkResultFlag;

    /**
     * Database Column Remarks:
     *   验收不合格原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.check_unqualified_reason")
    public static final SqlColumn<String> checkUnqualifiedReason = mtcRepairTask.checkUnqualifiedReason;

    /**
     * Database Column Remarks:
     *   自费和进保任务确认类型（1公司自费2客户付费3我方有责4我方无责）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.confirm_type")
    public static final SqlColumn<Integer> confirmType = mtcRepairTask.confirmType;

    /**
     * Database Column Remarks:
     *   保养转维修标志 -1:待选择0:否 1:是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.maintain_to_repair_flag")
    public static final SqlColumn<Short> maintainToRepairFlag = mtcRepairTask.maintainToRepairFlag;

    /**
     * Database Column Remarks:
     *   核损核价任务占有人ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_loss_task_oper_id")
    public static final SqlColumn<Long> verificationLossTaskOperId = mtcRepairTask.verificationLossTaskOperId;

    /**
     * Database Column Remarks:
     *   是否提交上级 0:未提交 1:已提交
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.examine_level")
    public static final SqlColumn<Short> examineLevel = mtcRepairTask.examineLevel;

    /**
     * Database Column Remarks:
     *   核损通过时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_loss_check_time")
    public static final SqlColumn<Date> verificationLossCheckTime = mtcRepairTask.verificationLossCheckTime;

    /**
     * Database Column Remarks:
     *   一级业务处理人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.verification_loss_check_id")
    public static final SqlColumn<Long> verificationLossCheckId = mtcRepairTask.verificationLossCheckId;

    /**
     * Database Column Remarks:
     *   超时原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.over_time_reasons")
    public static final SqlColumn<String> overTimeReasons = mtcRepairTask.overTimeReasons;

    /**
     * Database Column Remarks:
     *   首次报价金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_total_amount_first")
    public static final SqlColumn<BigDecimal> repairTotalAmountFirst = mtcRepairTask.repairTotalAmountFirst;

    /**
     * Database Column Remarks:
     *   是否驳回标识
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.nuclear_loss_reversion_flag")
    public static final SqlColumn<Integer> nuclearLossReversionFlag = mtcRepairTask.nuclearLossReversionFlag;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.remark")
    public static final SqlColumn<String> remark = mtcRepairTask.remark;

    /**
     * Database Column Remarks:
     *   轮胎数量
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.tireNumber")
    public static final SqlColumn<Long> tirenumber = mtcRepairTask.tirenumber;

    /**
     * Database Column Remarks:
     *   轮胎单价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.tire_unit_price")
    public static final SqlColumn<BigDecimal> tireUnitPrice = mtcRepairTask.tireUnitPrice;

    /**
     * Database Column Remarks:
     *   轮胎品牌
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.tire_brand")
    public static final SqlColumn<String> tireBrand = mtcRepairTask.tireBrand;

    /**
     * Database Column Remarks:
     *   关联任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.associated_task_no")
    public static final SqlColumn<String> associatedTaskNo = mtcRepairTask.associatedTaskNo;

    /**
     * Database Column Remarks:
     *   是否进保（0：否 1：是）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_flag")
    public static final SqlColumn<Integer> insuranceFlag = mtcRepairTask.insuranceFlag;

    /**
     * Database Column Remarks:
     *   二级审核级别 2:待运营经理审核 3:待资产管理部审核 4:待运营管理部审核 5:资产管理已退回 6:运营管理部已退回
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.advanced_audit_leve")
    public static final SqlColumn<Integer> advancedAuditLeve = mtcRepairTask.advancedAuditLeve;

    /**
     * Database Column Remarks:
     *   已来款金额(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.already_incoming_amount")
    public static final SqlColumn<BigDecimal> alreadyIncomingAmount = mtcRepairTask.alreadyIncomingAmount;

    /**
     * Database Column Remarks:
     *   已支付金额(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.already_pay_amount")
    public static final SqlColumn<BigDecimal> alreadyPayAmount = mtcRepairTask.alreadyPayAmount;

    /**
     * Database Column Remarks:
     *   待支付金额(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.wait_pay_amount")
    public static final SqlColumn<BigDecimal> waitPayAmount = mtcRepairTask.waitPayAmount;

    /**
     * Database Column Remarks:
     *   应理赔金额总计(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.must_claim_amount")
    public static final SqlColumn<BigDecimal> mustClaimAmount = mtcRepairTask.mustClaimAmount;

    /**
     * Database Column Remarks:
     *   应支付金额总计(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.must_pay_amount")
    public static final SqlColumn<BigDecimal> mustPayAmount = mtcRepairTask.mustPayAmount;

    /**
     * Database Column Remarks:
     *   维修成本金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_repair_cost_amount")
    public static final SqlColumn<Double> vehicleRepairCostAmount = mtcRepairTask.vehicleRepairCostAmount;

    /**
     * Database Column Remarks:
     *   结算金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_settle_amount")
    public static final SqlColumn<Double> repairSettleAmount = mtcRepairTask.repairSettleAmount;

    /**
     * Database Column Remarks:
     *   税前结算金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_fax_settle_amount")
    public static final SqlColumn<Double> repairFaxSettleAmount = mtcRepairTask.repairFaxSettleAmount;

    /**
     * Database Column Remarks:
     *   确定转为收入(0：未确认 1：已确认)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.transfer_flag")
    public static final SqlColumn<Integer> transferFlag = mtcRepairTask.transferFlag;

    /**
     * Database Column Remarks:
     *   理赔款关联计数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.rational_indemnity_cnt")
    public static final SqlColumn<Integer> rationalIndemnityCnt = mtcRepairTask.rationalIndemnityCnt;

    /**
     * Database Column Remarks:
     *   事故发生时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.accident_day_time")
    public static final SqlColumn<Date> accidentDayTime = mtcRepairTask.accidentDayTime;

    /**
     * Database Column Remarks:
     *   免赔结算状态(0:无 1:未结算 2:已结算)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.deduct_flag")
    public static final SqlColumn<Integer> deductFlag = mtcRepairTask.deductFlag;

    /**
     * Database Column Remarks:
     *   任务来源（0：调度系统 1：车管系统）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.origin")
    public static final SqlColumn<Integer> origin = mtcRepairTask.origin;

    /**
     * Database Column Remarks:
     *   车辆业务状态（-1：未出库 0：分时租赁 1：长租 3：短租 4：公务用车）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.renttype")
    public static final SqlColumn<Integer> renttype = mtcRepairTask.renttype;

    /**
     * Database Column Remarks:
     *   实际运营标签 0.未投车辆 1.短租运营车辆 2.长租运营车辆 3.备库车辆 4.待退运车辆 5.已处置车辆 6.特殊车辆
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.fact_operate_tag")
    public static final SqlColumn<Integer> factOperateTag = mtcRepairTask.factOperateTag;

    /**
     * Database Column Remarks:
     *   提车人姓名
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.take_user_name")
    public static final SqlColumn<String> takeUserName = mtcRepairTask.takeUserName;

    /**
     * Database Column Remarks:
     *   提车人手机
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.take_user_phone")
    public static final SqlColumn<String> takeUserPhone = mtcRepairTask.takeUserPhone;

    /**
     * Database Column Remarks:
     *   提车凭证
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.take_voucher")
    public static final SqlColumn<String> takeVoucher = mtcRepairTask.takeVoucher;

    /**
     * Database Column Remarks:
     *   提车信息同步时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.syn_take_time")
    public static final SqlColumn<Date> synTakeTime = mtcRepairTask.synTakeTime;

    /**
     * Database Column Remarks:
     *   关闭原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.close_reason")
    public static final SqlColumn<String> closeReason = mtcRepairTask.closeReason;

    /**
     * Database Column Remarks:
     *   是否保险理赔状态：0否 1是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.claims_flag")
    public static final SqlColumn<Integer> claimsFlag = mtcRepairTask.claimsFlag;

    /**
     * Database Column Remarks:
     *   预估理赔金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.estimated_claim_amount")
    public static final SqlColumn<BigDecimal> estimatedClaimAmount = mtcRepairTask.estimatedClaimAmount;

    /**
     * Database Column Remarks:
     *   车损类型 1 人为损坏 2 自然损耗
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.car_damage_type")
    public static final SqlColumn<Integer> carDamageType = mtcRepairTask.carDamageType;

    /**
     * Database Column Remarks:
     *   车辆处置残值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.vehicle_scrape_value")
    public static final SqlColumn<BigDecimal> vehicleScrapeValue = mtcRepairTask.vehicleScrapeValue;

    /**
     * Database Column Remarks:
     *   进保预审转自费状态：0否 1是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.review_to_sel_fee_flag")
    public static final SqlColumn<Integer> reviewToSelFeeFlag = mtcRepairTask.reviewToSelFeeFlag;

    /**
     * Database Column Remarks:
     *   确认车损类型 1：我司车辆原因 2：客户原因
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.confirm_car_damage_type")
    public static final SqlColumn<Integer> confirmCarDamageType = mtcRepairTask.confirmCarDamageType;

    /**
     * Database Column Remarks:
     *   事故理赔任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.accident_no")
    public static final SqlColumn<String> accidentNo = mtcRepairTask.accidentNo;

    /**
     * Database Column Remarks:
     *   进保预审通过时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_pre_review_time")
    public static final SqlColumn<Date> insurancePreReviewTime = mtcRepairTask.insurancePreReviewTime;

    /**
     * Database Column Remarks:
     *   进保预审阶段
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.insurance_pre_review_level")
    public static final SqlColumn<Integer> insurancePreReviewLevel = mtcRepairTask.insurancePreReviewLevel;

    /**
     * Database Column Remarks:
     *   预审车辆处置残值
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.pre_review_vehicle_scrape_value")
    public static final SqlColumn<BigDecimal> preReviewVehicleScrapeValue = mtcRepairTask.preReviewVehicleScrapeValue;

    /**
     * Database Column Remarks:
     *   预审车辆处置残值时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.pre_review_vehicle_scrape_time")
    public static final SqlColumn<Date> preReviewVehicleScrapeTime = mtcRepairTask.preReviewVehicleScrapeTime;

    /**
     * Database Column Remarks:
     *   是否使用维修小程序 0-否 1-是
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.is_used_applets")
    public static final SqlColumn<String> isUsedApplets = mtcRepairTask.isUsedApplets;

    /**
     * Database Column Remarks:
     *   标准id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.criteria_id")
    public static final SqlColumn<Long> criteriaId = mtcRepairTask.criteriaId;

    /**
     * Database Column Remarks:
     *   是否客户直付 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.cust_pays_direct")
    public static final SqlColumn<Integer> custPaysDirect = mtcRepairTask.custPaysDirect;

    /**
     * Database Column Remarks:
     *   客户直付金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.cust_amount")
    public static final SqlColumn<BigDecimal> custAmount = mtcRepairTask.custAmount;

    /**
     * Database Column Remarks:
     *   用户承担金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.user_assumed_amount")
    public static final SqlColumn<BigDecimal> userAssumedAmount = mtcRepairTask.userAssumedAmount;

    /**
     * Database Column Remarks:
     *   非用户承担金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.not_user_assumed_amount")
    public static final SqlColumn<BigDecimal> notUserAssumedAmount = mtcRepairTask.notUserAssumedAmount;

    /**
     * Database Column Remarks:
     *   是否同步业财理赔金额 1-是 2-否
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sync_claim_flag")
    public static final SqlColumn<Integer> syncClaimFlag = mtcRepairTask.syncClaimFlag;

    /**
     * Database Column Remarks:
     *   税率
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.tax_rate")
    public static final SqlColumn<String> taxRate = mtcRepairTask.taxRate;

    /**
     * Database Column Remarks:
     *   修理厂类型(1:合作修理厂 2:非合作修理厂)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.repair_depot_type")
    public static final SqlColumn<Integer> repairDepotType = mtcRepairTask.repairDepotType;

    /**
     * Database Column Remarks:
     *   关联账单(PO)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.declare_no")
    public static final SqlColumn<String> declareNo = mtcRepairTask.declareNo;

    /**
     * Database Column Remarks:
     *   申报状态 1=未提交 2=审批中 3=审批通过 4=审批拒绝
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.declare_status")
    public static final SqlColumn<Integer> declareStatus = mtcRepairTask.declareStatus;

    /**
     * Database Column Remarks:
     *   结算状态 1=已生成 2=待结算 3=结算中 4=已结算 5=已作废
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.declare_settlement_status")
    public static final SqlColumn<Integer> declareSettlementStatus = mtcRepairTask.declareSettlementStatus;

    /**
     * Database Column Remarks:
     *   关联结算单(GR)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.settlement_no")
    public static final SqlColumn<String> settlementNo = mtcRepairTask.settlementNo;

    /**
     * Database Column Remarks:
     *   关联结算单状态 1=未提交 2=审核中 3=审批通过 4=审批拒绝 5=已作废 6=已关闭
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.settlement_status")
    public static final SqlColumn<Integer> settlementStatus = mtcRepairTask.settlementStatus;

    /**
     * Database Column Remarks:
     *   送修时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.send_repair_time")
    public static final SqlColumn<Date> sendRepairTime = mtcRepairTask.sendRepairTime;

    /**
     * Database Column Remarks:
     *   情况描述
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.situation_desc")
    public static final SqlColumn<String> situationDesc = mtcRepairTask.situationDesc;

    /**
     * Database Column Remarks:
     *   资产状态: 0=在建工程, 1=固定资产, 2=固定资产（待报废）, 3=报废, 4=固定资产(待处置), 5=固定资产(已处置), 6=以租代售, 7=库存商品, 8=已处置（未过户）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.property_status")
    public static final SqlColumn<Integer> propertyStatus = mtcRepairTask.propertyStatus;

    /**
     * Database Column Remarks:
     *   产品线: 1=车管中心, 2=长租, 3=短租, 4=公务用车
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.product_line")
    public static final SqlColumn<Integer> productLine = mtcRepairTask.productLine;

    /**
     * Database Column Remarks:
     *   子产品线: 1=携程短租-短租, 2=门店-短租, 3=分时-短租, 4=普通-长租, 5=时行-长租, 6=平台业务-长租, 7=政企业务-长租, 8=网约车业务-长租
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.sub_product_line")
    public static final SqlColumn<Integer> subProductLine = mtcRepairTask.subProductLine;

    /**
     * Database Column Remarks:
     *   自费金额
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.self_funded_amount")
    public static final SqlColumn<BigDecimal> selfFundedAmount = mtcRepairTask.selfFundedAmount;

    /**
     * Database Column Remarks:
     *   连续停放天数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.continue_days")
    public static final SqlColumn<String> continueDays = mtcRepairTask.continueDays;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.create_by")
    public static final SqlColumn<String> createBy = mtcRepairTask.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.created_time")
    public static final SqlColumn<Date> createdTime = mtcRepairTask.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.update_by")
    public static final SqlColumn<String> updateBy = mtcRepairTask.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_task.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcRepairTask.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    public static final class MtcRepairTask extends AliasableSqlTable<MtcRepairTask> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> taskNo = column("task_no", JDBCType.VARCHAR);

        public final SqlColumn<Short> taskType = column("task_type", JDBCType.DECIMAL);

        public final SqlColumn<String> orgId = column("org_id", JDBCType.VARCHAR);

        public final SqlColumn<String> orgName = column("org_name", JDBCType.VARCHAR);

        public final SqlColumn<String> operateOrgId = column("operate_org_id", JDBCType.VARCHAR);

        public final SqlColumn<String> operateOrgName = column("operate_org_name", JDBCType.VARCHAR);

        public final SqlColumn<String> vehicleNo = column("vehicle_no", JDBCType.VARCHAR);

        public final SqlColumn<Long> vehicleModelSeq = column("vehicle_model_seq", JDBCType.BIGINT);

        public final SqlColumn<String> vehicleModelInfo = column("vehicle_model_info", JDBCType.VARCHAR);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<String> insuranceCompanyName = column("insurance_company_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> repairTypeId = column("repair_type_id", JDBCType.INTEGER);

        public final SqlColumn<String> repairTypeName = column("repair_type_name", JDBCType.VARCHAR);

        public final SqlColumn<String> repairGrade = column("repair_grade", JDBCType.VARCHAR);

        public final SqlColumn<String> repairDepotId = column("repair_depot_id", JDBCType.VARCHAR);

        public final SqlColumn<String> repairDepotName = column("repair_depot_name", JDBCType.VARCHAR);

        public final SqlColumn<String> repairDepotOrgId = column("repair_depot_org_id", JDBCType.VARCHAR);

        public final SqlColumn<String> repairDepotSapCode = column("repair_depot_sap_code", JDBCType.VARCHAR);

        public final SqlColumn<Integer> sapSendStatus = column("sap_send_status", JDBCType.INTEGER);

        public final SqlColumn<Integer> sapSyncFlag = column("sap_sync_flag", JDBCType.INTEGER);

        public final SqlColumn<Date> sapSuccessTime = column("sap_success_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> partsLibraryType = column("parts_library_type", JDBCType.INTEGER);

        public final SqlColumn<Date> taskCreateTime = column("task_create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> taskInflowTime = column("task_inflow_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> vehicleReciveTime = column("vehicle_recive_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> vehicleCheckTime = column("vehicle_check_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> vehicleRepairTime = column("vehicle_repair_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> reassignmentPassTime = column("reassignment_pass_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> settleClosingTime = column("settle_closing_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> expectedRepairDays = column("expected_repair_days", JDBCType.BIGINT);

        public final SqlColumn<Date> expectedRepairComplete = column("expected_repair_complete", JDBCType.TIMESTAMP);

        public final SqlColumn<String> accidentReportNumber = column("accident_report_number", JDBCType.VARCHAR);

        public final SqlColumn<Short> repairFlag = column("repair_flag", JDBCType.DECIMAL);

        public final SqlColumn<String> terminalId = column("terminal_id", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> totalMileage = column("total_mileage", JDBCType.DECIMAL);

        public final SqlColumn<String> terminalMileage = column("terminal_mileage", JDBCType.VARCHAR);

        public final SqlColumn<String> associatedOrder = column("associated_order", JDBCType.VARCHAR);

        public final SqlColumn<Integer> orderType = column("order_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> relateType = column("relate_type", JDBCType.INTEGER);

        public final SqlColumn<String> orderRemark = column("order_remark", JDBCType.VARCHAR);

        public final SqlColumn<String> violationName = column("violation_name", JDBCType.VARCHAR);

        public final SqlColumn<String> violationTelNo = column("violation_tel_no", JDBCType.VARCHAR);

        public final SqlColumn<Byte> vehicleClientMaintenanceFee = column("vehicle_client_maintenance_fee", JDBCType.TINYINT);

        public final SqlColumn<Byte> clientInspectTag = column("client_inspect_tag", JDBCType.TINYINT);

        public final SqlColumn<Byte> clientUpkeepTag = column("client_upkeep_tag", JDBCType.TINYINT);

        public final SqlColumn<String> authId = column("auth_id", JDBCType.VARCHAR);

        public final SqlColumn<Integer> noDeductiblesFlag = column("no_deductibles_flag", JDBCType.INTEGER);

        public final SqlColumn<Integer> serviceType = column("service_type", JDBCType.INTEGER);

        public final SqlColumn<String> serviceContent = column("service_content", JDBCType.VARCHAR);

        public final SqlColumn<String> driverName = column("driver_name", JDBCType.VARCHAR);

        public final SqlColumn<String> driverTel = column("driver_tel", JDBCType.VARCHAR);

        public final SqlColumn<String> routingInspectionName = column("routing_inspection_name", JDBCType.VARCHAR);

        public final SqlColumn<String> routingInspectionTel = column("routing_inspection_tel", JDBCType.VARCHAR);

        public final SqlColumn<String> damagedPartDescribe = column("damaged_part_describe", JDBCType.VARCHAR);

        public final SqlColumn<String> accidentDescribe = column("accident_describe", JDBCType.VARCHAR);

        public final SqlColumn<Short> trailerFlag = column("trailer_flag", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> repairReviewTotalAmount = column("repair_review_total_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> repairReplaceTotalAmount = column("repair_replace_total_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> repairRepairTotalAmount = column("repair_repair_total_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> repairInsuranceTotalAmount = column("repair_insurance_total_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> vehicleReplaceTotalAmount = column("vehicle_replace_total_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> vehicleRepairTotalAmount = column("vehicle_repair_total_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> vehicleInsuranceTotalAmount = column("vehicle_insurance_total_amount", JDBCType.DECIMAL);

        public final SqlColumn<Short> vehicleManageViewFlag = column("vehicle_manage_view_flag", JDBCType.DECIMAL);

        public final SqlColumn<Short> resurveyFlag = column("resurvey_flag", JDBCType.DECIMAL);

        public final SqlColumn<String> resurveyPart = column("resurvey_part", JDBCType.VARCHAR);

        public final SqlColumn<Short> recoveryFlag = column("recovery_flag", JDBCType.DECIMAL);

        public final SqlColumn<Integer> dutySituation = column("duty_situation", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> recoveryAmount = column("recovery_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> insuranceAmount = column("insurance_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> accDepAmount = column("acc_dep_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> outageLossAmount = column("outage_loss_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> vehicleLossAmount = column("vehicle_loss_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> trailerRescueAmount = column("trailer_rescue_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> maintainAmount = column("maintain_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> lossOrderAmount = column("loss_order_amount", JDBCType.DECIMAL);

        public final SqlColumn<String> reassignmentRepairOrgId = column("reassignment_repair_org_id", JDBCType.VARCHAR);

        public final SqlColumn<String> reassignmentReasons = column("reassignment_reasons", JDBCType.VARCHAR);

        public final SqlColumn<String> reassignmentRejectReasons = column("reassignment_reject_reasons", JDBCType.VARCHAR);

        public final SqlColumn<String> verificationRejectReasons = column("verification_reject_reasons", JDBCType.VARCHAR);

        public final SqlColumn<String> verificationRejectReasonsDetail = column("verification_reject_reasons_detail", JDBCType.VARCHAR);

        public final SqlColumn<String> verificationRejectLevel = column("verification_reject_level", JDBCType.VARCHAR);

        public final SqlColumn<Short> checkResultFlag = column("check_result_flag", JDBCType.DECIMAL);

        public final SqlColumn<String> checkUnqualifiedReason = column("check_unqualified_reason", JDBCType.VARCHAR);

        public final SqlColumn<Integer> confirmType = column("confirm_type", JDBCType.INTEGER);

        public final SqlColumn<Short> maintainToRepairFlag = column("maintain_to_repair_flag", JDBCType.DECIMAL);

        public final SqlColumn<Long> verificationLossTaskOperId = column("verification_loss_task_oper_id", JDBCType.BIGINT);

        public final SqlColumn<Short> examineLevel = column("examine_level", JDBCType.DECIMAL);

        public final SqlColumn<Date> verificationLossCheckTime = column("verification_loss_check_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> verificationLossCheckId = column("verification_loss_check_id", JDBCType.BIGINT);

        public final SqlColumn<String> overTimeReasons = column("over_time_reasons", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> repairTotalAmountFirst = column("repair_total_amount_first", JDBCType.DECIMAL);

        public final SqlColumn<Integer> nuclearLossReversionFlag = column("nuclear_loss_reversion_flag", JDBCType.INTEGER);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<Long> tirenumber = column("tireNumber", JDBCType.BIGINT);

        public final SqlColumn<BigDecimal> tireUnitPrice = column("tire_unit_price", JDBCType.DECIMAL);

        public final SqlColumn<String> tireBrand = column("tire_brand", JDBCType.VARCHAR);

        public final SqlColumn<String> associatedTaskNo = column("associated_task_no", JDBCType.VARCHAR);

        public final SqlColumn<Integer> insuranceFlag = column("insurance_flag", JDBCType.INTEGER);

        public final SqlColumn<Integer> advancedAuditLeve = column("advanced_audit_leve", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> alreadyIncomingAmount = column("already_incoming_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> alreadyPayAmount = column("already_pay_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> waitPayAmount = column("wait_pay_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> mustClaimAmount = column("must_claim_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> mustPayAmount = column("must_pay_amount", JDBCType.DECIMAL);

        public final SqlColumn<Double> vehicleRepairCostAmount = column("vehicle_repair_cost_amount", JDBCType.DOUBLE);

        public final SqlColumn<Double> repairSettleAmount = column("repair_settle_amount", JDBCType.DOUBLE);

        public final SqlColumn<Double> repairFaxSettleAmount = column("repair_fax_settle_amount", JDBCType.DOUBLE);

        public final SqlColumn<Integer> transferFlag = column("transfer_flag", JDBCType.INTEGER);

        public final SqlColumn<Integer> rationalIndemnityCnt = column("rational_indemnity_cnt", JDBCType.INTEGER);

        public final SqlColumn<Date> accidentDayTime = column("accident_day_time", JDBCType.DATE);

        public final SqlColumn<Integer> deductFlag = column("deduct_flag", JDBCType.INTEGER);

        public final SqlColumn<Integer> origin = column("origin", JDBCType.INTEGER);

        public final SqlColumn<Integer> renttype = column("renttype", JDBCType.INTEGER);

        public final SqlColumn<Integer> factOperateTag = column("fact_operate_tag", JDBCType.INTEGER);

        public final SqlColumn<String> takeUserName = column("take_user_name", JDBCType.VARCHAR);

        public final SqlColumn<String> takeUserPhone = column("take_user_phone", JDBCType.VARCHAR);

        public final SqlColumn<String> takeVoucher = column("take_voucher", JDBCType.VARCHAR);

        public final SqlColumn<Date> synTakeTime = column("syn_take_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> closeReason = column("close_reason", JDBCType.VARCHAR);

        public final SqlColumn<Integer> claimsFlag = column("claims_flag", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> estimatedClaimAmount = column("estimated_claim_amount", JDBCType.DECIMAL);

        public final SqlColumn<Integer> carDamageType = column("car_damage_type", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> vehicleScrapeValue = column("vehicle_scrape_value", JDBCType.DECIMAL);

        public final SqlColumn<Integer> reviewToSelFeeFlag = column("review_to_sel_fee_flag", JDBCType.INTEGER);

        public final SqlColumn<Integer> confirmCarDamageType = column("confirm_car_damage_type", JDBCType.INTEGER);

        public final SqlColumn<String> accidentNo = column("accident_no", JDBCType.VARCHAR);

        public final SqlColumn<Date> insurancePreReviewTime = column("insurance_pre_review_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> insurancePreReviewLevel = column("insurance_pre_review_level", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> preReviewVehicleScrapeValue = column("pre_review_vehicle_scrape_value", JDBCType.DECIMAL);

        public final SqlColumn<Date> preReviewVehicleScrapeTime = column("pre_review_vehicle_scrape_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> isUsedApplets = column("is_used_applets", JDBCType.VARCHAR);

        public final SqlColumn<Long> criteriaId = column("criteria_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> custPaysDirect = column("cust_pays_direct", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> custAmount = column("cust_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> userAssumedAmount = column("user_assumed_amount", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> notUserAssumedAmount = column("not_user_assumed_amount", JDBCType.DECIMAL);

        public final SqlColumn<Integer> syncClaimFlag = column("sync_claim_flag", JDBCType.INTEGER);

        public final SqlColumn<String> taxRate = column("tax_rate", JDBCType.VARCHAR);

        public final SqlColumn<Integer> repairDepotType = column("repair_depot_type", JDBCType.INTEGER);

        public final SqlColumn<String> declareNo = column("declare_no", JDBCType.VARCHAR);

        public final SqlColumn<Integer> declareStatus = column("declare_status", JDBCType.INTEGER);

        public final SqlColumn<Integer> declareSettlementStatus = column("declare_settlement_status", JDBCType.INTEGER);

        public final SqlColumn<String> settlementNo = column("settlement_no", JDBCType.VARCHAR);

        public final SqlColumn<Integer> settlementStatus = column("settlement_status", JDBCType.INTEGER);

        public final SqlColumn<Date> sendRepairTime = column("send_repair_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> situationDesc = column("situation_desc", JDBCType.VARCHAR);

        public final SqlColumn<Integer> propertyStatus = column("property_status", JDBCType.INTEGER);

        public final SqlColumn<Integer> productLine = column("product_line", JDBCType.INTEGER);

        public final SqlColumn<Integer> subProductLine = column("sub_product_line", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> selfFundedAmount = column("self_funded_amount", JDBCType.DECIMAL);

        public final SqlColumn<String> continueDays = column("continue_days", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcRepairTask() {
            super("mtc_repair_task", MtcRepairTask::new);
        }
    }
}