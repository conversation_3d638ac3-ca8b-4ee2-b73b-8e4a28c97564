package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcVehicleInfoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    public static final MtcVehicleInfo mtcVehicleInfo = new MtcVehicleInfo();

    /**
     * Database Column Remarks:
     *   主键，自增
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.id")
    public static final SqlColumn<Long> id = mtcVehicleInfo.id;

    /**
     * Database Column Remarks:
     *   车架号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vin")
    public static final SqlColumn<String> vin = mtcVehicleInfo.vin;

    /**
     * Database Column Remarks:
     *   车牌号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vehicle_no")
    public static final SqlColumn<String> vehicleNo = mtcVehicleInfo.vehicleNo;

    /**
     * Database Column Remarks:
     *   车型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vehicle_model_id")
    public static final SqlColumn<Long> vehicleModelId = mtcVehicleInfo.vehicleModelId;

    /**
     * Database Column Remarks:
     *   维修时所属公司
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.vehicle_org_id")
    public static final SqlColumn<String> vehicleOrgId = mtcVehicleInfo.vehicleOrgId;

    /**
     * Database Column Remarks:
     *   维修时运营公司
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.operation_org_id")
    public static final SqlColumn<String> operationOrgId = mtcVehicleInfo.operationOrgId;

    /**
     * Database Column Remarks:
     *   维修时产品线
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.product_line")
    public static final SqlColumn<String> productLine = mtcVehicleInfo.productLine;

    /**
     * Database Column Remarks:
     *   维修时子产品线
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.sub_product_line")
    public static final SqlColumn<String> subProductLine = mtcVehicleInfo.subProductLine;

    /**
     * Database Column Remarks:
     *   维修时实际运营标签
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.fact_operate_tag")
    public static final SqlColumn<Integer> factOperateTag = mtcVehicleInfo.factOperateTag;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.create_time")
    public static final SqlColumn<Date> createTime = mtcVehicleInfo.createTime;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.create_by")
    public static final SqlColumn<String> createBy = mtcVehicleInfo.createBy;

    /**
     * Database Column Remarks:
     *   修改时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.update_time")
    public static final SqlColumn<Date> updateTime = mtcVehicleInfo.updateTime;

    /**
     * Database Column Remarks:
     *   修改人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_vehicle_info.update_by")
    public static final SqlColumn<String> updateBy = mtcVehicleInfo.updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_vehicle_info")
    public static final class MtcVehicleInfo extends AliasableSqlTable<MtcVehicleInfo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> vin = column("vin", JDBCType.VARCHAR);

        public final SqlColumn<String> vehicleNo = column("vehicle_no", JDBCType.VARCHAR);

        public final SqlColumn<Long> vehicleModelId = column("vehicle_model_id", JDBCType.BIGINT);

        public final SqlColumn<String> vehicleOrgId = column("vehicle_org_id", JDBCType.VARCHAR);

        public final SqlColumn<String> operationOrgId = column("operation_org_id", JDBCType.VARCHAR);

        public final SqlColumn<String> productLine = column("product_line", JDBCType.VARCHAR);

        public final SqlColumn<String> subProductLine = column("sub_product_line", JDBCType.VARCHAR);

        public final SqlColumn<Integer> factOperateTag = column("fact_operate_tag", JDBCType.INTEGER);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public MtcVehicleInfo() {
            super("mtc_vehicle_info", MtcVehicleInfo::new);
        }
    }
}