package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcReplaceItemDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcReplaceItem;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcReplaceItemMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    BasicColumn[] selectList = BasicColumn.columnList(id, orgId, partName, groupingId, originalFactoryPartNo, originalFactoryPartName, sysMarketPrice, sysMajorInPrice, localMarketPrice, localMajorInPrice, vehicleModelSeq, status, remark, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcReplaceItem> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcReplaceItemResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="org_id", property="orgId", jdbcType=JdbcType.VARCHAR),
        @Result(column="part_name", property="partName", jdbcType=JdbcType.VARCHAR),
        @Result(column="grouping_id", property="groupingId", jdbcType=JdbcType.BIGINT),
        @Result(column="original_factory_part_no", property="originalFactoryPartNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="original_factory_part_name", property="originalFactoryPartName", jdbcType=JdbcType.VARCHAR),
        @Result(column="sys_market_price", property="sysMarketPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="sys_major_in_price", property="sysMajorInPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="local_market_price", property="localMarketPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="local_major_in_price", property="localMajorInPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="vehicle_model_seq", property="vehicleModelSeq", jdbcType=JdbcType.BIGINT),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<MtcReplaceItem> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcReplaceItemResult")
    Optional<MtcReplaceItem> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcReplaceItem, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcReplaceItem, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    default int insert(MtcReplaceItem row) {
        return MyBatis3Utils.insert(this::insert, row, mtcReplaceItem, c ->
            c.map(orgId).toProperty("orgId")
            .map(partName).toProperty("partName")
            .map(groupingId).toProperty("groupingId")
            .map(originalFactoryPartNo).toProperty("originalFactoryPartNo")
            .map(originalFactoryPartName).toProperty("originalFactoryPartName")
            .map(sysMarketPrice).toProperty("sysMarketPrice")
            .map(sysMajorInPrice).toProperty("sysMajorInPrice")
            .map(localMarketPrice).toProperty("localMarketPrice")
            .map(localMajorInPrice).toProperty("localMajorInPrice")
            .map(vehicleModelSeq).toProperty("vehicleModelSeq")
            .map(status).toProperty("status")
            .map(remark).toProperty("remark")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    default int insertSelective(MtcReplaceItem row) {
        return MyBatis3Utils.insert(this::insert, row, mtcReplaceItem, c ->
            c.map(orgId).toPropertyWhenPresent("orgId", row::getOrgId)
            .map(partName).toPropertyWhenPresent("partName", row::getPartName)
            .map(groupingId).toPropertyWhenPresent("groupingId", row::getGroupingId)
            .map(originalFactoryPartNo).toPropertyWhenPresent("originalFactoryPartNo", row::getOriginalFactoryPartNo)
            .map(originalFactoryPartName).toPropertyWhenPresent("originalFactoryPartName", row::getOriginalFactoryPartName)
            .map(sysMarketPrice).toPropertyWhenPresent("sysMarketPrice", row::getSysMarketPrice)
            .map(sysMajorInPrice).toPropertyWhenPresent("sysMajorInPrice", row::getSysMajorInPrice)
            .map(localMarketPrice).toPropertyWhenPresent("localMarketPrice", row::getLocalMarketPrice)
            .map(localMajorInPrice).toPropertyWhenPresent("localMajorInPrice", row::getLocalMajorInPrice)
            .map(vehicleModelSeq).toPropertyWhenPresent("vehicleModelSeq", row::getVehicleModelSeq)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(remark).toPropertyWhenPresent("remark", row::getRemark)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    default Optional<MtcReplaceItem> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcReplaceItem, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    default List<MtcReplaceItem> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcReplaceItem, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    default List<MtcReplaceItem> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcReplaceItem, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    default Optional<MtcReplaceItem> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcReplaceItem, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcReplaceItem row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(orgId).equalTo(row::getOrgId)
                .set(partName).equalTo(row::getPartName)
                .set(groupingId).equalTo(row::getGroupingId)
                .set(originalFactoryPartNo).equalTo(row::getOriginalFactoryPartNo)
                .set(originalFactoryPartName).equalTo(row::getOriginalFactoryPartName)
                .set(sysMarketPrice).equalTo(row::getSysMarketPrice)
                .set(sysMajorInPrice).equalTo(row::getSysMajorInPrice)
                .set(localMarketPrice).equalTo(row::getLocalMarketPrice)
                .set(localMajorInPrice).equalTo(row::getLocalMajorInPrice)
                .set(vehicleModelSeq).equalTo(row::getVehicleModelSeq)
                .set(status).equalTo(row::getStatus)
                .set(remark).equalTo(row::getRemark)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcReplaceItem row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(orgId).equalToWhenPresent(row::getOrgId)
                .set(partName).equalToWhenPresent(row::getPartName)
                .set(groupingId).equalToWhenPresent(row::getGroupingId)
                .set(originalFactoryPartNo).equalToWhenPresent(row::getOriginalFactoryPartNo)
                .set(originalFactoryPartName).equalToWhenPresent(row::getOriginalFactoryPartName)
                .set(sysMarketPrice).equalToWhenPresent(row::getSysMarketPrice)
                .set(sysMajorInPrice).equalToWhenPresent(row::getSysMajorInPrice)
                .set(localMarketPrice).equalToWhenPresent(row::getLocalMarketPrice)
                .set(localMajorInPrice).equalToWhenPresent(row::getLocalMajorInPrice)
                .set(vehicleModelSeq).equalToWhenPresent(row::getVehicleModelSeq)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    default int updateByPrimaryKey(MtcReplaceItem row) {
        return update(c ->
            c.set(orgId).equalTo(row::getOrgId)
            .set(partName).equalTo(row::getPartName)
            .set(groupingId).equalTo(row::getGroupingId)
            .set(originalFactoryPartNo).equalTo(row::getOriginalFactoryPartNo)
            .set(originalFactoryPartName).equalTo(row::getOriginalFactoryPartName)
            .set(sysMarketPrice).equalTo(row::getSysMarketPrice)
            .set(sysMajorInPrice).equalTo(row::getSysMajorInPrice)
            .set(localMarketPrice).equalTo(row::getLocalMarketPrice)
            .set(localMajorInPrice).equalTo(row::getLocalMajorInPrice)
            .set(vehicleModelSeq).equalTo(row::getVehicleModelSeq)
            .set(status).equalTo(row::getStatus)
            .set(remark).equalTo(row::getRemark)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    default int updateByPrimaryKeySelective(MtcReplaceItem row) {
        return update(c ->
            c.set(orgId).equalToWhenPresent(row::getOrgId)
            .set(partName).equalToWhenPresent(row::getPartName)
            .set(groupingId).equalToWhenPresent(row::getGroupingId)
            .set(originalFactoryPartNo).equalToWhenPresent(row::getOriginalFactoryPartNo)
            .set(originalFactoryPartName).equalToWhenPresent(row::getOriginalFactoryPartName)
            .set(sysMarketPrice).equalToWhenPresent(row::getSysMarketPrice)
            .set(sysMajorInPrice).equalToWhenPresent(row::getSysMajorInPrice)
            .set(localMarketPrice).equalToWhenPresent(row::getLocalMarketPrice)
            .set(localMajorInPrice).equalToWhenPresent(row::getLocalMajorInPrice)
            .set(vehicleModelSeq).equalToWhenPresent(row::getVehicleModelSeq)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(remark).equalToWhenPresent(row::getRemark)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}