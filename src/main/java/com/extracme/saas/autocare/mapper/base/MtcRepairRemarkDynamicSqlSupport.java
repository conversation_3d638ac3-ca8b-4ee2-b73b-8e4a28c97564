package com.extracme.saas.autocare.mapper.base;

import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcRepairRemarkDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    public static final MtcRepairRemark mtcRepairRemark = new MtcRepairRemark();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_remark.id")
    public static final SqlColumn<Long> id = mtcRepairRemark.id;

    /**
     * Database Column Remarks:
     *   任务编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_remark.task_no")
    public static final SqlColumn<String> taskNo = mtcRepairRemark.taskNo;

    /**
     * Database Column Remarks:
     *   维修阶段 1:定损报价 2:核损核价(一级审核) 3:核损核价(审核通过) 4:车辆维修 5:车辆验收 6:轮胎任务
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_remark.repair_stage")
    public static final SqlColumn<Short> repairStage = mtcRepairRemark.repairStage;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_remark.remark")
    public static final SqlColumn<String> remark = mtcRepairRemark.remark;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_remark.create_by")
    public static final SqlColumn<String> createBy = mtcRepairRemark.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_remark.created_time")
    public static final SqlColumn<Date> createdTime = mtcRepairRemark.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_remark.update_by")
    public static final SqlColumn<String> updateBy = mtcRepairRemark.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_remark.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcRepairRemark.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_remark")
    public static final class MtcRepairRemark extends AliasableSqlTable<MtcRepairRemark> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> taskNo = column("task_no", JDBCType.VARCHAR);

        public final SqlColumn<Short> repairStage = column("repair_stage", JDBCType.DECIMAL);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcRepairRemark() {
            super("mtc_repair_remark", MtcRepairRemark::new);
        }
    }
}