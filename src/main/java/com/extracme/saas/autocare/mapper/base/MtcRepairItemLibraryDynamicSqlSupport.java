package com.extracme.saas.autocare.mapper.base;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcRepairItemLibraryDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    public static final MtcRepairItemLibrary mtcRepairItemLibrary = new MtcRepairItemLibrary();

    /**
     * Database Column Remarks:
     *   主键id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.id")
    public static final SqlColumn<Long> id = mtcRepairItemLibrary.id;

    /**
     * Database Column Remarks:
     *   序号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.serial_number")
    public static final SqlColumn<Integer> serialNumber = mtcRepairItemLibrary.serialNumber;

    /**
     * Database Column Remarks:
     *   项目编号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.item_no")
    public static final SqlColumn<String> itemNo = mtcRepairItemLibrary.itemNo;

    /**
     * Database Column Remarks:
     *   项目名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.item_name")
    public static final SqlColumn<String> itemName = mtcRepairItemLibrary.itemName;

    /**
     * Database Column Remarks:
     *   项目类型 1：保养 2：终端
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.item_type")
    public static final SqlColumn<Integer> itemType = mtcRepairItemLibrary.itemType;

    /**
     * Database Column Remarks:
     *   车型ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.vehicle_model_seq")
    public static final SqlColumn<Long> vehicleModelSeq = mtcRepairItemLibrary.vehicleModelSeq;

    /**
     * Database Column Remarks:
     *   车型名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.vehicle_model_info")
    public static final SqlColumn<String> vehicleModelInfo = mtcRepairItemLibrary.vehicleModelInfo;

    /**
     * Database Column Remarks:
     *   工时费全国市场价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.hour_fee_national_market_price")
    public static final SqlColumn<BigDecimal> hourFeeNationalMarketPrice = mtcRepairItemLibrary.hourFeeNationalMarketPrice;

    /**
     * Database Column Remarks:
     *   工时费全国上限
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.hour_fee_national_highest_price")
    public static final SqlColumn<BigDecimal> hourFeeNationalHighestPrice = mtcRepairItemLibrary.hourFeeNationalHighestPrice;

    /**
     * Database Column Remarks:
     *   材料费全国市场价
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.material_cost_national_market_price")
    public static final SqlColumn<BigDecimal> materialCostNationalMarketPrice = mtcRepairItemLibrary.materialCostNationalMarketPrice;

    /**
     * Database Column Remarks:
     *   材料费全国上限
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.material_cost_national_highest_price")
    public static final SqlColumn<BigDecimal> materialCostNationalHighestPrice = mtcRepairItemLibrary.materialCostNationalHighestPrice;

    /**
     * Database Column Remarks:
     *   保养周期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.maintenance_cycle")
    public static final SqlColumn<Integer> maintenanceCycle = mtcRepairItemLibrary.maintenanceCycle;

    /**
     * Database Column Remarks:
     *   保养里程数
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.maintenance_mileage")
    public static final SqlColumn<BigDecimal> maintenanceMileage = mtcRepairItemLibrary.maintenanceMileage;

    /**
     * Database Column Remarks:
     *   是否常用 0：否 1：是 
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.is_commonly_used")
    public static final SqlColumn<Integer> isCommonlyUsed = mtcRepairItemLibrary.isCommonlyUsed;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.remark")
    public static final SqlColumn<String> remark = mtcRepairItemLibrary.remark;

    /**
     * Database Column Remarks:
     *   状态(0：无效  1：有效)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.status")
    public static final SqlColumn<Integer> status = mtcRepairItemLibrary.status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.misc_Desc")
    public static final SqlColumn<String> miscDesc = mtcRepairItemLibrary.miscDesc;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.create_by")
    public static final SqlColumn<String> createBy = mtcRepairItemLibrary.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.created_time")
    public static final SqlColumn<Date> createdTime = mtcRepairItemLibrary.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.update_by")
    public static final SqlColumn<String> updateBy = mtcRepairItemLibrary.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_repair_item_library.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcRepairItemLibrary.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    public static final class MtcRepairItemLibrary extends AliasableSqlTable<MtcRepairItemLibrary> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Integer> serialNumber = column("serial_number", JDBCType.INTEGER);

        public final SqlColumn<String> itemNo = column("item_no", JDBCType.VARCHAR);

        public final SqlColumn<String> itemName = column("item_name", JDBCType.VARCHAR);

        public final SqlColumn<Integer> itemType = column("item_type", JDBCType.INTEGER);

        public final SqlColumn<Long> vehicleModelSeq = column("vehicle_model_seq", JDBCType.BIGINT);

        public final SqlColumn<String> vehicleModelInfo = column("vehicle_model_info", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> hourFeeNationalMarketPrice = column("hour_fee_national_market_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> hourFeeNationalHighestPrice = column("hour_fee_national_highest_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> materialCostNationalMarketPrice = column("material_cost_national_market_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> materialCostNationalHighestPrice = column("material_cost_national_highest_price", JDBCType.DECIMAL);

        public final SqlColumn<Integer> maintenanceCycle = column("maintenance_cycle", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> maintenanceMileage = column("maintenance_mileage", JDBCType.DECIMAL);

        public final SqlColumn<Integer> isCommonlyUsed = column("is_commonly_used", JDBCType.INTEGER);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<String> miscDesc = column("misc_Desc", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcRepairItemLibrary() {
            super("mtc_repair_item_library", MtcRepairItemLibrary::new);
        }
    }
}