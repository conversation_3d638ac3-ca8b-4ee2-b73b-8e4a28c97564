package com.extracme.saas.autocare.mapper.base;

import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import javax.annotation.Generated;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MtcReplaceItemDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    public static final MtcReplaceItem mtcReplaceItem = new MtcReplaceItem();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_replace_item.id")
    public static final SqlColumn<Long> id = mtcReplaceItem.id;

    /**
     * Database Column Remarks:
     *   组织机构ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_replace_item.org_id")
    public static final SqlColumn<String> orgId = mtcReplaceItem.orgId;

    /**
     * Database Column Remarks:
     *   零件名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_replace_item.part_name")
    public static final SqlColumn<String> partName = mtcReplaceItem.partName;

    /**
     * Database Column Remarks:
     *   零件分组ID
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_replace_item.grouping_id")
    public static final SqlColumn<Long> groupingId = mtcReplaceItem.groupingId;

    /**
     * Database Column Remarks:
     *   原厂零件号
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_replace_item.original_factory_part_no")
    public static final SqlColumn<String> originalFactoryPartNo = mtcReplaceItem.originalFactoryPartNo;

    /**
     * Database Column Remarks:
     *   原厂零件名称
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_replace_item.original_factory_part_name")
    public static final SqlColumn<String> originalFactoryPartName = mtcReplaceItem.originalFactoryPartName;

    /**
     * Database Column Remarks:
     *   系统市场价(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_replace_item.sys_market_price")
    public static final SqlColumn<BigDecimal> sysMarketPrice = mtcReplaceItem.sysMarketPrice;

    /**
     * Database Column Remarks:
     *   系统专修价(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_replace_item.sys_major_in_price")
    public static final SqlColumn<BigDecimal> sysMajorInPrice = mtcReplaceItem.sysMajorInPrice;

    /**
     * Database Column Remarks:
     *   本地市场价(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_replace_item.local_market_price")
    public static final SqlColumn<BigDecimal> localMarketPrice = mtcReplaceItem.localMarketPrice;

    /**
     * Database Column Remarks:
     *   本地专修价(元)
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_replace_item.local_major_in_price")
    public static final SqlColumn<BigDecimal> localMajorInPrice = mtcReplaceItem.localMajorInPrice;

    /**
     * Database Column Remarks:
     *   车型SEQ
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_replace_item.vehicle_model_seq")
    public static final SqlColumn<Long> vehicleModelSeq = mtcReplaceItem.vehicleModelSeq;

    /**
     * Database Column Remarks:
     *   状态（1：有效 0：无效）
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_replace_item.status")
    public static final SqlColumn<Integer> status = mtcReplaceItem.status;

    /**
     * Database Column Remarks:
     *   备注
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_replace_item.remark")
    public static final SqlColumn<String> remark = mtcReplaceItem.remark;

    /**
     * Database Column Remarks:
     *   创建人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_replace_item.create_by")
    public static final SqlColumn<String> createBy = mtcReplaceItem.createBy;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_replace_item.created_time")
    public static final SqlColumn<Date> createdTime = mtcReplaceItem.createdTime;

    /**
     * Database Column Remarks:
     *   更新人
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_replace_item.update_by")
    public static final SqlColumn<String> updateBy = mtcReplaceItem.updateBy;

    /**
     * Database Column Remarks:
     *   更新时间
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: mtc_replace_item.updated_time")
    public static final SqlColumn<Date> updatedTime = mtcReplaceItem.updatedTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_replace_item")
    public static final class MtcReplaceItem extends AliasableSqlTable<MtcReplaceItem> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> orgId = column("org_id", JDBCType.VARCHAR);

        public final SqlColumn<String> partName = column("part_name", JDBCType.VARCHAR);

        public final SqlColumn<Long> groupingId = column("grouping_id", JDBCType.BIGINT);

        public final SqlColumn<String> originalFactoryPartNo = column("original_factory_part_no", JDBCType.VARCHAR);

        public final SqlColumn<String> originalFactoryPartName = column("original_factory_part_name", JDBCType.VARCHAR);

        public final SqlColumn<BigDecimal> sysMarketPrice = column("sys_market_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> sysMajorInPrice = column("sys_major_in_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> localMarketPrice = column("local_market_price", JDBCType.DECIMAL);

        public final SqlColumn<BigDecimal> localMajorInPrice = column("local_major_in_price", JDBCType.DECIMAL);

        public final SqlColumn<Long> vehicleModelSeq = column("vehicle_model_seq", JDBCType.BIGINT);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public final SqlColumn<String> remark = column("remark", JDBCType.VARCHAR);

        public final SqlColumn<String> createBy = column("create_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> updateBy = column("update_by", JDBCType.VARCHAR);

        public final SqlColumn<Date> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public MtcReplaceItem() {
            super("mtc_replace_item", MtcReplaceItem::new);
        }
    }
}