package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcRepairTaskDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcRepairTaskMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    BasicColumn[] selectList = BasicColumn.columnList(id, taskNo, taskType, orgId, orgName, operateOrgId, operateOrgName, vehicleNo, vehicleModelSeq, vehicleModelInfo, vin, insuranceCompanyName, repairTypeId, repairTypeName, repairGrade, repairDepotId, repairDepotName, repairDepotOrgId, repairDepotSapCode, sapSendStatus, sapSyncFlag, sapSuccessTime, partsLibraryType, taskCreateTime, taskInflowTime, vehicleReciveTime, vehicleCheckTime, vehicleRepairTime, reassignmentPassTime, settleClosingTime, expectedRepairDays, expectedRepairComplete, accidentReportNumber, repairFlag, terminalId, totalMileage, terminalMileage, associatedOrder, orderType, relateType, orderRemark, violationName, violationTelNo, vehicleClientMaintenanceFee, clientInspectTag, clientUpkeepTag, authId, noDeductiblesFlag, serviceType, serviceContent, driverName, driverTel, routingInspectionName, routingInspectionTel, damagedPartDescribe, accidentDescribe, trailerFlag, repairReviewTotalAmount, repairReplaceTotalAmount, repairRepairTotalAmount, repairInsuranceTotalAmount, vehicleReplaceTotalAmount, vehicleRepairTotalAmount, vehicleInsuranceTotalAmount, vehicleManageViewFlag, resurveyFlag, resurveyPart, recoveryFlag, dutySituation, recoveryAmount, insuranceAmount, accDepAmount, outageLossAmount, vehicleLossAmount, trailerRescueAmount, maintainAmount, lossOrderAmount, reassignmentRepairOrgId, reassignmentReasons, reassignmentRejectReasons, verificationRejectReasons, verificationRejectReasonsDetail, verificationRejectLevel, checkResultFlag, checkUnqualifiedReason, confirmType, maintainToRepairFlag, verificationLossTaskOperId, examineLevel, verificationLossCheckTime, verificationLossCheckId, overTimeReasons, repairTotalAmountFirst, nuclearLossReversionFlag, remark, tirenumber, tireUnitPrice, tireBrand, associatedTaskNo, insuranceFlag, advancedAuditLeve, alreadyIncomingAmount, alreadyPayAmount, waitPayAmount, mustClaimAmount, mustPayAmount, vehicleRepairCostAmount, repairSettleAmount, repairFaxSettleAmount, transferFlag, rationalIndemnityCnt, accidentDayTime, deductFlag, origin, renttype, factOperateTag, takeUserName, takeUserPhone, takeVoucher, synTakeTime, closeReason, claimsFlag, estimatedClaimAmount, carDamageType, vehicleScrapeValue, reviewToSelFeeFlag, confirmCarDamageType, accidentNo, insurancePreReviewTime, insurancePreReviewLevel, preReviewVehicleScrapeValue, preReviewVehicleScrapeTime, isUsedApplets, criteriaId, custPaysDirect, custAmount, userAssumedAmount, notUserAssumedAmount, syncClaimFlag, taxRate, repairDepotType, declareNo, declareStatus, declareSettlementStatus, settlementNo, settlementStatus, sendRepairTime, situationDesc, propertyStatus, productLine, subProductLine, selfFundedAmount, continueDays, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcRepairTask> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcRepairTaskResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="task_no", property="taskNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="task_type", property="taskType", jdbcType=JdbcType.DECIMAL),
        @Result(column="org_id", property="orgId", jdbcType=JdbcType.VARCHAR),
        @Result(column="org_name", property="orgName", jdbcType=JdbcType.VARCHAR),
        @Result(column="operate_org_id", property="operateOrgId", jdbcType=JdbcType.VARCHAR),
        @Result(column="operate_org_name", property="operateOrgName", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_no", property="vehicleNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_model_seq", property="vehicleModelSeq", jdbcType=JdbcType.BIGINT),
        @Result(column="vehicle_model_info", property="vehicleModelInfo", jdbcType=JdbcType.VARCHAR),
        @Result(column="vin", property="vin", jdbcType=JdbcType.VARCHAR),
        @Result(column="insurance_company_name", property="insuranceCompanyName", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_type_id", property="repairTypeId", jdbcType=JdbcType.INTEGER),
        @Result(column="repair_type_name", property="repairTypeName", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_grade", property="repairGrade", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_depot_id", property="repairDepotId", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_depot_name", property="repairDepotName", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_depot_org_id", property="repairDepotOrgId", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_depot_sap_code", property="repairDepotSapCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="sap_send_status", property="sapSendStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="sap_sync_flag", property="sapSyncFlag", jdbcType=JdbcType.INTEGER),
        @Result(column="sap_success_time", property="sapSuccessTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="parts_library_type", property="partsLibraryType", jdbcType=JdbcType.INTEGER),
        @Result(column="task_create_time", property="taskCreateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="task_inflow_time", property="taskInflowTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="vehicle_recive_time", property="vehicleReciveTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="vehicle_check_time", property="vehicleCheckTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="vehicle_repair_time", property="vehicleRepairTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="reassignment_pass_time", property="reassignmentPassTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="settle_closing_time", property="settleClosingTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="expected_repair_days", property="expectedRepairDays", jdbcType=JdbcType.BIGINT),
        @Result(column="expected_repair_complete", property="expectedRepairComplete", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="accident_report_number", property="accidentReportNumber", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_flag", property="repairFlag", jdbcType=JdbcType.DECIMAL),
        @Result(column="terminal_id", property="terminalId", jdbcType=JdbcType.VARCHAR),
        @Result(column="total_mileage", property="totalMileage", jdbcType=JdbcType.DECIMAL),
        @Result(column="terminal_mileage", property="terminalMileage", jdbcType=JdbcType.VARCHAR),
        @Result(column="associated_order", property="associatedOrder", jdbcType=JdbcType.VARCHAR),
        @Result(column="order_type", property="orderType", jdbcType=JdbcType.INTEGER),
        @Result(column="relate_type", property="relateType", jdbcType=JdbcType.INTEGER),
        @Result(column="order_remark", property="orderRemark", jdbcType=JdbcType.VARCHAR),
        @Result(column="violation_name", property="violationName", jdbcType=JdbcType.VARCHAR),
        @Result(column="violation_tel_no", property="violationTelNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="vehicle_client_maintenance_fee", property="vehicleClientMaintenanceFee", jdbcType=JdbcType.TINYINT),
        @Result(column="client_inspect_tag", property="clientInspectTag", jdbcType=JdbcType.TINYINT),
        @Result(column="client_upkeep_tag", property="clientUpkeepTag", jdbcType=JdbcType.TINYINT),
        @Result(column="auth_id", property="authId", jdbcType=JdbcType.VARCHAR),
        @Result(column="no_deductibles_flag", property="noDeductiblesFlag", jdbcType=JdbcType.INTEGER),
        @Result(column="service_type", property="serviceType", jdbcType=JdbcType.INTEGER),
        @Result(column="service_content", property="serviceContent", jdbcType=JdbcType.VARCHAR),
        @Result(column="driver_name", property="driverName", jdbcType=JdbcType.VARCHAR),
        @Result(column="driver_tel", property="driverTel", jdbcType=JdbcType.VARCHAR),
        @Result(column="routing_inspection_name", property="routingInspectionName", jdbcType=JdbcType.VARCHAR),
        @Result(column="routing_inspection_tel", property="routingInspectionTel", jdbcType=JdbcType.VARCHAR),
        @Result(column="damaged_part_describe", property="damagedPartDescribe", jdbcType=JdbcType.VARCHAR),
        @Result(column="accident_describe", property="accidentDescribe", jdbcType=JdbcType.VARCHAR),
        @Result(column="trailer_flag", property="trailerFlag", jdbcType=JdbcType.DECIMAL),
        @Result(column="repair_review_total_amount", property="repairReviewTotalAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="repair_replace_total_amount", property="repairReplaceTotalAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="repair_repair_total_amount", property="repairRepairTotalAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="repair_insurance_total_amount", property="repairInsuranceTotalAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="vehicle_replace_total_amount", property="vehicleReplaceTotalAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="vehicle_repair_total_amount", property="vehicleRepairTotalAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="vehicle_insurance_total_amount", property="vehicleInsuranceTotalAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="vehicle_manage_view_flag", property="vehicleManageViewFlag", jdbcType=JdbcType.DECIMAL),
        @Result(column="resurvey_flag", property="resurveyFlag", jdbcType=JdbcType.DECIMAL),
        @Result(column="resurvey_part", property="resurveyPart", jdbcType=JdbcType.VARCHAR),
        @Result(column="recovery_flag", property="recoveryFlag", jdbcType=JdbcType.DECIMAL),
        @Result(column="duty_situation", property="dutySituation", jdbcType=JdbcType.INTEGER),
        @Result(column="recovery_amount", property="recoveryAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="insurance_amount", property="insuranceAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="acc_dep_amount", property="accDepAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="outage_loss_amount", property="outageLossAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="vehicle_loss_amount", property="vehicleLossAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="trailer_rescue_amount", property="trailerRescueAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="maintain_amount", property="maintainAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="loss_order_amount", property="lossOrderAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="reassignment_repair_org_id", property="reassignmentRepairOrgId", jdbcType=JdbcType.VARCHAR),
        @Result(column="reassignment_reasons", property="reassignmentReasons", jdbcType=JdbcType.VARCHAR),
        @Result(column="reassignment_reject_reasons", property="reassignmentRejectReasons", jdbcType=JdbcType.VARCHAR),
        @Result(column="verification_reject_reasons", property="verificationRejectReasons", jdbcType=JdbcType.VARCHAR),
        @Result(column="verification_reject_reasons_detail", property="verificationRejectReasonsDetail", jdbcType=JdbcType.VARCHAR),
        @Result(column="verification_reject_level", property="verificationRejectLevel", jdbcType=JdbcType.VARCHAR),
        @Result(column="check_result_flag", property="checkResultFlag", jdbcType=JdbcType.DECIMAL),
        @Result(column="check_unqualified_reason", property="checkUnqualifiedReason", jdbcType=JdbcType.VARCHAR),
        @Result(column="confirm_type", property="confirmType", jdbcType=JdbcType.INTEGER),
        @Result(column="maintain_to_repair_flag", property="maintainToRepairFlag", jdbcType=JdbcType.DECIMAL),
        @Result(column="verification_loss_task_oper_id", property="verificationLossTaskOperId", jdbcType=JdbcType.BIGINT),
        @Result(column="examine_level", property="examineLevel", jdbcType=JdbcType.DECIMAL),
        @Result(column="verification_loss_check_time", property="verificationLossCheckTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="verification_loss_check_id", property="verificationLossCheckId", jdbcType=JdbcType.BIGINT),
        @Result(column="over_time_reasons", property="overTimeReasons", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_total_amount_first", property="repairTotalAmountFirst", jdbcType=JdbcType.DECIMAL),
        @Result(column="nuclear_loss_reversion_flag", property="nuclearLossReversionFlag", jdbcType=JdbcType.INTEGER),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="tireNumber", property="tirenumber", jdbcType=JdbcType.BIGINT),
        @Result(column="tire_unit_price", property="tireUnitPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="tire_brand", property="tireBrand", jdbcType=JdbcType.VARCHAR),
        @Result(column="associated_task_no", property="associatedTaskNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="insurance_flag", property="insuranceFlag", jdbcType=JdbcType.INTEGER),
        @Result(column="advanced_audit_leve", property="advancedAuditLeve", jdbcType=JdbcType.INTEGER),
        @Result(column="already_incoming_amount", property="alreadyIncomingAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="already_pay_amount", property="alreadyPayAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="wait_pay_amount", property="waitPayAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="must_claim_amount", property="mustClaimAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="must_pay_amount", property="mustPayAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="vehicle_repair_cost_amount", property="vehicleRepairCostAmount", jdbcType=JdbcType.DOUBLE),
        @Result(column="repair_settle_amount", property="repairSettleAmount", jdbcType=JdbcType.DOUBLE),
        @Result(column="repair_fax_settle_amount", property="repairFaxSettleAmount", jdbcType=JdbcType.DOUBLE),
        @Result(column="transfer_flag", property="transferFlag", jdbcType=JdbcType.INTEGER),
        @Result(column="rational_indemnity_cnt", property="rationalIndemnityCnt", jdbcType=JdbcType.INTEGER),
        @Result(column="accident_day_time", property="accidentDayTime", jdbcType=JdbcType.DATE),
        @Result(column="deduct_flag", property="deductFlag", jdbcType=JdbcType.INTEGER),
        @Result(column="origin", property="origin", jdbcType=JdbcType.INTEGER),
        @Result(column="renttype", property="renttype", jdbcType=JdbcType.INTEGER),
        @Result(column="fact_operate_tag", property="factOperateTag", jdbcType=JdbcType.INTEGER),
        @Result(column="take_user_name", property="takeUserName", jdbcType=JdbcType.VARCHAR),
        @Result(column="take_user_phone", property="takeUserPhone", jdbcType=JdbcType.VARCHAR),
        @Result(column="take_voucher", property="takeVoucher", jdbcType=JdbcType.VARCHAR),
        @Result(column="syn_take_time", property="synTakeTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="close_reason", property="closeReason", jdbcType=JdbcType.VARCHAR),
        @Result(column="claims_flag", property="claimsFlag", jdbcType=JdbcType.INTEGER),
        @Result(column="estimated_claim_amount", property="estimatedClaimAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="car_damage_type", property="carDamageType", jdbcType=JdbcType.INTEGER),
        @Result(column="vehicle_scrape_value", property="vehicleScrapeValue", jdbcType=JdbcType.DECIMAL),
        @Result(column="review_to_sel_fee_flag", property="reviewToSelFeeFlag", jdbcType=JdbcType.INTEGER),
        @Result(column="confirm_car_damage_type", property="confirmCarDamageType", jdbcType=JdbcType.INTEGER),
        @Result(column="accident_no", property="accidentNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="insurance_pre_review_time", property="insurancePreReviewTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="insurance_pre_review_level", property="insurancePreReviewLevel", jdbcType=JdbcType.INTEGER),
        @Result(column="pre_review_vehicle_scrape_value", property="preReviewVehicleScrapeValue", jdbcType=JdbcType.DECIMAL),
        @Result(column="pre_review_vehicle_scrape_time", property="preReviewVehicleScrapeTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="is_used_applets", property="isUsedApplets", jdbcType=JdbcType.VARCHAR),
        @Result(column="criteria_id", property="criteriaId", jdbcType=JdbcType.BIGINT),
        @Result(column="cust_pays_direct", property="custPaysDirect", jdbcType=JdbcType.INTEGER),
        @Result(column="cust_amount", property="custAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="user_assumed_amount", property="userAssumedAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="not_user_assumed_amount", property="notUserAssumedAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="sync_claim_flag", property="syncClaimFlag", jdbcType=JdbcType.INTEGER),
        @Result(column="tax_rate", property="taxRate", jdbcType=JdbcType.VARCHAR),
        @Result(column="repair_depot_type", property="repairDepotType", jdbcType=JdbcType.INTEGER),
        @Result(column="declare_no", property="declareNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="declare_status", property="declareStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="declare_settlement_status", property="declareSettlementStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="settlement_no", property="settlementNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="settlement_status", property="settlementStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="send_repair_time", property="sendRepairTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="situation_desc", property="situationDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="property_status", property="propertyStatus", jdbcType=JdbcType.INTEGER),
        @Result(column="product_line", property="productLine", jdbcType=JdbcType.INTEGER),
        @Result(column="sub_product_line", property="subProductLine", jdbcType=JdbcType.INTEGER),
        @Result(column="self_funded_amount", property="selfFundedAmount", jdbcType=JdbcType.DECIMAL),
        @Result(column="continue_days", property="continueDays", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<MtcRepairTask> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcRepairTaskResult")
    Optional<MtcRepairTask> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcRepairTask, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcRepairTask, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    default int insert(MtcRepairTask row) {
        return MyBatis3Utils.insert(this::insert, row, mtcRepairTask, c ->
            c.map(taskNo).toProperty("taskNo")
            .map(taskType).toProperty("taskType")
            .map(orgId).toProperty("orgId")
            .map(orgName).toProperty("orgName")
            .map(operateOrgId).toProperty("operateOrgId")
            .map(operateOrgName).toProperty("operateOrgName")
            .map(vehicleNo).toProperty("vehicleNo")
            .map(vehicleModelSeq).toProperty("vehicleModelSeq")
            .map(vehicleModelInfo).toProperty("vehicleModelInfo")
            .map(vin).toProperty("vin")
            .map(insuranceCompanyName).toProperty("insuranceCompanyName")
            .map(repairTypeId).toProperty("repairTypeId")
            .map(repairTypeName).toProperty("repairTypeName")
            .map(repairGrade).toProperty("repairGrade")
            .map(repairDepotId).toProperty("repairDepotId")
            .map(repairDepotName).toProperty("repairDepotName")
            .map(repairDepotOrgId).toProperty("repairDepotOrgId")
            .map(repairDepotSapCode).toProperty("repairDepotSapCode")
            .map(sapSendStatus).toProperty("sapSendStatus")
            .map(sapSyncFlag).toProperty("sapSyncFlag")
            .map(sapSuccessTime).toProperty("sapSuccessTime")
            .map(partsLibraryType).toProperty("partsLibraryType")
            .map(taskCreateTime).toProperty("taskCreateTime")
            .map(taskInflowTime).toProperty("taskInflowTime")
            .map(vehicleReciveTime).toProperty("vehicleReciveTime")
            .map(vehicleCheckTime).toProperty("vehicleCheckTime")
            .map(vehicleRepairTime).toProperty("vehicleRepairTime")
            .map(reassignmentPassTime).toProperty("reassignmentPassTime")
            .map(settleClosingTime).toProperty("settleClosingTime")
            .map(expectedRepairDays).toProperty("expectedRepairDays")
            .map(expectedRepairComplete).toProperty("expectedRepairComplete")
            .map(accidentReportNumber).toProperty("accidentReportNumber")
            .map(repairFlag).toProperty("repairFlag")
            .map(terminalId).toProperty("terminalId")
            .map(totalMileage).toProperty("totalMileage")
            .map(terminalMileage).toProperty("terminalMileage")
            .map(associatedOrder).toProperty("associatedOrder")
            .map(orderType).toProperty("orderType")
            .map(relateType).toProperty("relateType")
            .map(orderRemark).toProperty("orderRemark")
            .map(violationName).toProperty("violationName")
            .map(violationTelNo).toProperty("violationTelNo")
            .map(vehicleClientMaintenanceFee).toProperty("vehicleClientMaintenanceFee")
            .map(clientInspectTag).toProperty("clientInspectTag")
            .map(clientUpkeepTag).toProperty("clientUpkeepTag")
            .map(authId).toProperty("authId")
            .map(noDeductiblesFlag).toProperty("noDeductiblesFlag")
            .map(serviceType).toProperty("serviceType")
            .map(serviceContent).toProperty("serviceContent")
            .map(driverName).toProperty("driverName")
            .map(driverTel).toProperty("driverTel")
            .map(routingInspectionName).toProperty("routingInspectionName")
            .map(routingInspectionTel).toProperty("routingInspectionTel")
            .map(damagedPartDescribe).toProperty("damagedPartDescribe")
            .map(accidentDescribe).toProperty("accidentDescribe")
            .map(trailerFlag).toProperty("trailerFlag")
            .map(repairReviewTotalAmount).toProperty("repairReviewTotalAmount")
            .map(repairReplaceTotalAmount).toProperty("repairReplaceTotalAmount")
            .map(repairRepairTotalAmount).toProperty("repairRepairTotalAmount")
            .map(repairInsuranceTotalAmount).toProperty("repairInsuranceTotalAmount")
            .map(vehicleReplaceTotalAmount).toProperty("vehicleReplaceTotalAmount")
            .map(vehicleRepairTotalAmount).toProperty("vehicleRepairTotalAmount")
            .map(vehicleInsuranceTotalAmount).toProperty("vehicleInsuranceTotalAmount")
            .map(vehicleManageViewFlag).toProperty("vehicleManageViewFlag")
            .map(resurveyFlag).toProperty("resurveyFlag")
            .map(resurveyPart).toProperty("resurveyPart")
            .map(recoveryFlag).toProperty("recoveryFlag")
            .map(dutySituation).toProperty("dutySituation")
            .map(recoveryAmount).toProperty("recoveryAmount")
            .map(insuranceAmount).toProperty("insuranceAmount")
            .map(accDepAmount).toProperty("accDepAmount")
            .map(outageLossAmount).toProperty("outageLossAmount")
            .map(vehicleLossAmount).toProperty("vehicleLossAmount")
            .map(trailerRescueAmount).toProperty("trailerRescueAmount")
            .map(maintainAmount).toProperty("maintainAmount")
            .map(lossOrderAmount).toProperty("lossOrderAmount")
            .map(reassignmentRepairOrgId).toProperty("reassignmentRepairOrgId")
            .map(reassignmentReasons).toProperty("reassignmentReasons")
            .map(reassignmentRejectReasons).toProperty("reassignmentRejectReasons")
            .map(verificationRejectReasons).toProperty("verificationRejectReasons")
            .map(verificationRejectReasonsDetail).toProperty("verificationRejectReasonsDetail")
            .map(verificationRejectLevel).toProperty("verificationRejectLevel")
            .map(checkResultFlag).toProperty("checkResultFlag")
            .map(checkUnqualifiedReason).toProperty("checkUnqualifiedReason")
            .map(confirmType).toProperty("confirmType")
            .map(maintainToRepairFlag).toProperty("maintainToRepairFlag")
            .map(verificationLossTaskOperId).toProperty("verificationLossTaskOperId")
            .map(examineLevel).toProperty("examineLevel")
            .map(verificationLossCheckTime).toProperty("verificationLossCheckTime")
            .map(verificationLossCheckId).toProperty("verificationLossCheckId")
            .map(overTimeReasons).toProperty("overTimeReasons")
            .map(repairTotalAmountFirst).toProperty("repairTotalAmountFirst")
            .map(nuclearLossReversionFlag).toProperty("nuclearLossReversionFlag")
            .map(remark).toProperty("remark")
            .map(tirenumber).toProperty("tirenumber")
            .map(tireUnitPrice).toProperty("tireUnitPrice")
            .map(tireBrand).toProperty("tireBrand")
            .map(associatedTaskNo).toProperty("associatedTaskNo")
            .map(insuranceFlag).toProperty("insuranceFlag")
            .map(advancedAuditLeve).toProperty("advancedAuditLeve")
            .map(alreadyIncomingAmount).toProperty("alreadyIncomingAmount")
            .map(alreadyPayAmount).toProperty("alreadyPayAmount")
            .map(waitPayAmount).toProperty("waitPayAmount")
            .map(mustClaimAmount).toProperty("mustClaimAmount")
            .map(mustPayAmount).toProperty("mustPayAmount")
            .map(vehicleRepairCostAmount).toProperty("vehicleRepairCostAmount")
            .map(repairSettleAmount).toProperty("repairSettleAmount")
            .map(repairFaxSettleAmount).toProperty("repairFaxSettleAmount")
            .map(transferFlag).toProperty("transferFlag")
            .map(rationalIndemnityCnt).toProperty("rationalIndemnityCnt")
            .map(accidentDayTime).toProperty("accidentDayTime")
            .map(deductFlag).toProperty("deductFlag")
            .map(origin).toProperty("origin")
            .map(renttype).toProperty("renttype")
            .map(factOperateTag).toProperty("factOperateTag")
            .map(takeUserName).toProperty("takeUserName")
            .map(takeUserPhone).toProperty("takeUserPhone")
            .map(takeVoucher).toProperty("takeVoucher")
            .map(synTakeTime).toProperty("synTakeTime")
            .map(closeReason).toProperty("closeReason")
            .map(claimsFlag).toProperty("claimsFlag")
            .map(estimatedClaimAmount).toProperty("estimatedClaimAmount")
            .map(carDamageType).toProperty("carDamageType")
            .map(vehicleScrapeValue).toProperty("vehicleScrapeValue")
            .map(reviewToSelFeeFlag).toProperty("reviewToSelFeeFlag")
            .map(confirmCarDamageType).toProperty("confirmCarDamageType")
            .map(accidentNo).toProperty("accidentNo")
            .map(insurancePreReviewTime).toProperty("insurancePreReviewTime")
            .map(insurancePreReviewLevel).toProperty("insurancePreReviewLevel")
            .map(preReviewVehicleScrapeValue).toProperty("preReviewVehicleScrapeValue")
            .map(preReviewVehicleScrapeTime).toProperty("preReviewVehicleScrapeTime")
            .map(isUsedApplets).toProperty("isUsedApplets")
            .map(criteriaId).toProperty("criteriaId")
            .map(custPaysDirect).toProperty("custPaysDirect")
            .map(custAmount).toProperty("custAmount")
            .map(userAssumedAmount).toProperty("userAssumedAmount")
            .map(notUserAssumedAmount).toProperty("notUserAssumedAmount")
            .map(syncClaimFlag).toProperty("syncClaimFlag")
            .map(taxRate).toProperty("taxRate")
            .map(repairDepotType).toProperty("repairDepotType")
            .map(declareNo).toProperty("declareNo")
            .map(declareStatus).toProperty("declareStatus")
            .map(declareSettlementStatus).toProperty("declareSettlementStatus")
            .map(settlementNo).toProperty("settlementNo")
            .map(settlementStatus).toProperty("settlementStatus")
            .map(sendRepairTime).toProperty("sendRepairTime")
            .map(situationDesc).toProperty("situationDesc")
            .map(propertyStatus).toProperty("propertyStatus")
            .map(productLine).toProperty("productLine")
            .map(subProductLine).toProperty("subProductLine")
            .map(selfFundedAmount).toProperty("selfFundedAmount")
            .map(continueDays).toProperty("continueDays")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    default int insertSelective(MtcRepairTask row) {
        return MyBatis3Utils.insert(this::insert, row, mtcRepairTask, c ->
            c.map(taskNo).toPropertyWhenPresent("taskNo", row::getTaskNo)
            .map(taskType).toPropertyWhenPresent("taskType", row::getTaskType)
            .map(orgId).toPropertyWhenPresent("orgId", row::getOrgId)
            .map(orgName).toPropertyWhenPresent("orgName", row::getOrgName)
            .map(operateOrgId).toPropertyWhenPresent("operateOrgId", row::getOperateOrgId)
            .map(operateOrgName).toPropertyWhenPresent("operateOrgName", row::getOperateOrgName)
            .map(vehicleNo).toPropertyWhenPresent("vehicleNo", row::getVehicleNo)
            .map(vehicleModelSeq).toPropertyWhenPresent("vehicleModelSeq", row::getVehicleModelSeq)
            .map(vehicleModelInfo).toPropertyWhenPresent("vehicleModelInfo", row::getVehicleModelInfo)
            .map(vin).toPropertyWhenPresent("vin", row::getVin)
            .map(insuranceCompanyName).toPropertyWhenPresent("insuranceCompanyName", row::getInsuranceCompanyName)
            .map(repairTypeId).toPropertyWhenPresent("repairTypeId", row::getRepairTypeId)
            .map(repairTypeName).toPropertyWhenPresent("repairTypeName", row::getRepairTypeName)
            .map(repairGrade).toPropertyWhenPresent("repairGrade", row::getRepairGrade)
            .map(repairDepotId).toPropertyWhenPresent("repairDepotId", row::getRepairDepotId)
            .map(repairDepotName).toPropertyWhenPresent("repairDepotName", row::getRepairDepotName)
            .map(repairDepotOrgId).toPropertyWhenPresent("repairDepotOrgId", row::getRepairDepotOrgId)
            .map(repairDepotSapCode).toPropertyWhenPresent("repairDepotSapCode", row::getRepairDepotSapCode)
            .map(sapSendStatus).toPropertyWhenPresent("sapSendStatus", row::getSapSendStatus)
            .map(sapSyncFlag).toPropertyWhenPresent("sapSyncFlag", row::getSapSyncFlag)
            .map(sapSuccessTime).toPropertyWhenPresent("sapSuccessTime", row::getSapSuccessTime)
            .map(partsLibraryType).toPropertyWhenPresent("partsLibraryType", row::getPartsLibraryType)
            .map(taskCreateTime).toPropertyWhenPresent("taskCreateTime", row::getTaskCreateTime)
            .map(taskInflowTime).toPropertyWhenPresent("taskInflowTime", row::getTaskInflowTime)
            .map(vehicleReciveTime).toPropertyWhenPresent("vehicleReciveTime", row::getVehicleReciveTime)
            .map(vehicleCheckTime).toPropertyWhenPresent("vehicleCheckTime", row::getVehicleCheckTime)
            .map(vehicleRepairTime).toPropertyWhenPresent("vehicleRepairTime", row::getVehicleRepairTime)
            .map(reassignmentPassTime).toPropertyWhenPresent("reassignmentPassTime", row::getReassignmentPassTime)
            .map(settleClosingTime).toPropertyWhenPresent("settleClosingTime", row::getSettleClosingTime)
            .map(expectedRepairDays).toPropertyWhenPresent("expectedRepairDays", row::getExpectedRepairDays)
            .map(expectedRepairComplete).toPropertyWhenPresent("expectedRepairComplete", row::getExpectedRepairComplete)
            .map(accidentReportNumber).toPropertyWhenPresent("accidentReportNumber", row::getAccidentReportNumber)
            .map(repairFlag).toPropertyWhenPresent("repairFlag", row::getRepairFlag)
            .map(terminalId).toPropertyWhenPresent("terminalId", row::getTerminalId)
            .map(totalMileage).toPropertyWhenPresent("totalMileage", row::getTotalMileage)
            .map(terminalMileage).toPropertyWhenPresent("terminalMileage", row::getTerminalMileage)
            .map(associatedOrder).toPropertyWhenPresent("associatedOrder", row::getAssociatedOrder)
            .map(orderType).toPropertyWhenPresent("orderType", row::getOrderType)
            .map(relateType).toPropertyWhenPresent("relateType", row::getRelateType)
            .map(orderRemark).toPropertyWhenPresent("orderRemark", row::getOrderRemark)
            .map(violationName).toPropertyWhenPresent("violationName", row::getViolationName)
            .map(violationTelNo).toPropertyWhenPresent("violationTelNo", row::getViolationTelNo)
            .map(vehicleClientMaintenanceFee).toPropertyWhenPresent("vehicleClientMaintenanceFee", row::getVehicleClientMaintenanceFee)
            .map(clientInspectTag).toPropertyWhenPresent("clientInspectTag", row::getClientInspectTag)
            .map(clientUpkeepTag).toPropertyWhenPresent("clientUpkeepTag", row::getClientUpkeepTag)
            .map(authId).toPropertyWhenPresent("authId", row::getAuthId)
            .map(noDeductiblesFlag).toPropertyWhenPresent("noDeductiblesFlag", row::getNoDeductiblesFlag)
            .map(serviceType).toPropertyWhenPresent("serviceType", row::getServiceType)
            .map(serviceContent).toPropertyWhenPresent("serviceContent", row::getServiceContent)
            .map(driverName).toPropertyWhenPresent("driverName", row::getDriverName)
            .map(driverTel).toPropertyWhenPresent("driverTel", row::getDriverTel)
            .map(routingInspectionName).toPropertyWhenPresent("routingInspectionName", row::getRoutingInspectionName)
            .map(routingInspectionTel).toPropertyWhenPresent("routingInspectionTel", row::getRoutingInspectionTel)
            .map(damagedPartDescribe).toPropertyWhenPresent("damagedPartDescribe", row::getDamagedPartDescribe)
            .map(accidentDescribe).toPropertyWhenPresent("accidentDescribe", row::getAccidentDescribe)
            .map(trailerFlag).toPropertyWhenPresent("trailerFlag", row::getTrailerFlag)
            .map(repairReviewTotalAmount).toPropertyWhenPresent("repairReviewTotalAmount", row::getRepairReviewTotalAmount)
            .map(repairReplaceTotalAmount).toPropertyWhenPresent("repairReplaceTotalAmount", row::getRepairReplaceTotalAmount)
            .map(repairRepairTotalAmount).toPropertyWhenPresent("repairRepairTotalAmount", row::getRepairRepairTotalAmount)
            .map(repairInsuranceTotalAmount).toPropertyWhenPresent("repairInsuranceTotalAmount", row::getRepairInsuranceTotalAmount)
            .map(vehicleReplaceTotalAmount).toPropertyWhenPresent("vehicleReplaceTotalAmount", row::getVehicleReplaceTotalAmount)
            .map(vehicleRepairTotalAmount).toPropertyWhenPresent("vehicleRepairTotalAmount", row::getVehicleRepairTotalAmount)
            .map(vehicleInsuranceTotalAmount).toPropertyWhenPresent("vehicleInsuranceTotalAmount", row::getVehicleInsuranceTotalAmount)
            .map(vehicleManageViewFlag).toPropertyWhenPresent("vehicleManageViewFlag", row::getVehicleManageViewFlag)
            .map(resurveyFlag).toPropertyWhenPresent("resurveyFlag", row::getResurveyFlag)
            .map(resurveyPart).toPropertyWhenPresent("resurveyPart", row::getResurveyPart)
            .map(recoveryFlag).toPropertyWhenPresent("recoveryFlag", row::getRecoveryFlag)
            .map(dutySituation).toPropertyWhenPresent("dutySituation", row::getDutySituation)
            .map(recoveryAmount).toPropertyWhenPresent("recoveryAmount", row::getRecoveryAmount)
            .map(insuranceAmount).toPropertyWhenPresent("insuranceAmount", row::getInsuranceAmount)
            .map(accDepAmount).toPropertyWhenPresent("accDepAmount", row::getAccDepAmount)
            .map(outageLossAmount).toPropertyWhenPresent("outageLossAmount", row::getOutageLossAmount)
            .map(vehicleLossAmount).toPropertyWhenPresent("vehicleLossAmount", row::getVehicleLossAmount)
            .map(trailerRescueAmount).toPropertyWhenPresent("trailerRescueAmount", row::getTrailerRescueAmount)
            .map(maintainAmount).toPropertyWhenPresent("maintainAmount", row::getMaintainAmount)
            .map(lossOrderAmount).toPropertyWhenPresent("lossOrderAmount", row::getLossOrderAmount)
            .map(reassignmentRepairOrgId).toPropertyWhenPresent("reassignmentRepairOrgId", row::getReassignmentRepairOrgId)
            .map(reassignmentReasons).toPropertyWhenPresent("reassignmentReasons", row::getReassignmentReasons)
            .map(reassignmentRejectReasons).toPropertyWhenPresent("reassignmentRejectReasons", row::getReassignmentRejectReasons)
            .map(verificationRejectReasons).toPropertyWhenPresent("verificationRejectReasons", row::getVerificationRejectReasons)
            .map(verificationRejectReasonsDetail).toPropertyWhenPresent("verificationRejectReasonsDetail", row::getVerificationRejectReasonsDetail)
            .map(verificationRejectLevel).toPropertyWhenPresent("verificationRejectLevel", row::getVerificationRejectLevel)
            .map(checkResultFlag).toPropertyWhenPresent("checkResultFlag", row::getCheckResultFlag)
            .map(checkUnqualifiedReason).toPropertyWhenPresent("checkUnqualifiedReason", row::getCheckUnqualifiedReason)
            .map(confirmType).toPropertyWhenPresent("confirmType", row::getConfirmType)
            .map(maintainToRepairFlag).toPropertyWhenPresent("maintainToRepairFlag", row::getMaintainToRepairFlag)
            .map(verificationLossTaskOperId).toPropertyWhenPresent("verificationLossTaskOperId", row::getVerificationLossTaskOperId)
            .map(examineLevel).toPropertyWhenPresent("examineLevel", row::getExamineLevel)
            .map(verificationLossCheckTime).toPropertyWhenPresent("verificationLossCheckTime", row::getVerificationLossCheckTime)
            .map(verificationLossCheckId).toPropertyWhenPresent("verificationLossCheckId", row::getVerificationLossCheckId)
            .map(overTimeReasons).toPropertyWhenPresent("overTimeReasons", row::getOverTimeReasons)
            .map(repairTotalAmountFirst).toPropertyWhenPresent("repairTotalAmountFirst", row::getRepairTotalAmountFirst)
            .map(nuclearLossReversionFlag).toPropertyWhenPresent("nuclearLossReversionFlag", row::getNuclearLossReversionFlag)
            .map(remark).toPropertyWhenPresent("remark", row::getRemark)
            .map(tirenumber).toPropertyWhenPresent("tirenumber", row::getTirenumber)
            .map(tireUnitPrice).toPropertyWhenPresent("tireUnitPrice", row::getTireUnitPrice)
            .map(tireBrand).toPropertyWhenPresent("tireBrand", row::getTireBrand)
            .map(associatedTaskNo).toPropertyWhenPresent("associatedTaskNo", row::getAssociatedTaskNo)
            .map(insuranceFlag).toPropertyWhenPresent("insuranceFlag", row::getInsuranceFlag)
            .map(advancedAuditLeve).toPropertyWhenPresent("advancedAuditLeve", row::getAdvancedAuditLeve)
            .map(alreadyIncomingAmount).toPropertyWhenPresent("alreadyIncomingAmount", row::getAlreadyIncomingAmount)
            .map(alreadyPayAmount).toPropertyWhenPresent("alreadyPayAmount", row::getAlreadyPayAmount)
            .map(waitPayAmount).toPropertyWhenPresent("waitPayAmount", row::getWaitPayAmount)
            .map(mustClaimAmount).toPropertyWhenPresent("mustClaimAmount", row::getMustClaimAmount)
            .map(mustPayAmount).toPropertyWhenPresent("mustPayAmount", row::getMustPayAmount)
            .map(vehicleRepairCostAmount).toPropertyWhenPresent("vehicleRepairCostAmount", row::getVehicleRepairCostAmount)
            .map(repairSettleAmount).toPropertyWhenPresent("repairSettleAmount", row::getRepairSettleAmount)
            .map(repairFaxSettleAmount).toPropertyWhenPresent("repairFaxSettleAmount", row::getRepairFaxSettleAmount)
            .map(transferFlag).toPropertyWhenPresent("transferFlag", row::getTransferFlag)
            .map(rationalIndemnityCnt).toPropertyWhenPresent("rationalIndemnityCnt", row::getRationalIndemnityCnt)
            .map(accidentDayTime).toPropertyWhenPresent("accidentDayTime", row::getAccidentDayTime)
            .map(deductFlag).toPropertyWhenPresent("deductFlag", row::getDeductFlag)
            .map(origin).toPropertyWhenPresent("origin", row::getOrigin)
            .map(renttype).toPropertyWhenPresent("renttype", row::getRenttype)
            .map(factOperateTag).toPropertyWhenPresent("factOperateTag", row::getFactOperateTag)
            .map(takeUserName).toPropertyWhenPresent("takeUserName", row::getTakeUserName)
            .map(takeUserPhone).toPropertyWhenPresent("takeUserPhone", row::getTakeUserPhone)
            .map(takeVoucher).toPropertyWhenPresent("takeVoucher", row::getTakeVoucher)
            .map(synTakeTime).toPropertyWhenPresent("synTakeTime", row::getSynTakeTime)
            .map(closeReason).toPropertyWhenPresent("closeReason", row::getCloseReason)
            .map(claimsFlag).toPropertyWhenPresent("claimsFlag", row::getClaimsFlag)
            .map(estimatedClaimAmount).toPropertyWhenPresent("estimatedClaimAmount", row::getEstimatedClaimAmount)
            .map(carDamageType).toPropertyWhenPresent("carDamageType", row::getCarDamageType)
            .map(vehicleScrapeValue).toPropertyWhenPresent("vehicleScrapeValue", row::getVehicleScrapeValue)
            .map(reviewToSelFeeFlag).toPropertyWhenPresent("reviewToSelFeeFlag", row::getReviewToSelFeeFlag)
            .map(confirmCarDamageType).toPropertyWhenPresent("confirmCarDamageType", row::getConfirmCarDamageType)
            .map(accidentNo).toPropertyWhenPresent("accidentNo", row::getAccidentNo)
            .map(insurancePreReviewTime).toPropertyWhenPresent("insurancePreReviewTime", row::getInsurancePreReviewTime)
            .map(insurancePreReviewLevel).toPropertyWhenPresent("insurancePreReviewLevel", row::getInsurancePreReviewLevel)
            .map(preReviewVehicleScrapeValue).toPropertyWhenPresent("preReviewVehicleScrapeValue", row::getPreReviewVehicleScrapeValue)
            .map(preReviewVehicleScrapeTime).toPropertyWhenPresent("preReviewVehicleScrapeTime", row::getPreReviewVehicleScrapeTime)
            .map(isUsedApplets).toPropertyWhenPresent("isUsedApplets", row::getIsUsedApplets)
            .map(criteriaId).toPropertyWhenPresent("criteriaId", row::getCriteriaId)
            .map(custPaysDirect).toPropertyWhenPresent("custPaysDirect", row::getCustPaysDirect)
            .map(custAmount).toPropertyWhenPresent("custAmount", row::getCustAmount)
            .map(userAssumedAmount).toPropertyWhenPresent("userAssumedAmount", row::getUserAssumedAmount)
            .map(notUserAssumedAmount).toPropertyWhenPresent("notUserAssumedAmount", row::getNotUserAssumedAmount)
            .map(syncClaimFlag).toPropertyWhenPresent("syncClaimFlag", row::getSyncClaimFlag)
            .map(taxRate).toPropertyWhenPresent("taxRate", row::getTaxRate)
            .map(repairDepotType).toPropertyWhenPresent("repairDepotType", row::getRepairDepotType)
            .map(declareNo).toPropertyWhenPresent("declareNo", row::getDeclareNo)
            .map(declareStatus).toPropertyWhenPresent("declareStatus", row::getDeclareStatus)
            .map(declareSettlementStatus).toPropertyWhenPresent("declareSettlementStatus", row::getDeclareSettlementStatus)
            .map(settlementNo).toPropertyWhenPresent("settlementNo", row::getSettlementNo)
            .map(settlementStatus).toPropertyWhenPresent("settlementStatus", row::getSettlementStatus)
            .map(sendRepairTime).toPropertyWhenPresent("sendRepairTime", row::getSendRepairTime)
            .map(situationDesc).toPropertyWhenPresent("situationDesc", row::getSituationDesc)
            .map(propertyStatus).toPropertyWhenPresent("propertyStatus", row::getPropertyStatus)
            .map(productLine).toPropertyWhenPresent("productLine", row::getProductLine)
            .map(subProductLine).toPropertyWhenPresent("subProductLine", row::getSubProductLine)
            .map(selfFundedAmount).toPropertyWhenPresent("selfFundedAmount", row::getSelfFundedAmount)
            .map(continueDays).toPropertyWhenPresent("continueDays", row::getContinueDays)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    default Optional<MtcRepairTask> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcRepairTask, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    default List<MtcRepairTask> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcRepairTask, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    default List<MtcRepairTask> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcRepairTask, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    default Optional<MtcRepairTask> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcRepairTask, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcRepairTask row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalTo(row::getTaskNo)
                .set(taskType).equalTo(row::getTaskType)
                .set(orgId).equalTo(row::getOrgId)
                .set(orgName).equalTo(row::getOrgName)
                .set(operateOrgId).equalTo(row::getOperateOrgId)
                .set(operateOrgName).equalTo(row::getOperateOrgName)
                .set(vehicleNo).equalTo(row::getVehicleNo)
                .set(vehicleModelSeq).equalTo(row::getVehicleModelSeq)
                .set(vehicleModelInfo).equalTo(row::getVehicleModelInfo)
                .set(vin).equalTo(row::getVin)
                .set(insuranceCompanyName).equalTo(row::getInsuranceCompanyName)
                .set(repairTypeId).equalTo(row::getRepairTypeId)
                .set(repairTypeName).equalTo(row::getRepairTypeName)
                .set(repairGrade).equalTo(row::getRepairGrade)
                .set(repairDepotId).equalTo(row::getRepairDepotId)
                .set(repairDepotName).equalTo(row::getRepairDepotName)
                .set(repairDepotOrgId).equalTo(row::getRepairDepotOrgId)
                .set(repairDepotSapCode).equalTo(row::getRepairDepotSapCode)
                .set(sapSendStatus).equalTo(row::getSapSendStatus)
                .set(sapSyncFlag).equalTo(row::getSapSyncFlag)
                .set(sapSuccessTime).equalTo(row::getSapSuccessTime)
                .set(partsLibraryType).equalTo(row::getPartsLibraryType)
                .set(taskCreateTime).equalTo(row::getTaskCreateTime)
                .set(taskInflowTime).equalTo(row::getTaskInflowTime)
                .set(vehicleReciveTime).equalTo(row::getVehicleReciveTime)
                .set(vehicleCheckTime).equalTo(row::getVehicleCheckTime)
                .set(vehicleRepairTime).equalTo(row::getVehicleRepairTime)
                .set(reassignmentPassTime).equalTo(row::getReassignmentPassTime)
                .set(settleClosingTime).equalTo(row::getSettleClosingTime)
                .set(expectedRepairDays).equalTo(row::getExpectedRepairDays)
                .set(expectedRepairComplete).equalTo(row::getExpectedRepairComplete)
                .set(accidentReportNumber).equalTo(row::getAccidentReportNumber)
                .set(repairFlag).equalTo(row::getRepairFlag)
                .set(terminalId).equalTo(row::getTerminalId)
                .set(totalMileage).equalTo(row::getTotalMileage)
                .set(terminalMileage).equalTo(row::getTerminalMileage)
                .set(associatedOrder).equalTo(row::getAssociatedOrder)
                .set(orderType).equalTo(row::getOrderType)
                .set(relateType).equalTo(row::getRelateType)
                .set(orderRemark).equalTo(row::getOrderRemark)
                .set(violationName).equalTo(row::getViolationName)
                .set(violationTelNo).equalTo(row::getViolationTelNo)
                .set(vehicleClientMaintenanceFee).equalTo(row::getVehicleClientMaintenanceFee)
                .set(clientInspectTag).equalTo(row::getClientInspectTag)
                .set(clientUpkeepTag).equalTo(row::getClientUpkeepTag)
                .set(authId).equalTo(row::getAuthId)
                .set(noDeductiblesFlag).equalTo(row::getNoDeductiblesFlag)
                .set(serviceType).equalTo(row::getServiceType)
                .set(serviceContent).equalTo(row::getServiceContent)
                .set(driverName).equalTo(row::getDriverName)
                .set(driverTel).equalTo(row::getDriverTel)
                .set(routingInspectionName).equalTo(row::getRoutingInspectionName)
                .set(routingInspectionTel).equalTo(row::getRoutingInspectionTel)
                .set(damagedPartDescribe).equalTo(row::getDamagedPartDescribe)
                .set(accidentDescribe).equalTo(row::getAccidentDescribe)
                .set(trailerFlag).equalTo(row::getTrailerFlag)
                .set(repairReviewTotalAmount).equalTo(row::getRepairReviewTotalAmount)
                .set(repairReplaceTotalAmount).equalTo(row::getRepairReplaceTotalAmount)
                .set(repairRepairTotalAmount).equalTo(row::getRepairRepairTotalAmount)
                .set(repairInsuranceTotalAmount).equalTo(row::getRepairInsuranceTotalAmount)
                .set(vehicleReplaceTotalAmount).equalTo(row::getVehicleReplaceTotalAmount)
                .set(vehicleRepairTotalAmount).equalTo(row::getVehicleRepairTotalAmount)
                .set(vehicleInsuranceTotalAmount).equalTo(row::getVehicleInsuranceTotalAmount)
                .set(vehicleManageViewFlag).equalTo(row::getVehicleManageViewFlag)
                .set(resurveyFlag).equalTo(row::getResurveyFlag)
                .set(resurveyPart).equalTo(row::getResurveyPart)
                .set(recoveryFlag).equalTo(row::getRecoveryFlag)
                .set(dutySituation).equalTo(row::getDutySituation)
                .set(recoveryAmount).equalTo(row::getRecoveryAmount)
                .set(insuranceAmount).equalTo(row::getInsuranceAmount)
                .set(accDepAmount).equalTo(row::getAccDepAmount)
                .set(outageLossAmount).equalTo(row::getOutageLossAmount)
                .set(vehicleLossAmount).equalTo(row::getVehicleLossAmount)
                .set(trailerRescueAmount).equalTo(row::getTrailerRescueAmount)
                .set(maintainAmount).equalTo(row::getMaintainAmount)
                .set(lossOrderAmount).equalTo(row::getLossOrderAmount)
                .set(reassignmentRepairOrgId).equalTo(row::getReassignmentRepairOrgId)
                .set(reassignmentReasons).equalTo(row::getReassignmentReasons)
                .set(reassignmentRejectReasons).equalTo(row::getReassignmentRejectReasons)
                .set(verificationRejectReasons).equalTo(row::getVerificationRejectReasons)
                .set(verificationRejectReasonsDetail).equalTo(row::getVerificationRejectReasonsDetail)
                .set(verificationRejectLevel).equalTo(row::getVerificationRejectLevel)
                .set(checkResultFlag).equalTo(row::getCheckResultFlag)
                .set(checkUnqualifiedReason).equalTo(row::getCheckUnqualifiedReason)
                .set(confirmType).equalTo(row::getConfirmType)
                .set(maintainToRepairFlag).equalTo(row::getMaintainToRepairFlag)
                .set(verificationLossTaskOperId).equalTo(row::getVerificationLossTaskOperId)
                .set(examineLevel).equalTo(row::getExamineLevel)
                .set(verificationLossCheckTime).equalTo(row::getVerificationLossCheckTime)
                .set(verificationLossCheckId).equalTo(row::getVerificationLossCheckId)
                .set(overTimeReasons).equalTo(row::getOverTimeReasons)
                .set(repairTotalAmountFirst).equalTo(row::getRepairTotalAmountFirst)
                .set(nuclearLossReversionFlag).equalTo(row::getNuclearLossReversionFlag)
                .set(remark).equalTo(row::getRemark)
                .set(tirenumber).equalTo(row::getTirenumber)
                .set(tireUnitPrice).equalTo(row::getTireUnitPrice)
                .set(tireBrand).equalTo(row::getTireBrand)
                .set(associatedTaskNo).equalTo(row::getAssociatedTaskNo)
                .set(insuranceFlag).equalTo(row::getInsuranceFlag)
                .set(advancedAuditLeve).equalTo(row::getAdvancedAuditLeve)
                .set(alreadyIncomingAmount).equalTo(row::getAlreadyIncomingAmount)
                .set(alreadyPayAmount).equalTo(row::getAlreadyPayAmount)
                .set(waitPayAmount).equalTo(row::getWaitPayAmount)
                .set(mustClaimAmount).equalTo(row::getMustClaimAmount)
                .set(mustPayAmount).equalTo(row::getMustPayAmount)
                .set(vehicleRepairCostAmount).equalTo(row::getVehicleRepairCostAmount)
                .set(repairSettleAmount).equalTo(row::getRepairSettleAmount)
                .set(repairFaxSettleAmount).equalTo(row::getRepairFaxSettleAmount)
                .set(transferFlag).equalTo(row::getTransferFlag)
                .set(rationalIndemnityCnt).equalTo(row::getRationalIndemnityCnt)
                .set(accidentDayTime).equalTo(row::getAccidentDayTime)
                .set(deductFlag).equalTo(row::getDeductFlag)
                .set(origin).equalTo(row::getOrigin)
                .set(renttype).equalTo(row::getRenttype)
                .set(factOperateTag).equalTo(row::getFactOperateTag)
                .set(takeUserName).equalTo(row::getTakeUserName)
                .set(takeUserPhone).equalTo(row::getTakeUserPhone)
                .set(takeVoucher).equalTo(row::getTakeVoucher)
                .set(synTakeTime).equalTo(row::getSynTakeTime)
                .set(closeReason).equalTo(row::getCloseReason)
                .set(claimsFlag).equalTo(row::getClaimsFlag)
                .set(estimatedClaimAmount).equalTo(row::getEstimatedClaimAmount)
                .set(carDamageType).equalTo(row::getCarDamageType)
                .set(vehicleScrapeValue).equalTo(row::getVehicleScrapeValue)
                .set(reviewToSelFeeFlag).equalTo(row::getReviewToSelFeeFlag)
                .set(confirmCarDamageType).equalTo(row::getConfirmCarDamageType)
                .set(accidentNo).equalTo(row::getAccidentNo)
                .set(insurancePreReviewTime).equalTo(row::getInsurancePreReviewTime)
                .set(insurancePreReviewLevel).equalTo(row::getInsurancePreReviewLevel)
                .set(preReviewVehicleScrapeValue).equalTo(row::getPreReviewVehicleScrapeValue)
                .set(preReviewVehicleScrapeTime).equalTo(row::getPreReviewVehicleScrapeTime)
                .set(isUsedApplets).equalTo(row::getIsUsedApplets)
                .set(criteriaId).equalTo(row::getCriteriaId)
                .set(custPaysDirect).equalTo(row::getCustPaysDirect)
                .set(custAmount).equalTo(row::getCustAmount)
                .set(userAssumedAmount).equalTo(row::getUserAssumedAmount)
                .set(notUserAssumedAmount).equalTo(row::getNotUserAssumedAmount)
                .set(syncClaimFlag).equalTo(row::getSyncClaimFlag)
                .set(taxRate).equalTo(row::getTaxRate)
                .set(repairDepotType).equalTo(row::getRepairDepotType)
                .set(declareNo).equalTo(row::getDeclareNo)
                .set(declareStatus).equalTo(row::getDeclareStatus)
                .set(declareSettlementStatus).equalTo(row::getDeclareSettlementStatus)
                .set(settlementNo).equalTo(row::getSettlementNo)
                .set(settlementStatus).equalTo(row::getSettlementStatus)
                .set(sendRepairTime).equalTo(row::getSendRepairTime)
                .set(situationDesc).equalTo(row::getSituationDesc)
                .set(propertyStatus).equalTo(row::getPropertyStatus)
                .set(productLine).equalTo(row::getProductLine)
                .set(subProductLine).equalTo(row::getSubProductLine)
                .set(selfFundedAmount).equalTo(row::getSelfFundedAmount)
                .set(continueDays).equalTo(row::getContinueDays)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcRepairTask row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(taskNo).equalToWhenPresent(row::getTaskNo)
                .set(taskType).equalToWhenPresent(row::getTaskType)
                .set(orgId).equalToWhenPresent(row::getOrgId)
                .set(orgName).equalToWhenPresent(row::getOrgName)
                .set(operateOrgId).equalToWhenPresent(row::getOperateOrgId)
                .set(operateOrgName).equalToWhenPresent(row::getOperateOrgName)
                .set(vehicleNo).equalToWhenPresent(row::getVehicleNo)
                .set(vehicleModelSeq).equalToWhenPresent(row::getVehicleModelSeq)
                .set(vehicleModelInfo).equalToWhenPresent(row::getVehicleModelInfo)
                .set(vin).equalToWhenPresent(row::getVin)
                .set(insuranceCompanyName).equalToWhenPresent(row::getInsuranceCompanyName)
                .set(repairTypeId).equalToWhenPresent(row::getRepairTypeId)
                .set(repairTypeName).equalToWhenPresent(row::getRepairTypeName)
                .set(repairGrade).equalToWhenPresent(row::getRepairGrade)
                .set(repairDepotId).equalToWhenPresent(row::getRepairDepotId)
                .set(repairDepotName).equalToWhenPresent(row::getRepairDepotName)
                .set(repairDepotOrgId).equalToWhenPresent(row::getRepairDepotOrgId)
                .set(repairDepotSapCode).equalToWhenPresent(row::getRepairDepotSapCode)
                .set(sapSendStatus).equalToWhenPresent(row::getSapSendStatus)
                .set(sapSyncFlag).equalToWhenPresent(row::getSapSyncFlag)
                .set(sapSuccessTime).equalToWhenPresent(row::getSapSuccessTime)
                .set(partsLibraryType).equalToWhenPresent(row::getPartsLibraryType)
                .set(taskCreateTime).equalToWhenPresent(row::getTaskCreateTime)
                .set(taskInflowTime).equalToWhenPresent(row::getTaskInflowTime)
                .set(vehicleReciveTime).equalToWhenPresent(row::getVehicleReciveTime)
                .set(vehicleCheckTime).equalToWhenPresent(row::getVehicleCheckTime)
                .set(vehicleRepairTime).equalToWhenPresent(row::getVehicleRepairTime)
                .set(reassignmentPassTime).equalToWhenPresent(row::getReassignmentPassTime)
                .set(settleClosingTime).equalToWhenPresent(row::getSettleClosingTime)
                .set(expectedRepairDays).equalToWhenPresent(row::getExpectedRepairDays)
                .set(expectedRepairComplete).equalToWhenPresent(row::getExpectedRepairComplete)
                .set(accidentReportNumber).equalToWhenPresent(row::getAccidentReportNumber)
                .set(repairFlag).equalToWhenPresent(row::getRepairFlag)
                .set(terminalId).equalToWhenPresent(row::getTerminalId)
                .set(totalMileage).equalToWhenPresent(row::getTotalMileage)
                .set(terminalMileage).equalToWhenPresent(row::getTerminalMileage)
                .set(associatedOrder).equalToWhenPresent(row::getAssociatedOrder)
                .set(orderType).equalToWhenPresent(row::getOrderType)
                .set(relateType).equalToWhenPresent(row::getRelateType)
                .set(orderRemark).equalToWhenPresent(row::getOrderRemark)
                .set(violationName).equalToWhenPresent(row::getViolationName)
                .set(violationTelNo).equalToWhenPresent(row::getViolationTelNo)
                .set(vehicleClientMaintenanceFee).equalToWhenPresent(row::getVehicleClientMaintenanceFee)
                .set(clientInspectTag).equalToWhenPresent(row::getClientInspectTag)
                .set(clientUpkeepTag).equalToWhenPresent(row::getClientUpkeepTag)
                .set(authId).equalToWhenPresent(row::getAuthId)
                .set(noDeductiblesFlag).equalToWhenPresent(row::getNoDeductiblesFlag)
                .set(serviceType).equalToWhenPresent(row::getServiceType)
                .set(serviceContent).equalToWhenPresent(row::getServiceContent)
                .set(driverName).equalToWhenPresent(row::getDriverName)
                .set(driverTel).equalToWhenPresent(row::getDriverTel)
                .set(routingInspectionName).equalToWhenPresent(row::getRoutingInspectionName)
                .set(routingInspectionTel).equalToWhenPresent(row::getRoutingInspectionTel)
                .set(damagedPartDescribe).equalToWhenPresent(row::getDamagedPartDescribe)
                .set(accidentDescribe).equalToWhenPresent(row::getAccidentDescribe)
                .set(trailerFlag).equalToWhenPresent(row::getTrailerFlag)
                .set(repairReviewTotalAmount).equalToWhenPresent(row::getRepairReviewTotalAmount)
                .set(repairReplaceTotalAmount).equalToWhenPresent(row::getRepairReplaceTotalAmount)
                .set(repairRepairTotalAmount).equalToWhenPresent(row::getRepairRepairTotalAmount)
                .set(repairInsuranceTotalAmount).equalToWhenPresent(row::getRepairInsuranceTotalAmount)
                .set(vehicleReplaceTotalAmount).equalToWhenPresent(row::getVehicleReplaceTotalAmount)
                .set(vehicleRepairTotalAmount).equalToWhenPresent(row::getVehicleRepairTotalAmount)
                .set(vehicleInsuranceTotalAmount).equalToWhenPresent(row::getVehicleInsuranceTotalAmount)
                .set(vehicleManageViewFlag).equalToWhenPresent(row::getVehicleManageViewFlag)
                .set(resurveyFlag).equalToWhenPresent(row::getResurveyFlag)
                .set(resurveyPart).equalToWhenPresent(row::getResurveyPart)
                .set(recoveryFlag).equalToWhenPresent(row::getRecoveryFlag)
                .set(dutySituation).equalToWhenPresent(row::getDutySituation)
                .set(recoveryAmount).equalToWhenPresent(row::getRecoveryAmount)
                .set(insuranceAmount).equalToWhenPresent(row::getInsuranceAmount)
                .set(accDepAmount).equalToWhenPresent(row::getAccDepAmount)
                .set(outageLossAmount).equalToWhenPresent(row::getOutageLossAmount)
                .set(vehicleLossAmount).equalToWhenPresent(row::getVehicleLossAmount)
                .set(trailerRescueAmount).equalToWhenPresent(row::getTrailerRescueAmount)
                .set(maintainAmount).equalToWhenPresent(row::getMaintainAmount)
                .set(lossOrderAmount).equalToWhenPresent(row::getLossOrderAmount)
                .set(reassignmentRepairOrgId).equalToWhenPresent(row::getReassignmentRepairOrgId)
                .set(reassignmentReasons).equalToWhenPresent(row::getReassignmentReasons)
                .set(reassignmentRejectReasons).equalToWhenPresent(row::getReassignmentRejectReasons)
                .set(verificationRejectReasons).equalToWhenPresent(row::getVerificationRejectReasons)
                .set(verificationRejectReasonsDetail).equalToWhenPresent(row::getVerificationRejectReasonsDetail)
                .set(verificationRejectLevel).equalToWhenPresent(row::getVerificationRejectLevel)
                .set(checkResultFlag).equalToWhenPresent(row::getCheckResultFlag)
                .set(checkUnqualifiedReason).equalToWhenPresent(row::getCheckUnqualifiedReason)
                .set(confirmType).equalToWhenPresent(row::getConfirmType)
                .set(maintainToRepairFlag).equalToWhenPresent(row::getMaintainToRepairFlag)
                .set(verificationLossTaskOperId).equalToWhenPresent(row::getVerificationLossTaskOperId)
                .set(examineLevel).equalToWhenPresent(row::getExamineLevel)
                .set(verificationLossCheckTime).equalToWhenPresent(row::getVerificationLossCheckTime)
                .set(verificationLossCheckId).equalToWhenPresent(row::getVerificationLossCheckId)
                .set(overTimeReasons).equalToWhenPresent(row::getOverTimeReasons)
                .set(repairTotalAmountFirst).equalToWhenPresent(row::getRepairTotalAmountFirst)
                .set(nuclearLossReversionFlag).equalToWhenPresent(row::getNuclearLossReversionFlag)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(tirenumber).equalToWhenPresent(row::getTirenumber)
                .set(tireUnitPrice).equalToWhenPresent(row::getTireUnitPrice)
                .set(tireBrand).equalToWhenPresent(row::getTireBrand)
                .set(associatedTaskNo).equalToWhenPresent(row::getAssociatedTaskNo)
                .set(insuranceFlag).equalToWhenPresent(row::getInsuranceFlag)
                .set(advancedAuditLeve).equalToWhenPresent(row::getAdvancedAuditLeve)
                .set(alreadyIncomingAmount).equalToWhenPresent(row::getAlreadyIncomingAmount)
                .set(alreadyPayAmount).equalToWhenPresent(row::getAlreadyPayAmount)
                .set(waitPayAmount).equalToWhenPresent(row::getWaitPayAmount)
                .set(mustClaimAmount).equalToWhenPresent(row::getMustClaimAmount)
                .set(mustPayAmount).equalToWhenPresent(row::getMustPayAmount)
                .set(vehicleRepairCostAmount).equalToWhenPresent(row::getVehicleRepairCostAmount)
                .set(repairSettleAmount).equalToWhenPresent(row::getRepairSettleAmount)
                .set(repairFaxSettleAmount).equalToWhenPresent(row::getRepairFaxSettleAmount)
                .set(transferFlag).equalToWhenPresent(row::getTransferFlag)
                .set(rationalIndemnityCnt).equalToWhenPresent(row::getRationalIndemnityCnt)
                .set(accidentDayTime).equalToWhenPresent(row::getAccidentDayTime)
                .set(deductFlag).equalToWhenPresent(row::getDeductFlag)
                .set(origin).equalToWhenPresent(row::getOrigin)
                .set(renttype).equalToWhenPresent(row::getRenttype)
                .set(factOperateTag).equalToWhenPresent(row::getFactOperateTag)
                .set(takeUserName).equalToWhenPresent(row::getTakeUserName)
                .set(takeUserPhone).equalToWhenPresent(row::getTakeUserPhone)
                .set(takeVoucher).equalToWhenPresent(row::getTakeVoucher)
                .set(synTakeTime).equalToWhenPresent(row::getSynTakeTime)
                .set(closeReason).equalToWhenPresent(row::getCloseReason)
                .set(claimsFlag).equalToWhenPresent(row::getClaimsFlag)
                .set(estimatedClaimAmount).equalToWhenPresent(row::getEstimatedClaimAmount)
                .set(carDamageType).equalToWhenPresent(row::getCarDamageType)
                .set(vehicleScrapeValue).equalToWhenPresent(row::getVehicleScrapeValue)
                .set(reviewToSelFeeFlag).equalToWhenPresent(row::getReviewToSelFeeFlag)
                .set(confirmCarDamageType).equalToWhenPresent(row::getConfirmCarDamageType)
                .set(accidentNo).equalToWhenPresent(row::getAccidentNo)
                .set(insurancePreReviewTime).equalToWhenPresent(row::getInsurancePreReviewTime)
                .set(insurancePreReviewLevel).equalToWhenPresent(row::getInsurancePreReviewLevel)
                .set(preReviewVehicleScrapeValue).equalToWhenPresent(row::getPreReviewVehicleScrapeValue)
                .set(preReviewVehicleScrapeTime).equalToWhenPresent(row::getPreReviewVehicleScrapeTime)
                .set(isUsedApplets).equalToWhenPresent(row::getIsUsedApplets)
                .set(criteriaId).equalToWhenPresent(row::getCriteriaId)
                .set(custPaysDirect).equalToWhenPresent(row::getCustPaysDirect)
                .set(custAmount).equalToWhenPresent(row::getCustAmount)
                .set(userAssumedAmount).equalToWhenPresent(row::getUserAssumedAmount)
                .set(notUserAssumedAmount).equalToWhenPresent(row::getNotUserAssumedAmount)
                .set(syncClaimFlag).equalToWhenPresent(row::getSyncClaimFlag)
                .set(taxRate).equalToWhenPresent(row::getTaxRate)
                .set(repairDepotType).equalToWhenPresent(row::getRepairDepotType)
                .set(declareNo).equalToWhenPresent(row::getDeclareNo)
                .set(declareStatus).equalToWhenPresent(row::getDeclareStatus)
                .set(declareSettlementStatus).equalToWhenPresent(row::getDeclareSettlementStatus)
                .set(settlementNo).equalToWhenPresent(row::getSettlementNo)
                .set(settlementStatus).equalToWhenPresent(row::getSettlementStatus)
                .set(sendRepairTime).equalToWhenPresent(row::getSendRepairTime)
                .set(situationDesc).equalToWhenPresent(row::getSituationDesc)
                .set(propertyStatus).equalToWhenPresent(row::getPropertyStatus)
                .set(productLine).equalToWhenPresent(row::getProductLine)
                .set(subProductLine).equalToWhenPresent(row::getSubProductLine)
                .set(selfFundedAmount).equalToWhenPresent(row::getSelfFundedAmount)
                .set(continueDays).equalToWhenPresent(row::getContinueDays)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    default int updateByPrimaryKey(MtcRepairTask row) {
        return update(c ->
            c.set(taskNo).equalTo(row::getTaskNo)
            .set(taskType).equalTo(row::getTaskType)
            .set(orgId).equalTo(row::getOrgId)
            .set(orgName).equalTo(row::getOrgName)
            .set(operateOrgId).equalTo(row::getOperateOrgId)
            .set(operateOrgName).equalTo(row::getOperateOrgName)
            .set(vehicleNo).equalTo(row::getVehicleNo)
            .set(vehicleModelSeq).equalTo(row::getVehicleModelSeq)
            .set(vehicleModelInfo).equalTo(row::getVehicleModelInfo)
            .set(vin).equalTo(row::getVin)
            .set(insuranceCompanyName).equalTo(row::getInsuranceCompanyName)
            .set(repairTypeId).equalTo(row::getRepairTypeId)
            .set(repairTypeName).equalTo(row::getRepairTypeName)
            .set(repairGrade).equalTo(row::getRepairGrade)
            .set(repairDepotId).equalTo(row::getRepairDepotId)
            .set(repairDepotName).equalTo(row::getRepairDepotName)
            .set(repairDepotOrgId).equalTo(row::getRepairDepotOrgId)
            .set(repairDepotSapCode).equalTo(row::getRepairDepotSapCode)
            .set(sapSendStatus).equalTo(row::getSapSendStatus)
            .set(sapSyncFlag).equalTo(row::getSapSyncFlag)
            .set(sapSuccessTime).equalTo(row::getSapSuccessTime)
            .set(partsLibraryType).equalTo(row::getPartsLibraryType)
            .set(taskCreateTime).equalTo(row::getTaskCreateTime)
            .set(taskInflowTime).equalTo(row::getTaskInflowTime)
            .set(vehicleReciveTime).equalTo(row::getVehicleReciveTime)
            .set(vehicleCheckTime).equalTo(row::getVehicleCheckTime)
            .set(vehicleRepairTime).equalTo(row::getVehicleRepairTime)
            .set(reassignmentPassTime).equalTo(row::getReassignmentPassTime)
            .set(settleClosingTime).equalTo(row::getSettleClosingTime)
            .set(expectedRepairDays).equalTo(row::getExpectedRepairDays)
            .set(expectedRepairComplete).equalTo(row::getExpectedRepairComplete)
            .set(accidentReportNumber).equalTo(row::getAccidentReportNumber)
            .set(repairFlag).equalTo(row::getRepairFlag)
            .set(terminalId).equalTo(row::getTerminalId)
            .set(totalMileage).equalTo(row::getTotalMileage)
            .set(terminalMileage).equalTo(row::getTerminalMileage)
            .set(associatedOrder).equalTo(row::getAssociatedOrder)
            .set(orderType).equalTo(row::getOrderType)
            .set(relateType).equalTo(row::getRelateType)
            .set(orderRemark).equalTo(row::getOrderRemark)
            .set(violationName).equalTo(row::getViolationName)
            .set(violationTelNo).equalTo(row::getViolationTelNo)
            .set(vehicleClientMaintenanceFee).equalTo(row::getVehicleClientMaintenanceFee)
            .set(clientInspectTag).equalTo(row::getClientInspectTag)
            .set(clientUpkeepTag).equalTo(row::getClientUpkeepTag)
            .set(authId).equalTo(row::getAuthId)
            .set(noDeductiblesFlag).equalTo(row::getNoDeductiblesFlag)
            .set(serviceType).equalTo(row::getServiceType)
            .set(serviceContent).equalTo(row::getServiceContent)
            .set(driverName).equalTo(row::getDriverName)
            .set(driverTel).equalTo(row::getDriverTel)
            .set(routingInspectionName).equalTo(row::getRoutingInspectionName)
            .set(routingInspectionTel).equalTo(row::getRoutingInspectionTel)
            .set(damagedPartDescribe).equalTo(row::getDamagedPartDescribe)
            .set(accidentDescribe).equalTo(row::getAccidentDescribe)
            .set(trailerFlag).equalTo(row::getTrailerFlag)
            .set(repairReviewTotalAmount).equalTo(row::getRepairReviewTotalAmount)
            .set(repairReplaceTotalAmount).equalTo(row::getRepairReplaceTotalAmount)
            .set(repairRepairTotalAmount).equalTo(row::getRepairRepairTotalAmount)
            .set(repairInsuranceTotalAmount).equalTo(row::getRepairInsuranceTotalAmount)
            .set(vehicleReplaceTotalAmount).equalTo(row::getVehicleReplaceTotalAmount)
            .set(vehicleRepairTotalAmount).equalTo(row::getVehicleRepairTotalAmount)
            .set(vehicleInsuranceTotalAmount).equalTo(row::getVehicleInsuranceTotalAmount)
            .set(vehicleManageViewFlag).equalTo(row::getVehicleManageViewFlag)
            .set(resurveyFlag).equalTo(row::getResurveyFlag)
            .set(resurveyPart).equalTo(row::getResurveyPart)
            .set(recoveryFlag).equalTo(row::getRecoveryFlag)
            .set(dutySituation).equalTo(row::getDutySituation)
            .set(recoveryAmount).equalTo(row::getRecoveryAmount)
            .set(insuranceAmount).equalTo(row::getInsuranceAmount)
            .set(accDepAmount).equalTo(row::getAccDepAmount)
            .set(outageLossAmount).equalTo(row::getOutageLossAmount)
            .set(vehicleLossAmount).equalTo(row::getVehicleLossAmount)
            .set(trailerRescueAmount).equalTo(row::getTrailerRescueAmount)
            .set(maintainAmount).equalTo(row::getMaintainAmount)
            .set(lossOrderAmount).equalTo(row::getLossOrderAmount)
            .set(reassignmentRepairOrgId).equalTo(row::getReassignmentRepairOrgId)
            .set(reassignmentReasons).equalTo(row::getReassignmentReasons)
            .set(reassignmentRejectReasons).equalTo(row::getReassignmentRejectReasons)
            .set(verificationRejectReasons).equalTo(row::getVerificationRejectReasons)
            .set(verificationRejectReasonsDetail).equalTo(row::getVerificationRejectReasonsDetail)
            .set(verificationRejectLevel).equalTo(row::getVerificationRejectLevel)
            .set(checkResultFlag).equalTo(row::getCheckResultFlag)
            .set(checkUnqualifiedReason).equalTo(row::getCheckUnqualifiedReason)
            .set(confirmType).equalTo(row::getConfirmType)
            .set(maintainToRepairFlag).equalTo(row::getMaintainToRepairFlag)
            .set(verificationLossTaskOperId).equalTo(row::getVerificationLossTaskOperId)
            .set(examineLevel).equalTo(row::getExamineLevel)
            .set(verificationLossCheckTime).equalTo(row::getVerificationLossCheckTime)
            .set(verificationLossCheckId).equalTo(row::getVerificationLossCheckId)
            .set(overTimeReasons).equalTo(row::getOverTimeReasons)
            .set(repairTotalAmountFirst).equalTo(row::getRepairTotalAmountFirst)
            .set(nuclearLossReversionFlag).equalTo(row::getNuclearLossReversionFlag)
            .set(remark).equalTo(row::getRemark)
            .set(tirenumber).equalTo(row::getTirenumber)
            .set(tireUnitPrice).equalTo(row::getTireUnitPrice)
            .set(tireBrand).equalTo(row::getTireBrand)
            .set(associatedTaskNo).equalTo(row::getAssociatedTaskNo)
            .set(insuranceFlag).equalTo(row::getInsuranceFlag)
            .set(advancedAuditLeve).equalTo(row::getAdvancedAuditLeve)
            .set(alreadyIncomingAmount).equalTo(row::getAlreadyIncomingAmount)
            .set(alreadyPayAmount).equalTo(row::getAlreadyPayAmount)
            .set(waitPayAmount).equalTo(row::getWaitPayAmount)
            .set(mustClaimAmount).equalTo(row::getMustClaimAmount)
            .set(mustPayAmount).equalTo(row::getMustPayAmount)
            .set(vehicleRepairCostAmount).equalTo(row::getVehicleRepairCostAmount)
            .set(repairSettleAmount).equalTo(row::getRepairSettleAmount)
            .set(repairFaxSettleAmount).equalTo(row::getRepairFaxSettleAmount)
            .set(transferFlag).equalTo(row::getTransferFlag)
            .set(rationalIndemnityCnt).equalTo(row::getRationalIndemnityCnt)
            .set(accidentDayTime).equalTo(row::getAccidentDayTime)
            .set(deductFlag).equalTo(row::getDeductFlag)
            .set(origin).equalTo(row::getOrigin)
            .set(renttype).equalTo(row::getRenttype)
            .set(factOperateTag).equalTo(row::getFactOperateTag)
            .set(takeUserName).equalTo(row::getTakeUserName)
            .set(takeUserPhone).equalTo(row::getTakeUserPhone)
            .set(takeVoucher).equalTo(row::getTakeVoucher)
            .set(synTakeTime).equalTo(row::getSynTakeTime)
            .set(closeReason).equalTo(row::getCloseReason)
            .set(claimsFlag).equalTo(row::getClaimsFlag)
            .set(estimatedClaimAmount).equalTo(row::getEstimatedClaimAmount)
            .set(carDamageType).equalTo(row::getCarDamageType)
            .set(vehicleScrapeValue).equalTo(row::getVehicleScrapeValue)
            .set(reviewToSelFeeFlag).equalTo(row::getReviewToSelFeeFlag)
            .set(confirmCarDamageType).equalTo(row::getConfirmCarDamageType)
            .set(accidentNo).equalTo(row::getAccidentNo)
            .set(insurancePreReviewTime).equalTo(row::getInsurancePreReviewTime)
            .set(insurancePreReviewLevel).equalTo(row::getInsurancePreReviewLevel)
            .set(preReviewVehicleScrapeValue).equalTo(row::getPreReviewVehicleScrapeValue)
            .set(preReviewVehicleScrapeTime).equalTo(row::getPreReviewVehicleScrapeTime)
            .set(isUsedApplets).equalTo(row::getIsUsedApplets)
            .set(criteriaId).equalTo(row::getCriteriaId)
            .set(custPaysDirect).equalTo(row::getCustPaysDirect)
            .set(custAmount).equalTo(row::getCustAmount)
            .set(userAssumedAmount).equalTo(row::getUserAssumedAmount)
            .set(notUserAssumedAmount).equalTo(row::getNotUserAssumedAmount)
            .set(syncClaimFlag).equalTo(row::getSyncClaimFlag)
            .set(taxRate).equalTo(row::getTaxRate)
            .set(repairDepotType).equalTo(row::getRepairDepotType)
            .set(declareNo).equalTo(row::getDeclareNo)
            .set(declareStatus).equalTo(row::getDeclareStatus)
            .set(declareSettlementStatus).equalTo(row::getDeclareSettlementStatus)
            .set(settlementNo).equalTo(row::getSettlementNo)
            .set(settlementStatus).equalTo(row::getSettlementStatus)
            .set(sendRepairTime).equalTo(row::getSendRepairTime)
            .set(situationDesc).equalTo(row::getSituationDesc)
            .set(propertyStatus).equalTo(row::getPropertyStatus)
            .set(productLine).equalTo(row::getProductLine)
            .set(subProductLine).equalTo(row::getSubProductLine)
            .set(selfFundedAmount).equalTo(row::getSelfFundedAmount)
            .set(continueDays).equalTo(row::getContinueDays)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_task")
    default int updateByPrimaryKeySelective(MtcRepairTask row) {
        return update(c ->
            c.set(taskNo).equalToWhenPresent(row::getTaskNo)
            .set(taskType).equalToWhenPresent(row::getTaskType)
            .set(orgId).equalToWhenPresent(row::getOrgId)
            .set(orgName).equalToWhenPresent(row::getOrgName)
            .set(operateOrgId).equalToWhenPresent(row::getOperateOrgId)
            .set(operateOrgName).equalToWhenPresent(row::getOperateOrgName)
            .set(vehicleNo).equalToWhenPresent(row::getVehicleNo)
            .set(vehicleModelSeq).equalToWhenPresent(row::getVehicleModelSeq)
            .set(vehicleModelInfo).equalToWhenPresent(row::getVehicleModelInfo)
            .set(vin).equalToWhenPresent(row::getVin)
            .set(insuranceCompanyName).equalToWhenPresent(row::getInsuranceCompanyName)
            .set(repairTypeId).equalToWhenPresent(row::getRepairTypeId)
            .set(repairTypeName).equalToWhenPresent(row::getRepairTypeName)
            .set(repairGrade).equalToWhenPresent(row::getRepairGrade)
            .set(repairDepotId).equalToWhenPresent(row::getRepairDepotId)
            .set(repairDepotName).equalToWhenPresent(row::getRepairDepotName)
            .set(repairDepotOrgId).equalToWhenPresent(row::getRepairDepotOrgId)
            .set(repairDepotSapCode).equalToWhenPresent(row::getRepairDepotSapCode)
            .set(sapSendStatus).equalToWhenPresent(row::getSapSendStatus)
            .set(sapSyncFlag).equalToWhenPresent(row::getSapSyncFlag)
            .set(sapSuccessTime).equalToWhenPresent(row::getSapSuccessTime)
            .set(partsLibraryType).equalToWhenPresent(row::getPartsLibraryType)
            .set(taskCreateTime).equalToWhenPresent(row::getTaskCreateTime)
            .set(taskInflowTime).equalToWhenPresent(row::getTaskInflowTime)
            .set(vehicleReciveTime).equalToWhenPresent(row::getVehicleReciveTime)
            .set(vehicleCheckTime).equalToWhenPresent(row::getVehicleCheckTime)
            .set(vehicleRepairTime).equalToWhenPresent(row::getVehicleRepairTime)
            .set(reassignmentPassTime).equalToWhenPresent(row::getReassignmentPassTime)
            .set(settleClosingTime).equalToWhenPresent(row::getSettleClosingTime)
            .set(expectedRepairDays).equalToWhenPresent(row::getExpectedRepairDays)
            .set(expectedRepairComplete).equalToWhenPresent(row::getExpectedRepairComplete)
            .set(accidentReportNumber).equalToWhenPresent(row::getAccidentReportNumber)
            .set(repairFlag).equalToWhenPresent(row::getRepairFlag)
            .set(terminalId).equalToWhenPresent(row::getTerminalId)
            .set(totalMileage).equalToWhenPresent(row::getTotalMileage)
            .set(terminalMileage).equalToWhenPresent(row::getTerminalMileage)
            .set(associatedOrder).equalToWhenPresent(row::getAssociatedOrder)
            .set(orderType).equalToWhenPresent(row::getOrderType)
            .set(relateType).equalToWhenPresent(row::getRelateType)
            .set(orderRemark).equalToWhenPresent(row::getOrderRemark)
            .set(violationName).equalToWhenPresent(row::getViolationName)
            .set(violationTelNo).equalToWhenPresent(row::getViolationTelNo)
            .set(vehicleClientMaintenanceFee).equalToWhenPresent(row::getVehicleClientMaintenanceFee)
            .set(clientInspectTag).equalToWhenPresent(row::getClientInspectTag)
            .set(clientUpkeepTag).equalToWhenPresent(row::getClientUpkeepTag)
            .set(authId).equalToWhenPresent(row::getAuthId)
            .set(noDeductiblesFlag).equalToWhenPresent(row::getNoDeductiblesFlag)
            .set(serviceType).equalToWhenPresent(row::getServiceType)
            .set(serviceContent).equalToWhenPresent(row::getServiceContent)
            .set(driverName).equalToWhenPresent(row::getDriverName)
            .set(driverTel).equalToWhenPresent(row::getDriverTel)
            .set(routingInspectionName).equalToWhenPresent(row::getRoutingInspectionName)
            .set(routingInspectionTel).equalToWhenPresent(row::getRoutingInspectionTel)
            .set(damagedPartDescribe).equalToWhenPresent(row::getDamagedPartDescribe)
            .set(accidentDescribe).equalToWhenPresent(row::getAccidentDescribe)
            .set(trailerFlag).equalToWhenPresent(row::getTrailerFlag)
            .set(repairReviewTotalAmount).equalToWhenPresent(row::getRepairReviewTotalAmount)
            .set(repairReplaceTotalAmount).equalToWhenPresent(row::getRepairReplaceTotalAmount)
            .set(repairRepairTotalAmount).equalToWhenPresent(row::getRepairRepairTotalAmount)
            .set(repairInsuranceTotalAmount).equalToWhenPresent(row::getRepairInsuranceTotalAmount)
            .set(vehicleReplaceTotalAmount).equalToWhenPresent(row::getVehicleReplaceTotalAmount)
            .set(vehicleRepairTotalAmount).equalToWhenPresent(row::getVehicleRepairTotalAmount)
            .set(vehicleInsuranceTotalAmount).equalToWhenPresent(row::getVehicleInsuranceTotalAmount)
            .set(vehicleManageViewFlag).equalToWhenPresent(row::getVehicleManageViewFlag)
            .set(resurveyFlag).equalToWhenPresent(row::getResurveyFlag)
            .set(resurveyPart).equalToWhenPresent(row::getResurveyPart)
            .set(recoveryFlag).equalToWhenPresent(row::getRecoveryFlag)
            .set(dutySituation).equalToWhenPresent(row::getDutySituation)
            .set(recoveryAmount).equalToWhenPresent(row::getRecoveryAmount)
            .set(insuranceAmount).equalToWhenPresent(row::getInsuranceAmount)
            .set(accDepAmount).equalToWhenPresent(row::getAccDepAmount)
            .set(outageLossAmount).equalToWhenPresent(row::getOutageLossAmount)
            .set(vehicleLossAmount).equalToWhenPresent(row::getVehicleLossAmount)
            .set(trailerRescueAmount).equalToWhenPresent(row::getTrailerRescueAmount)
            .set(maintainAmount).equalToWhenPresent(row::getMaintainAmount)
            .set(lossOrderAmount).equalToWhenPresent(row::getLossOrderAmount)
            .set(reassignmentRepairOrgId).equalToWhenPresent(row::getReassignmentRepairOrgId)
            .set(reassignmentReasons).equalToWhenPresent(row::getReassignmentReasons)
            .set(reassignmentRejectReasons).equalToWhenPresent(row::getReassignmentRejectReasons)
            .set(verificationRejectReasons).equalToWhenPresent(row::getVerificationRejectReasons)
            .set(verificationRejectReasonsDetail).equalToWhenPresent(row::getVerificationRejectReasonsDetail)
            .set(verificationRejectLevel).equalToWhenPresent(row::getVerificationRejectLevel)
            .set(checkResultFlag).equalToWhenPresent(row::getCheckResultFlag)
            .set(checkUnqualifiedReason).equalToWhenPresent(row::getCheckUnqualifiedReason)
            .set(confirmType).equalToWhenPresent(row::getConfirmType)
            .set(maintainToRepairFlag).equalToWhenPresent(row::getMaintainToRepairFlag)
            .set(verificationLossTaskOperId).equalToWhenPresent(row::getVerificationLossTaskOperId)
            .set(examineLevel).equalToWhenPresent(row::getExamineLevel)
            .set(verificationLossCheckTime).equalToWhenPresent(row::getVerificationLossCheckTime)
            .set(verificationLossCheckId).equalToWhenPresent(row::getVerificationLossCheckId)
            .set(overTimeReasons).equalToWhenPresent(row::getOverTimeReasons)
            .set(repairTotalAmountFirst).equalToWhenPresent(row::getRepairTotalAmountFirst)
            .set(nuclearLossReversionFlag).equalToWhenPresent(row::getNuclearLossReversionFlag)
            .set(remark).equalToWhenPresent(row::getRemark)
            .set(tirenumber).equalToWhenPresent(row::getTirenumber)
            .set(tireUnitPrice).equalToWhenPresent(row::getTireUnitPrice)
            .set(tireBrand).equalToWhenPresent(row::getTireBrand)
            .set(associatedTaskNo).equalToWhenPresent(row::getAssociatedTaskNo)
            .set(insuranceFlag).equalToWhenPresent(row::getInsuranceFlag)
            .set(advancedAuditLeve).equalToWhenPresent(row::getAdvancedAuditLeve)
            .set(alreadyIncomingAmount).equalToWhenPresent(row::getAlreadyIncomingAmount)
            .set(alreadyPayAmount).equalToWhenPresent(row::getAlreadyPayAmount)
            .set(waitPayAmount).equalToWhenPresent(row::getWaitPayAmount)
            .set(mustClaimAmount).equalToWhenPresent(row::getMustClaimAmount)
            .set(mustPayAmount).equalToWhenPresent(row::getMustPayAmount)
            .set(vehicleRepairCostAmount).equalToWhenPresent(row::getVehicleRepairCostAmount)
            .set(repairSettleAmount).equalToWhenPresent(row::getRepairSettleAmount)
            .set(repairFaxSettleAmount).equalToWhenPresent(row::getRepairFaxSettleAmount)
            .set(transferFlag).equalToWhenPresent(row::getTransferFlag)
            .set(rationalIndemnityCnt).equalToWhenPresent(row::getRationalIndemnityCnt)
            .set(accidentDayTime).equalToWhenPresent(row::getAccidentDayTime)
            .set(deductFlag).equalToWhenPresent(row::getDeductFlag)
            .set(origin).equalToWhenPresent(row::getOrigin)
            .set(renttype).equalToWhenPresent(row::getRenttype)
            .set(factOperateTag).equalToWhenPresent(row::getFactOperateTag)
            .set(takeUserName).equalToWhenPresent(row::getTakeUserName)
            .set(takeUserPhone).equalToWhenPresent(row::getTakeUserPhone)
            .set(takeVoucher).equalToWhenPresent(row::getTakeVoucher)
            .set(synTakeTime).equalToWhenPresent(row::getSynTakeTime)
            .set(closeReason).equalToWhenPresent(row::getCloseReason)
            .set(claimsFlag).equalToWhenPresent(row::getClaimsFlag)
            .set(estimatedClaimAmount).equalToWhenPresent(row::getEstimatedClaimAmount)
            .set(carDamageType).equalToWhenPresent(row::getCarDamageType)
            .set(vehicleScrapeValue).equalToWhenPresent(row::getVehicleScrapeValue)
            .set(reviewToSelFeeFlag).equalToWhenPresent(row::getReviewToSelFeeFlag)
            .set(confirmCarDamageType).equalToWhenPresent(row::getConfirmCarDamageType)
            .set(accidentNo).equalToWhenPresent(row::getAccidentNo)
            .set(insurancePreReviewTime).equalToWhenPresent(row::getInsurancePreReviewTime)
            .set(insurancePreReviewLevel).equalToWhenPresent(row::getInsurancePreReviewLevel)
            .set(preReviewVehicleScrapeValue).equalToWhenPresent(row::getPreReviewVehicleScrapeValue)
            .set(preReviewVehicleScrapeTime).equalToWhenPresent(row::getPreReviewVehicleScrapeTime)
            .set(isUsedApplets).equalToWhenPresent(row::getIsUsedApplets)
            .set(criteriaId).equalToWhenPresent(row::getCriteriaId)
            .set(custPaysDirect).equalToWhenPresent(row::getCustPaysDirect)
            .set(custAmount).equalToWhenPresent(row::getCustAmount)
            .set(userAssumedAmount).equalToWhenPresent(row::getUserAssumedAmount)
            .set(notUserAssumedAmount).equalToWhenPresent(row::getNotUserAssumedAmount)
            .set(syncClaimFlag).equalToWhenPresent(row::getSyncClaimFlag)
            .set(taxRate).equalToWhenPresent(row::getTaxRate)
            .set(repairDepotType).equalToWhenPresent(row::getRepairDepotType)
            .set(declareNo).equalToWhenPresent(row::getDeclareNo)
            .set(declareStatus).equalToWhenPresent(row::getDeclareStatus)
            .set(declareSettlementStatus).equalToWhenPresent(row::getDeclareSettlementStatus)
            .set(settlementNo).equalToWhenPresent(row::getSettlementNo)
            .set(settlementStatus).equalToWhenPresent(row::getSettlementStatus)
            .set(sendRepairTime).equalToWhenPresent(row::getSendRepairTime)
            .set(situationDesc).equalToWhenPresent(row::getSituationDesc)
            .set(propertyStatus).equalToWhenPresent(row::getPropertyStatus)
            .set(productLine).equalToWhenPresent(row::getProductLine)
            .set(subProductLine).equalToWhenPresent(row::getSubProductLine)
            .set(selfFundedAmount).equalToWhenPresent(row::getSelfFundedAmount)
            .set(continueDays).equalToWhenPresent(row::getContinueDays)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}