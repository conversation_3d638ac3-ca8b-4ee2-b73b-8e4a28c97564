package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.WorkflowTemplateDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.WorkflowTemplate;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface WorkflowTemplateMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    BasicColumn[] selectList = BasicColumn.columnList(id, workflowName, taskType, repairFactoryType, subProductLine, isActive, tenantId, createTime, createBy, updateTime, updateBy, description);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<WorkflowTemplate> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="WorkflowTemplateResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="workflow_name", property="workflowName", jdbcType=JdbcType.VARCHAR),
        @Result(column="task_type", property="taskType", jdbcType=JdbcType.INTEGER),
        @Result(column="repair_factory_type", property="repairFactoryType", jdbcType=JdbcType.INTEGER),
        @Result(column="sub_product_line", property="subProductLine", jdbcType=JdbcType.INTEGER),
        @Result(column="is_active", property="isActive", jdbcType=JdbcType.INTEGER),
        @Result(column="tenant_id", property="tenantId", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="description", property="description", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<WorkflowTemplate> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("WorkflowTemplateResult")
    Optional<WorkflowTemplate> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, workflowTemplate, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, workflowTemplate, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    default int insert(WorkflowTemplate row) {
        return MyBatis3Utils.insert(this::insert, row, workflowTemplate, c ->
            c.map(workflowName).toProperty("workflowName")
            .map(taskType).toProperty("taskType")
            .map(repairFactoryType).toProperty("repairFactoryType")
            .map(subProductLine).toProperty("subProductLine")
            .map(isActive).toProperty("isActive")
            .map(tenantId).toProperty("tenantId")
            .map(createTime).toProperty("createTime")
            .map(createBy).toProperty("createBy")
            .map(updateTime).toProperty("updateTime")
            .map(updateBy).toProperty("updateBy")
            .map(description).toProperty("description")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    default int insertSelective(WorkflowTemplate row) {
        return MyBatis3Utils.insert(this::insert, row, workflowTemplate, c ->
            c.map(workflowName).toPropertyWhenPresent("workflowName", row::getWorkflowName)
            .map(taskType).toPropertyWhenPresent("taskType", row::getTaskType)
            .map(repairFactoryType).toPropertyWhenPresent("repairFactoryType", row::getRepairFactoryType)
            .map(subProductLine).toPropertyWhenPresent("subProductLine", row::getSubProductLine)
            .map(isActive).toPropertyWhenPresent("isActive", row::getIsActive)
            .map(tenantId).toPropertyWhenPresent("tenantId", row::getTenantId)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(description).toPropertyWhenPresent("description", row::getDescription)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    default Optional<WorkflowTemplate> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, workflowTemplate, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    default List<WorkflowTemplate> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, workflowTemplate, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    default List<WorkflowTemplate> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, workflowTemplate, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    default Optional<WorkflowTemplate> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, workflowTemplate, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    static UpdateDSL<UpdateModel> updateAllColumns(WorkflowTemplate row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(workflowName).equalTo(row::getWorkflowName)
                .set(taskType).equalTo(row::getTaskType)
                .set(repairFactoryType).equalTo(row::getRepairFactoryType)
                .set(subProductLine).equalTo(row::getSubProductLine)
                .set(isActive).equalTo(row::getIsActive)
                .set(tenantId).equalTo(row::getTenantId)
                .set(createTime).equalTo(row::getCreateTime)
                .set(createBy).equalTo(row::getCreateBy)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(description).equalTo(row::getDescription);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(WorkflowTemplate row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(workflowName).equalToWhenPresent(row::getWorkflowName)
                .set(taskType).equalToWhenPresent(row::getTaskType)
                .set(repairFactoryType).equalToWhenPresent(row::getRepairFactoryType)
                .set(subProductLine).equalToWhenPresent(row::getSubProductLine)
                .set(isActive).equalToWhenPresent(row::getIsActive)
                .set(tenantId).equalToWhenPresent(row::getTenantId)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(description).equalToWhenPresent(row::getDescription);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    default int updateByPrimaryKey(WorkflowTemplate row) {
        return update(c ->
            c.set(workflowName).equalTo(row::getWorkflowName)
            .set(taskType).equalTo(row::getTaskType)
            .set(repairFactoryType).equalTo(row::getRepairFactoryType)
            .set(subProductLine).equalTo(row::getSubProductLine)
            .set(isActive).equalTo(row::getIsActive)
            .set(tenantId).equalTo(row::getTenantId)
            .set(createTime).equalTo(row::getCreateTime)
            .set(createBy).equalTo(row::getCreateBy)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(description).equalTo(row::getDescription)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: workflow_template")
    default int updateByPrimaryKeySelective(WorkflowTemplate row) {
        return update(c ->
            c.set(workflowName).equalToWhenPresent(row::getWorkflowName)
            .set(taskType).equalToWhenPresent(row::getTaskType)
            .set(repairFactoryType).equalToWhenPresent(row::getRepairFactoryType)
            .set(subProductLine).equalToWhenPresent(row::getSubProductLine)
            .set(isActive).equalToWhenPresent(row::getIsActive)
            .set(tenantId).equalToWhenPresent(row::getTenantId)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(description).equalToWhenPresent(row::getDescription)
            .where(id, isEqualTo(row::getId))
        );
    }
}