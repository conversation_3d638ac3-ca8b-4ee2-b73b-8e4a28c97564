package com.extracme.saas.autocare.mapper.base;

import static com.extracme.saas.autocare.mapper.base.MtcRepairItemLibraryDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.extracme.saas.autocare.model.entity.MtcRepairItemLibrary;
import java.util.List;
import java.util.Optional;
import javax.annotation.Generated;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MtcRepairItemLibraryMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    BasicColumn[] selectList = BasicColumn.columnList(id, serialNumber, itemNo, itemName, itemType, vehicleModelSeq, vehicleModelInfo, hourFeeNationalMarketPrice, hourFeeNationalHighestPrice, materialCostNationalMarketPrice, materialCostNationalHighestPrice, maintenanceCycle, maintenanceMileage, isCommonlyUsed, remark, status, miscDesc, createBy, createdTime, updateBy, updatedTime);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<MtcRepairItemLibrary> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MtcRepairItemLibraryResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="serial_number", property="serialNumber", jdbcType=JdbcType.INTEGER),
        @Result(column="item_no", property="itemNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="item_name", property="itemName", jdbcType=JdbcType.VARCHAR),
        @Result(column="item_type", property="itemType", jdbcType=JdbcType.INTEGER),
        @Result(column="vehicle_model_seq", property="vehicleModelSeq", jdbcType=JdbcType.BIGINT),
        @Result(column="vehicle_model_info", property="vehicleModelInfo", jdbcType=JdbcType.VARCHAR),
        @Result(column="hour_fee_national_market_price", property="hourFeeNationalMarketPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="hour_fee_national_highest_price", property="hourFeeNationalHighestPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="material_cost_national_market_price", property="materialCostNationalMarketPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="material_cost_national_highest_price", property="materialCostNationalHighestPrice", jdbcType=JdbcType.DECIMAL),
        @Result(column="maintenance_cycle", property="maintenanceCycle", jdbcType=JdbcType.INTEGER),
        @Result(column="maintenance_mileage", property="maintenanceMileage", jdbcType=JdbcType.DECIMAL),
        @Result(column="is_commonly_used", property="isCommonlyUsed", jdbcType=JdbcType.INTEGER),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER),
        @Result(column="misc_Desc", property="miscDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_by", property="createBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_time", property="createdTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.VARCHAR),
        @Result(column="updated_time", property="updatedTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<MtcRepairItemLibrary> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MtcRepairItemLibraryResult")
    Optional<MtcRepairItemLibrary> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, mtcRepairItemLibrary, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, mtcRepairItemLibrary, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    default int insert(MtcRepairItemLibrary row) {
        return MyBatis3Utils.insert(this::insert, row, mtcRepairItemLibrary, c ->
            c.map(serialNumber).toProperty("serialNumber")
            .map(itemNo).toProperty("itemNo")
            .map(itemName).toProperty("itemName")
            .map(itemType).toProperty("itemType")
            .map(vehicleModelSeq).toProperty("vehicleModelSeq")
            .map(vehicleModelInfo).toProperty("vehicleModelInfo")
            .map(hourFeeNationalMarketPrice).toProperty("hourFeeNationalMarketPrice")
            .map(hourFeeNationalHighestPrice).toProperty("hourFeeNationalHighestPrice")
            .map(materialCostNationalMarketPrice).toProperty("materialCostNationalMarketPrice")
            .map(materialCostNationalHighestPrice).toProperty("materialCostNationalHighestPrice")
            .map(maintenanceCycle).toProperty("maintenanceCycle")
            .map(maintenanceMileage).toProperty("maintenanceMileage")
            .map(isCommonlyUsed).toProperty("isCommonlyUsed")
            .map(remark).toProperty("remark")
            .map(status).toProperty("status")
            .map(miscDesc).toProperty("miscDesc")
            .map(createBy).toProperty("createBy")
            .map(createdTime).toProperty("createdTime")
            .map(updateBy).toProperty("updateBy")
            .map(updatedTime).toProperty("updatedTime")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    default int insertSelective(MtcRepairItemLibrary row) {
        return MyBatis3Utils.insert(this::insert, row, mtcRepairItemLibrary, c ->
            c.map(serialNumber).toPropertyWhenPresent("serialNumber", row::getSerialNumber)
            .map(itemNo).toPropertyWhenPresent("itemNo", row::getItemNo)
            .map(itemName).toPropertyWhenPresent("itemName", row::getItemName)
            .map(itemType).toPropertyWhenPresent("itemType", row::getItemType)
            .map(vehicleModelSeq).toPropertyWhenPresent("vehicleModelSeq", row::getVehicleModelSeq)
            .map(vehicleModelInfo).toPropertyWhenPresent("vehicleModelInfo", row::getVehicleModelInfo)
            .map(hourFeeNationalMarketPrice).toPropertyWhenPresent("hourFeeNationalMarketPrice", row::getHourFeeNationalMarketPrice)
            .map(hourFeeNationalHighestPrice).toPropertyWhenPresent("hourFeeNationalHighestPrice", row::getHourFeeNationalHighestPrice)
            .map(materialCostNationalMarketPrice).toPropertyWhenPresent("materialCostNationalMarketPrice", row::getMaterialCostNationalMarketPrice)
            .map(materialCostNationalHighestPrice).toPropertyWhenPresent("materialCostNationalHighestPrice", row::getMaterialCostNationalHighestPrice)
            .map(maintenanceCycle).toPropertyWhenPresent("maintenanceCycle", row::getMaintenanceCycle)
            .map(maintenanceMileage).toPropertyWhenPresent("maintenanceMileage", row::getMaintenanceMileage)
            .map(isCommonlyUsed).toPropertyWhenPresent("isCommonlyUsed", row::getIsCommonlyUsed)
            .map(remark).toPropertyWhenPresent("remark", row::getRemark)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(miscDesc).toPropertyWhenPresent("miscDesc", row::getMiscDesc)
            .map(createBy).toPropertyWhenPresent("createBy", row::getCreateBy)
            .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
            .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    default Optional<MtcRepairItemLibrary> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, mtcRepairItemLibrary, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    default List<MtcRepairItemLibrary> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, mtcRepairItemLibrary, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    default List<MtcRepairItemLibrary> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, mtcRepairItemLibrary, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    default Optional<MtcRepairItemLibrary> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, mtcRepairItemLibrary, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    static UpdateDSL<UpdateModel> updateAllColumns(MtcRepairItemLibrary row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(serialNumber).equalTo(row::getSerialNumber)
                .set(itemNo).equalTo(row::getItemNo)
                .set(itemName).equalTo(row::getItemName)
                .set(itemType).equalTo(row::getItemType)
                .set(vehicleModelSeq).equalTo(row::getVehicleModelSeq)
                .set(vehicleModelInfo).equalTo(row::getVehicleModelInfo)
                .set(hourFeeNationalMarketPrice).equalTo(row::getHourFeeNationalMarketPrice)
                .set(hourFeeNationalHighestPrice).equalTo(row::getHourFeeNationalHighestPrice)
                .set(materialCostNationalMarketPrice).equalTo(row::getMaterialCostNationalMarketPrice)
                .set(materialCostNationalHighestPrice).equalTo(row::getMaterialCostNationalHighestPrice)
                .set(maintenanceCycle).equalTo(row::getMaintenanceCycle)
                .set(maintenanceMileage).equalTo(row::getMaintenanceMileage)
                .set(isCommonlyUsed).equalTo(row::getIsCommonlyUsed)
                .set(remark).equalTo(row::getRemark)
                .set(status).equalTo(row::getStatus)
                .set(miscDesc).equalTo(row::getMiscDesc)
                .set(createBy).equalTo(row::getCreateBy)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updateBy).equalTo(row::getUpdateBy)
                .set(updatedTime).equalTo(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(MtcRepairItemLibrary row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(serialNumber).equalToWhenPresent(row::getSerialNumber)
                .set(itemNo).equalToWhenPresent(row::getItemNo)
                .set(itemName).equalToWhenPresent(row::getItemName)
                .set(itemType).equalToWhenPresent(row::getItemType)
                .set(vehicleModelSeq).equalToWhenPresent(row::getVehicleModelSeq)
                .set(vehicleModelInfo).equalToWhenPresent(row::getVehicleModelInfo)
                .set(hourFeeNationalMarketPrice).equalToWhenPresent(row::getHourFeeNationalMarketPrice)
                .set(hourFeeNationalHighestPrice).equalToWhenPresent(row::getHourFeeNationalHighestPrice)
                .set(materialCostNationalMarketPrice).equalToWhenPresent(row::getMaterialCostNationalMarketPrice)
                .set(materialCostNationalHighestPrice).equalToWhenPresent(row::getMaterialCostNationalHighestPrice)
                .set(maintenanceCycle).equalToWhenPresent(row::getMaintenanceCycle)
                .set(maintenanceMileage).equalToWhenPresent(row::getMaintenanceMileage)
                .set(isCommonlyUsed).equalToWhenPresent(row::getIsCommonlyUsed)
                .set(remark).equalToWhenPresent(row::getRemark)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
                .set(createBy).equalToWhenPresent(row::getCreateBy)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    default int updateByPrimaryKey(MtcRepairItemLibrary row) {
        return update(c ->
            c.set(serialNumber).equalTo(row::getSerialNumber)
            .set(itemNo).equalTo(row::getItemNo)
            .set(itemName).equalTo(row::getItemName)
            .set(itemType).equalTo(row::getItemType)
            .set(vehicleModelSeq).equalTo(row::getVehicleModelSeq)
            .set(vehicleModelInfo).equalTo(row::getVehicleModelInfo)
            .set(hourFeeNationalMarketPrice).equalTo(row::getHourFeeNationalMarketPrice)
            .set(hourFeeNationalHighestPrice).equalTo(row::getHourFeeNationalHighestPrice)
            .set(materialCostNationalMarketPrice).equalTo(row::getMaterialCostNationalMarketPrice)
            .set(materialCostNationalHighestPrice).equalTo(row::getMaterialCostNationalHighestPrice)
            .set(maintenanceCycle).equalTo(row::getMaintenanceCycle)
            .set(maintenanceMileage).equalTo(row::getMaintenanceMileage)
            .set(isCommonlyUsed).equalTo(row::getIsCommonlyUsed)
            .set(remark).equalTo(row::getRemark)
            .set(status).equalTo(row::getStatus)
            .set(miscDesc).equalTo(row::getMiscDesc)
            .set(createBy).equalTo(row::getCreateBy)
            .set(createdTime).equalTo(row::getCreatedTime)
            .set(updateBy).equalTo(row::getUpdateBy)
            .set(updatedTime).equalTo(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: mtc_repair_item_library")
    default int updateByPrimaryKeySelective(MtcRepairItemLibrary row) {
        return update(c ->
            c.set(serialNumber).equalToWhenPresent(row::getSerialNumber)
            .set(itemNo).equalToWhenPresent(row::getItemNo)
            .set(itemName).equalToWhenPresent(row::getItemName)
            .set(itemType).equalToWhenPresent(row::getItemType)
            .set(vehicleModelSeq).equalToWhenPresent(row::getVehicleModelSeq)
            .set(vehicleModelInfo).equalToWhenPresent(row::getVehicleModelInfo)
            .set(hourFeeNationalMarketPrice).equalToWhenPresent(row::getHourFeeNationalMarketPrice)
            .set(hourFeeNationalHighestPrice).equalToWhenPresent(row::getHourFeeNationalHighestPrice)
            .set(materialCostNationalMarketPrice).equalToWhenPresent(row::getMaterialCostNationalMarketPrice)
            .set(materialCostNationalHighestPrice).equalToWhenPresent(row::getMaterialCostNationalHighestPrice)
            .set(maintenanceCycle).equalToWhenPresent(row::getMaintenanceCycle)
            .set(maintenanceMileage).equalToWhenPresent(row::getMaintenanceMileage)
            .set(isCommonlyUsed).equalToWhenPresent(row::getIsCommonlyUsed)
            .set(remark).equalToWhenPresent(row::getRemark)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(miscDesc).equalToWhenPresent(row::getMiscDesc)
            .set(createBy).equalToWhenPresent(row::getCreateBy)
            .set(createdTime).equalToWhenPresent(row::getCreatedTime)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
            .where(id, isEqualTo(row::getId))
        );
    }
}