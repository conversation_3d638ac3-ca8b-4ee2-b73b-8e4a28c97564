package com.extracme.saas.autocare.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.LoginDTO;
import com.extracme.saas.autocare.model.dto.TenantAccountQueryDTO;
import com.extracme.saas.autocare.model.dto.TokenDTO;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.service.AuthService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 认证控制器
 */
@Api(tags = "认证管理", description = "用户认证相关接口")
@RestController
@RequestMapping("/api/v1/auth")
public class AuthController {

    @Autowired
    private AuthService authService;

    /**
     * 发送登录验证码
     *
     * @param loginDTO 登录信息
     * @param request HTTP请求
     * @return 发送结果
     */
    @ApiOperation(value = "发送登录验证码", notes = "向指定手机号发送登录验证码")
    @PostMapping("/code/send")
    public Result<Void> sendCode(
            @ApiParam(value = "登录信息", required = true) @RequestBody LoginDTO loginDTO,
            HttpServletRequest request) {
        // 获取客户端IP
        String ipAddress = getClientIpAddress(request);

        // 发送验证码
        boolean result = authService.sendLoginCode(loginDTO.getMobile(), ipAddress);

        if (result) {
            return Result.success();
        } else {
            return Result.error(400, "验证码发送失败");
        }
    }

    /**
     * 短信验证码登录（支持多租户）
     *
     * @param loginDTO 登录信息
     * @return 登录结果
     */
    @ApiOperation(value = "短信验证码登录", notes = "使用手机号和验证码进行登录，支持多租户账号选择")
    @PostMapping("/login/mobile")
    public Result<TokenDTO> loginByMobile(
            @ApiParam(value = "登录信息", required = true) @RequestBody LoginDTO loginDTO) {
        try {
            // 使用新的多租户登录方法
            TokenDTO tokenDTO = authService.loginByMobileWithTenant(
                loginDTO.getMobile(),
                loginDTO.getCode(),
                loginDTO.getTenantId()
            );
            return Result.success(tokenDTO);
        } catch (BusinessException e) {
            // 业务异常直接抛出，由GlobalExceptionHandler处理
            throw e;
        } catch (Exception e) {
            return Result.error(400, "登录失败，请检查手机号和验证码");
        }
    }

    /**
     * 根据手机号查询用户商户账号列表
     *
     * @param queryDTO 查询条件
     * @return 商户账号列表
     */
    @ApiOperation(value = "查询用户商户账号列表", notes = "根据手机号查询用户名下所有商户账号，用于多租户登录时的账号选择")
    @PostMapping("/tenant-accounts")
    public Result<List<ComboVO<String>>> getUserTenantAccounts(
            @ApiParam(value = "查询条件", required = true) @RequestBody TenantAccountQueryDTO queryDTO) {
        List<ComboVO<String>> accounts = authService.getUserTenantAccounts(queryDTO.getMobile());
        return Result.success(accounts);
    }

    /**
     * 退出登录
     *
     * @param request HTTP请求
     * @return 退出结果
     */
    @ApiOperation(value = "退出登录", notes = "注销当前用户登录状态")
    @GetMapping("/logout")
    public Result<Void> logout(HttpServletRequest request) {
        // 从请求头获取token
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
        }

        boolean result = authService.logout(token);

        if (result) {
            return Result.success();
        } else {
            return Result.error(400, "退出失败");
        }
    }

    /**
     * 获取客户端真实IP地址
     *
     * @param request HTTP请求
     * @return IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");

        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 多个代理的情况，第一个IP为客户真实IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        return ip;
    }
}