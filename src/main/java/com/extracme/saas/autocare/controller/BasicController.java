package com.extracme.saas.autocare.controller;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.extracme.saas.autocare.annotation.RequireLogin;
import com.extracme.saas.autocare.model.dto.ApprovalLevelsCreateDTO;
import com.extracme.saas.autocare.model.dto.ApprovalLevelsUpdateDTO;
import com.extracme.saas.autocare.model.dto.DataDictAddDTO;
import com.extracme.saas.autocare.model.dto.DataDictUpdateDTO;
import com.extracme.saas.autocare.model.dto.OperateLogQueryDTO;
import com.extracme.saas.autocare.model.dto.OrgTenantDTO;
import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import com.extracme.saas.autocare.model.vo.ApprovalLevelsVO;
import com.extracme.saas.autocare.model.vo.DataDictDetailVO;
import com.extracme.saas.autocare.model.vo.DataDictSimpleVO;
import com.extracme.saas.autocare.model.vo.OperateLogVO;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.service.ApprovalLevelsService;
import com.extracme.saas.autocare.service.DataDictService;
import com.extracme.saas.autocare.service.OperateLogService;
import com.extracme.saas.autocare.service.OrgService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 基础数据字典控制器
 */
@Api(tags = "基础数据管理")
@RestController
@RequestMapping("/api/v1/basic")
public class BasicController {

    @Autowired
    private DataDictService dataDictService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private ApprovalLevelsService approvalLevelsService;

    @Autowired
    private OperateLogService operateLogService;

    @ApiOperation("分页查询数据字典列表")
    @PostMapping("/dict/list")
    public Result<BasePageVO<DataDictSimpleVO>> pageDictList(@Validated @RequestBody BasePageDTO pageDTO) {
        return Result.success(dataDictService.pageList(pageDTO));
    }

    @ApiOperation("创建数据字典")
    @PostMapping("/dict/create")
    public <T> Result<Void> create(@Validated @RequestBody DataDictAddDTO<T> addDTO) {
        dataDictService.add(addDTO);
        return Result.success();
    }

    @ApiOperation("修改数据字典")
    @PostMapping("/dict/update")
    public <T> Result<Void> updateDict(@Validated @RequestBody DataDictUpdateDTO<T> updateDTO) {
        dataDictService.update(updateDTO);
        return Result.success();
    }

    @ApiOperation("删除数据字典")
    @PostMapping("/dict/delete")
    public Result<Void> deleteDict(@RequestBody Long id) {
        boolean result = dataDictService.deleteById(id);
        return result ? Result.success() : Result.error("删除失败");
    }

    @ApiOperation("根据编码查询数据字典详情")
    @GetMapping("/dict/detail/{code}")
    public <T> Result<DataDictDetailVO<T>> getDictByCode(
            @ApiParam(value = "数据字典编码", required = true) @PathVariable String code) {
        DataDictDetailVO<T> detail = dataDictService.getByCode(code);
        return detail != null ? Result.success(detail) : Result.error("数据字典不存在");
    }

    /**
     * 获取组织机构下拉列表
     * 根据当前登录用户的租户信息，返回对应的组织机构列表
     *
     * @return 统一封装的组织机构下拉列表数据
     */
    @RequireLogin
    @ApiOperation(value = "获取组织机构下拉列表", notes = "获取当前租户下的组织机构下拉列表数据，id为org_id(String类型)，value为org_name")
    @GetMapping("/org/combo")
    public Result<List<ComboVO<String>>> getOrgCombo() {
        List<ComboVO<String>> data = orgService.getOrgCombo();
        return Result.success(data);
    }

    /**
     * 根据租户ID获取组织机构下拉列表
     * 如果请求中未传递tenantId或tenantId为空，则使用当前登录用户的租户ID进行查询
     * 如果请求中传递了有效的tenantId，则使用传入的租户ID进行查询
     *
     * @param orgTenantDTO 包含租户ID的DTO对象
     * @return 统一封装的组织机构下拉列表数据
     */
    @RequireLogin
    @ApiOperation(value = "根据租户ID获取组织机构下拉列表", notes = "根据指定租户ID获取组织机构下拉列表数据，如果未传递租户ID则使用当前登录用户的租户ID")
    @PostMapping("/org/combo/tenant")
    public Result<List<ComboVO<String>>> getOrgComboByTenant(@RequestBody OrgTenantDTO orgTenantDTO) {
        List<ComboVO<String>> data = orgService.getOrgComboByTenantId(orgTenantDTO != null ? orgTenantDTO.getTenantId() : null);
        return Result.success(data);
    }

     /**
     * 分页查询操作日志列表
     *
     * @param queryDTO 查询参数
     * @return 操作日志列表
     */
    @ApiOperation(value = "分页查询操作日志列表", 
                  notes = "分页查询操作日志列表，支持多租户权限控制。超级管理员可查看所有租户日志，普通用户只能查看自己租户的日志")
    @PostMapping("/log/list")
    public Result<BasePageVO<OperateLogVO>> getOperateLogList(
            @ApiParam(value = "查询参数", required = true) @Valid @RequestBody OperateLogQueryDTO queryDTO) {
        BasePageVO<OperateLogVO> pageInfo = operateLogService.getOperateLogList(queryDTO);
        return Result.success(pageInfo);
    }

    // ==================== 审批层级管理接口 ====================

    /**
     * 新增审批层级
     *
     * @param createDTO 创建DTO
     * @return 统一响应结果
     */
    @RequireLogin
    @ApiOperation(value = "新增审批层级", notes = "创建新的审批层级配置")
    @PostMapping("/approval-levels/create")
    public Result<Void> createApprovalLevels(@Validated @RequestBody ApprovalLevelsCreateDTO createDTO) {
        approvalLevelsService.createApprovalLevels(createDTO);
        return Result.success();
    }

    /**
     * 修改审批层级
     *
     * @param updateDTO 更新DTO
     * @return 统一响应结果
     */
    @RequireLogin
    @ApiOperation(value = "修改审批层级", notes = "更新现有的审批层级配置")
    @PostMapping("/approval-levels/update")
    public Result<Void> updateApprovalLevels(@Validated @RequestBody ApprovalLevelsUpdateDTO updateDTO) {
        approvalLevelsService.updateApprovalLevels(updateDTO);
        return Result.success();
    }

    /**
     * 删除审批层级
     *
     * @param id 审批层级ID
     * @return 统一响应结果
     */
    @RequireLogin
    @ApiOperation(value = "删除审批层级", notes = "根据ID删除审批层级配置")
    @GetMapping("/approval-levels/delete/{id}")
    public Result<Void> deleteApprovalLevels(
            @ApiParam(value = "审批层级ID", required = true) @PathVariable Long id) {
        approvalLevelsService.deleteApprovalLevels(id);
        return Result.success();
    }

    /**
     * 分页查询审批层级列表
     * 根据当前登录用户的租户查询审批层级列表
     *
     * @param pageDTO 分页参数
     * @return 分页查询结果
     */
    @RequireLogin
    @ApiOperation(value = "查询审批层级列表", notes = "分页查询审批层级列表，基于当前用户租户进行数据隔离")
    @PostMapping("/approval-levels/list")
    public Result<BasePageVO<ApprovalLevelsVO>> getApprovalLevelsList(@Validated @RequestBody BasePageDTO pageDTO) {
        BasePageVO<ApprovalLevelsVO> result = approvalLevelsService.getApprovalLevelsList(pageDTO);
        return Result.success(result);
    }

    /**
     * 根据租户ID查询审批层级下拉框选项
     * 如果请求中未传递tenantId或tenantId为空，则使用当前登录用户的租户ID进行查询
     * 如果请求中传递了有效的tenantId，则使用传入的租户ID进行查询
     *
     * @param orgTenantDTO 包含租户ID的DTO对象
     * @return 下拉框选项列表
     */
    @RequireLogin
    @ApiOperation(value = "获取审批层级下拉框选项", notes = "根据指定租户ID获取审批层级下拉框选项，如果未传递租户ID则使用当前登录用户的租户ID")
    @PostMapping("/approval-levels/combo/tenant")
    public Result<List<ComboVO<Long>>> getApprovalLevelsCombo(@RequestBody OrgTenantDTO orgTenantDTO) {
        List<ComboVO<Long>> data = approvalLevelsService.getApprovalLevelsCombo(orgTenantDTO != null ? orgTenantDTO.getTenantId() : null);
        return Result.success(data);
    }
}