package com.extracme.saas.autocare.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionCreateDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionDeleteDTO;
import com.extracme.saas.autocare.model.dto.workflow.ActivityTransitionUpdateDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowInstanceQueryDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowProcessDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowStartDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowTemplateCreateDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowTemplateQueryDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowTemplateStatusDTO;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.model.vo.workflow.ActivityTransitionDetailVO;
import com.extracme.saas.autocare.model.vo.workflow.ActivityTransitionVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowInstanceDetailVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowInstanceVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowProgressVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowTemplateDetailVO;
import com.extracme.saas.autocare.model.vo.workflow.WorkflowTemplateVO;
import com.extracme.saas.autocare.service.WorkflowService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 工作流控制器
 * 提供工作流模板管理、流程实例管理等相关接口
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@RestController
@RequestMapping("/api/v1/workflow")
@Api(tags = "工作流管理", description = "工作流模板和实例管理相关接口")
public class WorkflowController {

    @Resource
    private WorkflowService workflowService;

    /**
     * 创建工作流模板
     *
     * @param createDTO 创建工作流模板请求对象
     * @return 创建成功的模板ID
     */
    @PostMapping("/template/create")
    @ApiOperation(value = "创建工作流模板", notes = "创建新的工作流模板")
    public Result<Long> createTemplate(
            @ApiParam(value = "工作流模板创建信息", required = true) @RequestBody @Validated WorkflowTemplateCreateDTO createDTO) {
        return Result.success(workflowService.createTemplate(createDTO));
    }

    /**
     * 获取工作流模板详情
     *
     * @param templateId 模板ID
     * @return 工作流模板详情视图对象
     */
    @GetMapping("/template/detail/{templateId}")
    @ApiOperation(value = "获取工作流模板详情", notes = "获取指定ID的工作流模板详细信息")
    public Result<WorkflowTemplateDetailVO> getTemplateDetail(
            @ApiParam(value = "模板ID", required = true) @PathVariable Long templateId) {
        return Result.success(workflowService.getTemplateDetail(templateId));
    }

    /**
     * 查询工作流模板列表
     *
     * @param queryDTO 查询条件对象
     * @return 工作流模板分页列表
     */
    @PostMapping("/template/list")
    @ApiOperation(value = "查询工作流模板列表", notes = "根据条件分页查询工作流模板列表")
    public Result<BasePageVO<WorkflowTemplateVO>> getTemplateList(
            @ApiParam(value = "查询条件", required = true) @RequestBody @Validated WorkflowTemplateQueryDTO queryDTO) {
        return Result.success(workflowService.getTemplateList(queryDTO));
    }

    /**
     * 启动工作流实例
     *
     * @param startDTO 启动工作流实例请求对象
     * @return 创建的工作流实例ID
     */
    @PostMapping("/instance/start")
    @ApiOperation(value = "启动工作流实例", notes = "创建并启动新的工作流实例")
    public Result<Long> startWorkflow(
            @ApiParam(value = "工作流启动信息", required = true) @RequestBody @Validated WorkflowStartDTO startDTO) {
        return Result.success(workflowService.startWorkflow(startDTO));
    }

    /**
     * 处理工作流节点
     *
     * @param instanceId 实例ID
     * @param processDTO 节点处理请求对象
     * @return 操作结果
     */
    @PostMapping("/instance/process/{instanceId}")
    @ApiOperation(value = "处理工作流节点", notes = "处理指定工作流实例的当前节点")
    public Result<Void> processNode(
            @ApiParam(value = "实例ID", required = true) @PathVariable Long instanceId,
            @ApiParam(value = "节点处理信息", required = true) @RequestBody @Validated WorkflowProcessDTO processDTO) {
        workflowService.processNode(instanceId, processDTO);
        return Result.success();
    }

    /**
     * 获取工作流实例详情
     *
     * @param instanceId 实例ID
     * @return 工作流实例详情视图对象
     */
    @GetMapping("/instance/detail/{instanceId}")
    @ApiOperation(value = "获取工作流实例详情", notes = "获取指定ID的工作流实例详细信息")
    public Result<WorkflowInstanceDetailVO> getInstanceDetail(
            @ApiParam(value = "实例ID", required = true) @PathVariable Long instanceId) {
        return Result.success(workflowService.getInstanceDetail(instanceId));
    }

    /**
     * 查询工作流实例列表
     *
     * @param queryDTO 查询条件对象
     * @return 工作流实例分页列表
     */
    @PostMapping("/instance/list")
    @ApiOperation(value = "查询工作流实例列表", notes = "根据条件分页查询工作流实例列表")
    public Result<BasePageVO<WorkflowInstanceVO>> getInstanceList(
            @ApiParam(value = "查询条件", required = true) @RequestBody @Validated WorkflowInstanceQueryDTO queryDTO) {
        return Result.success(workflowService.getInstanceList(queryDTO));
    }

    /**
     * 获取工作流实例进度
     *
     * @param instanceId 实例ID
     * @return 工作流进度视图对象
     */
    @GetMapping("/instance/progress/{instanceId}")
    @ApiOperation(value = "获取工作流实例进度", notes = "获取指定工作流实例的当前进度信息")
    public Result<WorkflowProgressVO> getInstanceProgress(
            @ApiParam(value = "实例ID", required = true) @PathVariable Long instanceId) {
        return Result.success(workflowService.getInstanceProgress(instanceId));
    }

    /**
     * 更改工作流模板状态（启用/禁用）
     *
     * @param statusDTO 工作流模板状态更新请求
     * @return 操作结果
     */
    @PostMapping("/template/status")
    @ApiOperation("更改工作流模板状态")
    public Result<Void> changeTemplateStatus(@Validated @RequestBody WorkflowTemplateStatusDTO statusDTO) {
        workflowService.updateTemplateStatus(statusDTO.getTemplateId(), statusDTO.getIsActive());
        return Result.success();
    }

    /**
     * 创建活动节点转换规则
     *
     * @param createDTO 创建活动节点转换规则请求对象
     * @return 创建成功的活动节点转换规则ID
     */
    @PostMapping("/transition/create")
    @ApiOperation(value = "创建活动节点转换规则", notes = "创建新的活动节点转换规则及关联的状态转换规则")
    public Result<Long> createActivityTransition(
            @ApiParam(value = "活动节点转换规则创建信息", required = true) @RequestBody @Validated ActivityTransitionCreateDTO createDTO) {
        return Result.success(workflowService.createActivityTransition(createDTO));
    }

    /**
     * 更新活动节点转换规则
     *
     * @param updateDTO 更新活动节点转换规则请求对象
     * @return 操作结果
     */
    @PostMapping("/transition/update")
    @ApiOperation(value = "更新活动节点转换规则", notes = "更新活动节点转换规则及关联的状态转换规则")
    public Result<Void> updateActivityTransition(
            @ApiParam(value = "活动节点转换规则更新信息", required = true) @RequestBody @Validated ActivityTransitionUpdateDTO updateDTO) {
        workflowService.updateActivityTransition(updateDTO);
        return Result.success();
    }

    /**
     * 删除活动节点转换规则
     *
     * @param deleteDTO 删除活动节点转换规则请求对象
     * @return 操作结果
     */
    @PostMapping("/transition/delete")
    @ApiOperation(value = "删除活动节点转换规则", notes = "删除活动节点转换规则及关联的状态转换规则")
    public Result<Void> deleteActivityTransition(
            @ApiParam(value = "活动节点转换规则删除信息", required = true) @RequestBody @Validated ActivityTransitionDeleteDTO deleteDTO) {
        workflowService.deleteActivityTransition(deleteDTO);
        return Result.success();
    }

    /**
     * 获取活动节点转换规则详情
     *
     * @param transitionId 转换规则ID
     * @return 活动节点转换规则详情视图对象
     */
    @GetMapping("/transition/detail/{transitionId}")
    @ApiOperation(value = "获取活动节点转换规则详情", notes = "获取指定ID的活动节点转换规则及关联的状态转换规则详细信息")
    public Result<ActivityTransitionDetailVO> getActivityTransitionDetail(
            @ApiParam(value = "转换规则ID", required = true) @PathVariable Long transitionId) {
        return Result.success(workflowService.getActivityTransitionDetail(transitionId));
    }

    /**
     * 根据工作流ID查询活动节点转换规则列表
     *
     * @param workflowId 工作流ID
     * @return 活动节点转换规则列表
     */
    @GetMapping("/transition/list/{workflowId}")
    @ApiOperation(value = "查询活动节点转换规则列表", notes = "根据工作流ID查询活动节点转换规则列表")
    public Result<List<ActivityTransitionVO>> getActivityTransitionList(
            @ApiParam(value = "工作流ID", required = true) @PathVariable Long workflowId) {
        return Result.success(workflowService.listActivityTransitionsByWorkflowId(workflowId));
    }


}