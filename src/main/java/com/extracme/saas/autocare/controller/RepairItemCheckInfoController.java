package com.extracme.saas.autocare.controller;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.RepairItemCheckInfoDTO;
import com.extracme.saas.autocare.model.vo.RepairItemCheckInfoVO;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.vo.base.BaseIdVO;
import com.extracme.saas.autocare.service.RepairItemCheckInfoService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * 自由配件库管理
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "自由配件库管理")
@RestController
@RequestMapping(value = "/api/v1/item-check")
public class RepairItemCheckInfoController {

    @Autowired
    private RepairItemCheckInfoService repairItemCheckInfoService;

    /**
     * 新增定损项目
     *
     * @param vo 维修项目核损信息VO
     * @return 操作结果
     */
    @ApiOperation(value = "新增定损项目", notes = "添加维修项目核损信息")
    @PostMapping("/addItemCheckInfo")
    public Result<Void> addItemCheckInfo(@Valid @RequestBody RepairItemCheckInfoVO vo) {
        try {
            log.info("新增定损项目, 任务编号: {}", vo.getTaskNo());
            repairItemCheckInfoService.addItemCheckInfo(vo.getTaskNo(), vo.getList());
            return Result.success();
        } catch (BusinessException e) {
            log.error("新增定损项目业务异常, 任务编号: {}, 错误信息: {}", vo.getTaskNo(), e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("新增定损项目系统异常, 任务编号: {}", vo.getTaskNo(), e);
            return Result.error("维修系统异常");
        }
    }

    /**
     * 更新核损信息
     *
     * @param vo 维修项目核损信息VO
     * @return 操作结果
     */
    @ApiOperation(value = "更新核损信息", notes = "更新维修项目核损信息")
    @PostMapping("/updateItemCheckInfo")
    public Result<Void> updateItemCheckInfo(@Valid @RequestBody RepairItemCheckInfoVO vo) {
        try {
            log.info("更新核损信息, 任务编号: {}", vo.getTaskNo());
            repairItemCheckInfoService.updateItemCheckInfo(vo.getTaskNo(), vo.getList());
            return Result.success();
        } catch (BusinessException e) {
            log.error("更新核损信息业务异常, 任务编号: {}, 错误信息: {}", vo.getTaskNo(), e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("更新核损信息系统异常, 任务编号: {}", vo.getTaskNo(), e);
            return Result.error("维修系统异常");
        }
    }

    /**
     * 移除定损项目
     *
     * @param id 维修项目核损信息ID
     * @return 操作结果
     */
    @ApiOperation(value = "移除定损项目", notes = "根据ID移除维修项目核损信息")
    @PostMapping("/removeItem")
    public Result<Void> removeItem(@Valid @RequestBody BaseIdVO vo) {
        try {
            log.info("移除定损项目, ID: {}", vo.getId());
            repairItemCheckInfoService.updateCheckInfoStatus(vo.getId());
            return Result.success();
        } catch (BusinessException e) {
            log.error("移除定损项目业务异常, ID: {}, 错误信息: {}", vo.getId(), e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("移除定损项目系统异常, ID: {}", vo.getId(), e);
            return Result.error("维修系统异常");
        }
    }

    /**
     * 查询定损/预审项目列表
     *
     * @param taskNo                   任务编号
     * @param insurancePreReviewStatus 预审状态（0非进保预审 1进保预审）
     * @return 维修项目核损信息列表
     */
    @ApiOperation(value = "查询定损/预审项目列表", notes = "根据任务编号和预审状态查询维修项目核损信息列表")
    @GetMapping("/queryCheckList/{taskNo}/{insurancePreReviewStatus}")
    public Result<List<RepairItemCheckInfoDTO>> queryCheckList(
            @ApiParam(value = "任务编号", required = true) @PathVariable String taskNo,
            @ApiParam(value = "预审状态（0非进保预审 1进保预审）", required = true) @PathVariable Integer insurancePreReviewStatus) {
        try {
            log.info("查询定损/预审项目列表, 任务编号: {}, 预审状态: {}", taskNo, insurancePreReviewStatus);
            List<RepairItemCheckInfoDTO> list = repairItemCheckInfoService
                    .queryCheckListByTaskNoAndInsurancePreReviewStatus(
                            taskNo, insurancePreReviewStatus);
            return Result.success(list);
        } catch (BusinessException e) {
            log.error("查询定损/预审项目列表业务异常, 任务编号: {}, 预审状态: {}, 错误信息: {}",
                    taskNo, insurancePreReviewStatus, e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询定损/预审项目列表系统异常, 任务编号: {}, 预审状态: {}",
                    taskNo, insurancePreReviewStatus, e);
            return Result.error("维修系统异常");
        }
    }

    /**
     * 查询核损列表
     *
     * @param taskNo 任务编号
     * @param orgId  组织ID
     * @return 维修项目核损信息列表
     */
    @ApiOperation(value = "查询核损列表", notes = "根据任务编号和组织ID查询维修项目核损信息列表")
    @GetMapping("/queryViewList/{taskNo}/{orgId}")
    public Result<List<RepairItemCheckInfoDTO>> queryViewList(
            @ApiParam(value = "任务编号", required = true) @PathVariable String taskNo,
            @ApiParam(value = "组织ID", required = true) @PathVariable String orgId) {
        try {
            log.info("查询核损列表, 任务编号: {}, 组织ID: {}", taskNo, orgId);
            List<RepairItemCheckInfoDTO> list = repairItemCheckInfoService.queryViewListByTaskNo(taskNo, orgId);
            return Result.success(list);
        } catch (BusinessException e) {
            log.error("查询核损列表业务异常, 任务编号: {}, 组织ID: {}, 错误信息: {}",
                    taskNo, orgId, e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询核损列表系统异常, 任务编号: {}, 组织ID: {}", taskNo, orgId, e);
            return Result.error("维修系统异常");
        }
    }
}