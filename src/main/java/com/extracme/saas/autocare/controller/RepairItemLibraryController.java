package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.model.dto.*;
import com.extracme.saas.autocare.model.vo.*;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.service.RepairItemLibraryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 维修项目库管理接口 -4
 */

@Api(tags = "维修项目管理")
@RestController
@RequestMapping("/api/v1/item-library")
public class RepairItemLibraryController {

    @Autowired
    private RepairItemLibraryService repairItemLibraryService;

    /**
     * 查询维修项目列表
     * @param queryDTO 查询参数
     * @return 维修项目列表
     */
    @ApiOperation(value = "查询维修项目列表", notes = "分页查询列表")
    @PostMapping("/list")
    public Result<BasePageVO<RepairItemLibraryListVO>> queryList(@Valid @RequestBody RepairItemLibraryQueryDTO queryDTO) {
        BasePageVO<RepairItemLibraryListVO> pageVO = repairItemLibraryService.queryItemLibraryList(queryDTO);
        return Result.success(pageVO);
    }

    /**
     * 获取维修项目详情
     */
    @ApiOperation(value = "获取维修项目详情", notes = "获取指定ID的维修项目详细信息")
    @GetMapping("/details/{id}")
    public Result<RepairItemLibraryVO> getItemLibraryDetails(@ApiParam(value = "维修项目id 必传", required = true) @PathVariable Long id) {
        RepairItemLibraryVO repairItemLibrary = repairItemLibraryService.getItemLibraryDetails(id);
        if (repairItemLibrary == null){
            return Result.error("未找到该数据");
        }
        return Result.success(repairItemLibrary);
    }


    @ApiOperation(value = "创建维修项目")
    @PostMapping("/create")
    public Result<Void> createItemLibrary(@Valid @RequestBody RepairItemLibraryCreateDTO createDTO) {
        // 项目类型 1：保养 2：终端 3：维修
        int itemType = createDTO.getItemType();
        if (itemType == 3 && (createDTO.getVehicleModelSeq() == null || createDTO.getVehicleModelSeq() == 0)){
            return Result.error("终端维修项目必须指定车型");
        }
        repairItemLibraryService.createItemLibrary(createDTO);
        return Result.success();
    }


    @ApiOperation(value = "修改维修项目")
    @PostMapping("/update")
    public Result<Void> updateItemLibrary(@Valid @RequestBody RepairItemLibraryUpdateDTO updateDTO) {
        // 项目类型 1：保养 2：终端 3：维修
        int itemType = updateDTO.getItemType();
        if (itemType == 3 && (updateDTO.getVehicleModelSeq() == null || updateDTO.getVehicleModelSeq() == 0)){
            return Result.error("终端维修项目必须指定车型");
        }
        repairItemLibraryService.updateItemLibrary(updateDTO);
        return Result.success();
    }

    @ApiOperation(value = "更新维修项目状态", httpMethod = "POST")
    @PostMapping("/updateStatus")
    public Result<Void> updateStatus(@Valid @RequestBody RepairItemLibraryUpdateStatusDTO updateStatusDTO) {
        repairItemLibraryService.updateItemLibraryStatus(updateStatusDTO);
        return Result.success();
    }


    /**
     * 查询本地维修项目列表
     * @param queryDTO 查询参数
     * @return 维修项目列表
     */
    @ApiOperation(value = "查询本地维修项目列表", notes = "分页查询本地维修项目列表")
    @PostMapping("/localList")
    public Result<BasePageVO<RepairItemLibraryLocalVO>> queryLocalList(@Valid @RequestBody RepairItemLibraryLocalQueryDTO queryDTO) {
        BasePageVO<RepairItemLibraryLocalVO> pageVO = repairItemLibraryService.queryLocalList(queryDTO);
        return Result.success(pageVO);
    }
    /**
     * 获取本地维修项目详情
     */
    @ApiOperation(value = "获取本地维修项目详情", notes = "获取指定ID的本地维修项目详细信息")
    @GetMapping("/localDetails/{id}")
    public Result<RepairItemLibraryLocalVO> getItemLibraryLocalDetails(@ApiParam(value = "维修项目id 必传", required = true) @PathVariable Long id) {
        RepairItemLibraryLocalVO repairItemLibraryLocal = repairItemLibraryService.getItemLibraryLocalDetails(id);
        if (repairItemLibraryLocal == null){
            return Result.error("未找到该数据");
        }
        return Result.success(repairItemLibraryLocal);
    }


    @ApiOperation(value = "创建本地维修项目")
    @PostMapping("/createLocal")
    public Result<Void> createItemLibraryLocal(@Valid @RequestBody RepairItemLibraryLocalCreateDTO createDTO) {
        repairItemLibraryService.createItemLibraryLocal(createDTO);
        return Result.success();
    }


    @ApiOperation(value = "修改本地维修项目")
    @PostMapping("/updateLocal")
    public Result<Void> updateItemLibraryLocal(@Valid @RequestBody RepairItemLibraryLocalUpdateDTO updateDTO) {
        repairItemLibraryService.updateItemLibraryLocal(updateDTO);
        return Result.success();
    }

    @ApiOperation(value = "更新本地维修项目状态", httpMethod = "POST")
    @PostMapping("/updateLocalStatus")
    public Result<Void> updateLocalStatus(@Valid @RequestBody RepairItemLibraryUpdateStatusDTO updateStatusDTO) {
        repairItemLibraryService.updateItemLibraryLocalStatus(updateStatusDTO);
        return Result.success();
    }

    /**
     * 查询待定损配件库列表
     * @param queryDTO 查询参数
     * @return 维修项目列表
     */
    @ApiOperation(value = "查询待定损配件库列表", notes = "分页查询待定损配件库列表")
    @PostMapping("/queryLibraryCheckList")
    public Result<BasePageVO<RepairItemLibraryCheckListVO>> queryLibraryCheckList(@Valid @RequestBody RepairItemLibraryCheckQueryDTO queryDTO) {
        BasePageVO<RepairItemLibraryCheckListVO> pageVO = repairItemLibraryService.queryLibraryCheckList(queryDTO);
        return Result.success(pageVO);
    }
}