package com.extracme.saas.autocare.controller;

import com.extracme.saas.autocare.model.dto.PermissionDTO;
import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.vo.PermissionTreeVO;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.service.PermissionService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

/**
 * 权限管理控制器
 */
@Api(tags = "权限管理", description = "权限管理相关接口")
@RestController
@RequestMapping("/api/v1/permission")
public class PermissionController {

    @Resource
    private PermissionService permissionService;

    /**
     * 获取权限树
     *
     * 返回系统中所有权限的树形结构，用于角色权限分配页面展示
     * 树形结构按照权限的层级关系组织，每个节点包含权限的基本信息和子节点列表
     */
    @ApiOperation(value = "获取权限列表", notes = "获取系统权限的树形结构，用于角色权限分配")
    @GetMapping("/tree")
    public Result<List<PermissionTreeVO>> getPermissionTree() {
        return Result.success(permissionService.getPermissionTree());
    }

    /**
     * 创建权限
     *
     * 创建新的系统权限，包括菜单、按钮和隐藏菜单权限
     * 权限编码必须唯一，权限类型必须是menu、button或hiddenMenu之一
     */
    @ApiOperation(value = "创建权限", notes = "创建新的系统权限，权限编码将自动生成UUID")
    @PostMapping("/create")
    public Result<Long> createPermission(
            @ApiParam(value = "权限信息", required = true) @Validated @RequestBody PermissionDTO permissionDTO) {
        SysPermission permission = new SysPermission();
        BeanUtils.copyProperties(permissionDTO, permission);

        // 如果权限编码为空，则生成UUID作为权限编码
        if(StringUtils.isBlank(permission.getPermissionCode())){    
            // 生成UUID作为权限编码
            String uuid = UUID.randomUUID().toString().replace("-", "");
            permission.setPermissionCode(uuid);
        }

        return Result.success(permissionService.createPermission(permission));
    }

    /**
     * 更新权限
     *
     * 更新指定ID的权限信息
     * 权限编码必须唯一，权限类型必须是menu、button或hiddenMenu之一
     */
    @ApiOperation(value = "更新权限", notes = "更新指定ID的权限信息")
    @PostMapping("/update")
    public Result<Void> updatePermission(
            @ApiParam(value = "权限信息", required = true) @Validated @RequestBody PermissionDTO permissionDTO) {
        if (permissionDTO.getId() == null) {
            return Result.error("权限ID不能为空");
        }
        if (permissionDTO.getPermissionCode() == null || permissionDTO.getPermissionCode().isEmpty()) {
            return Result.error("权限编码不能为空");
        }
        SysPermission permission = new SysPermission();
        BeanUtils.copyProperties(permissionDTO, permission);
        permissionService.updatePermission(permission);
        return Result.success();
    }

    /**
     * 删除权限
     *
     * 删除指定ID的权限
     * 如果权限正在被角色使用，则无法删除
     */
    @ApiOperation(value = "删除权限", notes = "删除指定ID的权限")
    @PostMapping("/delete")
    public Result<Void> deletePermission(
            @ApiParam(value = "权限ID", required = true) @RequestBody Long id) {
        permissionService.deletePermission(id);
        return Result.success();
    }
}
