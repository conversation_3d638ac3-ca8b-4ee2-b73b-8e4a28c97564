package com.extracme.saas.autocare.controller;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.extracme.saas.autocare.model.dto.LossAssessmentApproveDTO;
import com.extracme.saas.autocare.model.dto.RepairDepotSelectDTO;
import com.extracme.saas.autocare.model.dto.RepairQuoteSubmitDTO;
import com.extracme.saas.autocare.model.dto.RepairQuoteUpdateDTO;
import com.extracme.saas.autocare.model.dto.RepairRemarkDTO;
import com.extracme.saas.autocare.model.dto.TaskNoDTO;
import com.extracme.saas.autocare.model.dto.VehicleInspectionApproveDTO;
import com.extracme.saas.autocare.model.dto.VehicleInspectionRejectDTO;
import com.extracme.saas.autocare.model.dto.VehicleRepairActivityUpdateDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskCreateDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskListQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskProcessQueryDTO;
import com.extracme.saas.autocare.model.vo.MtcTaskListResultVO;
import com.extracme.saas.autocare.model.vo.RepairTaskDetailsVO;
import com.extracme.saas.autocare.model.vo.RepairTaskProcessListVO;
import com.extracme.saas.autocare.model.vo.Result;
import com.extracme.saas.autocare.model.vo.base.BasePageVO;
import com.extracme.saas.autocare.service.LossAssessmentService;
import com.extracme.saas.autocare.service.RepairQuoteService;
import com.extracme.saas.autocare.service.RepairTaskService;
import com.extracme.saas.autocare.service.VehicleInspectionService;
import com.extracme.saas.autocare.service.VehicleRepairActivityService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 维修任务管理接口
 */
@Api(tags = "维修任务管理")
@RestController
@RequestMapping("/api/v1/repair-task")
public class RepairTaskController {

    @Autowired
    private RepairTaskService repairTaskService;
    
    @Autowired
    private RepairQuoteService repairQuoteService;
    
    @Autowired
    private LossAssessmentService lossAssessmentService;
    
    @Autowired
    private VehicleRepairActivityService vehicleRepairActivityService;

    @Autowired
    private VehicleInspectionService vehicleInspectionService;

    /**
     * 创建维修任务
     * 
     * @param createDTO 创建维修任务参数
     */
    @ApiOperation(value = "创建维修任务")
    @PostMapping("/create")
    public Result<Long> createRepairTask(@Valid @RequestBody RepairTaskCreateDTO createDTO) {
        Long taskId = repairTaskService.createRepairTask(createDTO);
        return Result.success(taskId);
    }

    /**
     * 流程查询-查询维修任务流程列表
     * 
     * @param queryDTO 查询参数
     * @return 维修任务列表
     */
    @ApiOperation(value = "流程查询-查询维修任务流程列表", notes = "分页查询列表")
    @PostMapping("/processList")
    public Result<BasePageVO<RepairTaskProcessListVO>> queryRepairTaskProcessList(@Valid @RequestBody RepairTaskProcessQueryDTO queryDTO) {
        BasePageVO<RepairTaskProcessListVO> pageVO = repairTaskService.queryRepairTaskProcessList(queryDTO);
        return Result.success(pageVO);
    }

    /**
     * 各环节-查询维修任务列表
     * 
     * @param queryDTO 查询参数
     * @return 维修任务列表
     */
    @ApiOperation(value = "各环节-查询维修任务列表", notes = "分页查询列表")
    @PostMapping("/listResult")
    public Result<MtcTaskListResultVO> queryRepairTaskList(@Valid @RequestBody RepairTaskListQueryDTO queryDTO) {
        MtcTaskListResultVO resultVO = repairTaskService.queryRepairTaskList(queryDTO);
        return Result.success(resultVO);
    }

    /**
     * 获取维修任务详情
     */
    @ApiOperation(value = "获取维修任务详情", notes = "获取指定ID的维修任务详细信息")
    @GetMapping("/details/{id}")
    public Result<RepairTaskDetailsVO> getRepairTaskDetails(
            @ApiParam(value = "维修任务id 必传", required = true) @PathVariable Long id) {
        RepairTaskDetailsVO repairTask = repairTaskService.getRepairTaskDetails(id);
        if (repairTask == null) {
            return Result.error("未找到该数据");
        }
        return Result.success(repairTask);
    }

    /**
     * 维修报价-提交审核
     *
     * @param submitDTO 提交维修报价审核参数
     * @return 提交结果
     */
    @ApiOperation(value = "维修报价-提交审核", notes = "提交维修报价信息")
    @PostMapping("/repairQuotation/submit")
    public Result<Boolean> submitRepairQuote(@Valid @RequestBody RepairQuoteSubmitDTO submitDTO) {
        repairQuoteService.submitRepairQuote(submitDTO);
        return Result.success();
    }
    
    /**
     * 核损核价-审核通过
     *
     * @param approveDTO 核损核价审核参数
     * @return 审核结果
     */
    @ApiOperation(value = "核损核价-审核通过", notes = "审核通过核损核价信息")
    @PostMapping("/lossAssessment/approve")
    public Result<Void> approveLossAssessment(@Valid @RequestBody LossAssessmentApproveDTO approveDTO) {
        lossAssessmentService.approveLossAssessment(approveDTO);
        return Result.success();
    }
    
    /**
     * 车辆维修-申请验收
     *
     * @param id 维修任务ID
     * @param updateDTO 车辆维修活动更新参数
     * @return 操作结果
     */
    @ApiOperation(value = "车辆维修-申请验收", notes = "车辆维修完成后申请验收")
    @PostMapping("/vehicleRepair/requestInspection")
    public Result<Void> changeVehicleCheck(@ApiParam(value = "车辆维修活动更新信息", required = true) @Valid @RequestBody VehicleRepairActivityUpdateDTO updateDTO) {
        vehicleRepairActivityService.changeVehicleCheck(updateDTO);
        return Result.success();
    }

    /**
     * 车辆验收-验收通过
     *
     * @param approveDTO 验收通过信息DTO
     * @return 操作结果
     */
    @ApiOperation(value = "车辆验收-验收通过", notes = "处理车辆维修任务的验收通过流程")
    @PostMapping("/vehicleInspection/approve")
    public Result<Void> approveVehicleInspection(@ApiParam(value = "验收通过信息", required = true) @Valid @RequestBody VehicleInspectionApproveDTO approveDTO) {
        vehicleInspectionService.approveInspection(approveDTO);
        return Result.success();
    }

    /**
     * 车辆验收-验收不通过
     *
     * @param rejectDTO 验收不通过信息DTO
     * @return 操作结果
     */
    @ApiOperation(value = "车辆验收-验收不通过", notes = "处理车辆维修任务的验收不通过流程")
    @PostMapping("/vehicleInspection/reject")
    public Result<Void> rejectVehicleInspection(@ApiParam(value = "验收不通过信息", required = true) @Valid @RequestBody VehicleInspectionRejectDTO rejectDTO) {
        vehicleInspectionService.rejectInspection(rejectDTO);
        return Result.success();
    }

    /**
     * 待分配-选择修理厂
     *
     * @param selectDTO 选择修理厂请求参数
     * @return 操作结果
     */
    @ApiOperation(value = "待分配-选择修理厂", notes = "为维修任务指定修理厂")
    @PostMapping("/pendingAssignment/selectRepairDepot")
    public Result<Void> selectRepairDepot(
            @ApiParam(value = "选择修理厂请求参数", required = true) 
            @Valid @RequestBody RepairDepotSelectDTO selectDTO) {
        repairTaskService.selectRepairDepot(selectDTO.getTaskId(), selectDTO.getRepairDepotId());
        return Result.success();
    }

    /**
     * 核损核价-平级移交
     *
     * @param taskNoDTO 包含维修任务编号的DTO
     * @return 操作结果
     */
    @ApiOperation(value = "核损核价-平级移交", notes = "将当前核损核价任务移交给其他人处理")
    @PostMapping("/lossAssessment/transfer")
    public Result<Void> transferLossAssessment(
            @ApiParam(value = "包含维修任务编号的DTO", required = true) 
            @Valid @RequestBody TaskNoDTO taskNoDTO) {
        lossAssessmentService.updateTransferTask(taskNoDTO.getTaskNo());
        return Result.success();
    }

    /**
     * 核损核价-清除任务占据人
     *
     * @param taskNoDTO 包含维修任务编号的DTO
     * @return 操作结果
     */
    @ApiOperation(value = "核损核价-清除任务占据人", notes = "清除核损核价任务的占据人，使任务可以被其他人处理")
    @PostMapping("/lossAssessment/clearOwner")
    public Result<Void> clearLossAssessmentOwner(
            @ApiParam(value = "包含维修任务编号的DTO", required = true) 
            @Valid @RequestBody TaskNoDTO taskNoDTO) {
        lossAssessmentService.clearOwner(taskNoDTO.getTaskNo());
        return Result.success();
    }

    /**
     * 维修报价-保存信息
     *
     * @param updateDTO 维修报价信息参数
     * @return 保存结果
     */
    @ApiOperation(value = "维修报价-保存信息", notes = "保存维修报价信息，不提交审核")
    @PostMapping("/repairQuotation/save")
    public Result<Void> saveRepairQuote(@ApiParam(value = "维修报价信息", required = true) @Valid @RequestBody RepairQuoteUpdateDTO updateDTO) {
        repairQuoteService.saveRepairQuote(updateDTO);
        return Result.success();
    }

    /**
     * 添加维修备注
     *
     * @param repairRemarkDTO 维修备注信息
     * @return 操作结果
     */
    @ApiOperation(value = "添加维修备注", notes = "为维修任务添加备注信息")
    @PostMapping("/remark/add")
    public Result<Void> addRepairRemark(
            @ApiParam(value = "维修备注信息", required = true) 
            @Valid @RequestBody RepairRemarkDTO repairRemarkDTO) {
        try {
            repairTaskService.addRepairRemark(repairRemarkDTO);
            return Result.success();
        } catch (Exception e) {
            return Result.error("系统异常，请稍后重试");
        }
    }

    /**
     * 删除维修备注
     *
     * @param id 备注ID
     * @param repairStage 维修阶段
     * @return 操作结果
     */
    @ApiOperation(value = "删除维修备注", notes = "删除指定ID的维修备注")
    @PostMapping("/remark/delete")
    public Result<Void> deleteRepairRemark(
            @ApiParam(value = "备注ID", required = true) @RequestParam Long id,
            @ApiParam(value = "维修阶段", required = true) @RequestParam String repairStage) {
        try {
            repairTaskService.deleteRepairRemark(id, repairStage);
            return Result.success();
        } catch (Exception e) {
            return Result.error("系统异常，请稍后重试");
        }
    }
}
