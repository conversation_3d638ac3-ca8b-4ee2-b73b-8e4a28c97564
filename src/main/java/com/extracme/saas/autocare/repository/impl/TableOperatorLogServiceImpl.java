package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcOperatorLogDynamicSqlSupport.mtcOperatorLog;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.extend.MtcOperatorLogExtendMapper;
import com.extracme.saas.autocare.model.dto.OperatorLogDTO;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;
import com.extracme.saas.autocare.repository.TableOperatorLogService;

/**
 * 操作日志数据访问服务实现类
 */
@Repository
public class TableOperatorLogServiceImpl implements TableOperatorLogService {

    @Autowired
    private MtcOperatorLogExtendMapper mtcOperatorLogMapper;

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public List<OperatorLogDTO> queryOperatorLog(Long recordId, String tableName) {
        if (recordId == null || tableName == null || tableName.isEmpty()) {
            return Collections.emptyList();
        }

        // 构建查询条件
        SelectStatementProvider selectStatement = select(mtcOperatorLog.allColumns())
            .from(mtcOperatorLog)
            .where(mtcOperatorLog.recordId, isEqualTo(recordId))
            .and(mtcOperatorLog.tableName, isEqualTo(tableName))
            .orderBy(mtcOperatorLog.createdTime.descending())
            .build()
            .render(RenderingStrategies.MYBATIS3);

        // 执行查询
        List<MtcOperatorLog> logs = mtcOperatorLogMapper.selectMany(selectStatement);

        // 转换为DTO
        List<OperatorLogDTO> dtoList = new ArrayList<>();
        for (MtcOperatorLog log : logs) {
            OperatorLogDTO dto = new OperatorLogDTO();
            dto.setOperationTime(log.getCreatedTime() != null ? DATE_FORMAT.format(log.getCreatedTime()) : "");
            dto.setOperationUserName(log.getCreateBy());
            dto.setOperationContext(log.getOpeContent());
            // 根据环节编码设置环节名称
            dto.setOperationType(getTacheName(log.getCurrentActivityCode()));
            dtoList.add(dto);
        }

        return dtoList;
    }

    @Override
    public int insertSelective(MtcOperatorLog log) {
        if (log == null) {
            return 0;
        }
        return mtcOperatorLogMapper.insertSelective(log);
    }

    @Override
    public List<MtcOperatorLog> selectByRecordId(Long recordId) {
        if (recordId == null) {
            return Collections.emptyList();
        }

        // 构建查询条件
        SelectStatementProvider selectStatement = select(mtcOperatorLog.allColumns())
            .from(mtcOperatorLog)
            .where(mtcOperatorLog.recordId, isEqualTo(recordId))
            .orderBy(mtcOperatorLog.createdTime.descending())
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return mtcOperatorLogMapper.selectMany(selectStatement);
    }

    @Override
    public List<MtcOperatorLog> selectByRecordIdAndTache(Long recordId, String currentActivityCode) {
        if (recordId == null || currentActivityCode == null) {
            return Collections.emptyList();
        }

        // 构建查询条件
        SelectStatementProvider selectStatement = select(mtcOperatorLog.allColumns())
            .from(mtcOperatorLog)
            .where(mtcOperatorLog.recordId, isEqualTo(recordId))
            .and(mtcOperatorLog.currentActivityCode, isEqualTo(currentActivityCode))
            .orderBy(mtcOperatorLog.createdTime.descending())
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return mtcOperatorLogMapper.selectMany(selectStatement);
    }

    @Override
    public int updateByPrimaryKeySelective(MtcOperatorLog log) {
        if (log == null || log.getId() == null) {
            return 0;
        }
        return mtcOperatorLogMapper.updateByPrimaryKeySelective(log);
    }

    /**
     * 根据环节编码获取环节名称
     *
     * @param tacheCode 环节编码
     * @return 环节名称
     */
    private String getTacheName(String tacheCode) {
        if (tacheCode == null) {
            return "";
        }

        // 尝试将字符串转换为数字进行匹配
        try {
            int code = Integer.parseInt(tacheCode);
            switch (code) {
                case 10: return "车辆交接";
                case 20: return "定损报价";
                case 30: return "核损核价";
                case 40: return "改派中";
                case 50: return "车辆维修";
                case 60: return "车辆验收";
                case 70: return "资料收集";
                case 80: return "损失登记";
                case 90: return "结算管理";
                default: break;
            }
        } catch (NumberFormatException e) {
            // 如果不是数字格式，则直接返回原值作为环节名称
        }

        // 如果是活动编码格式，则直接返回
        return tacheCode;
    }
}
