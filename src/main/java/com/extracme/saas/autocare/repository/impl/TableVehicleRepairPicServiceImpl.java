package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcVehicleRepairPicDynamicSqlSupport.id;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isInWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.base.MtcVehicleRepairPicDynamicSqlSupport;
import com.extracme.saas.autocare.mapper.extend.MtcVehicleRepairPicExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcVehicleRepairPic;
import com.extracme.saas.autocare.repository.TableVehicleRepairPicService;

/**
 * 维修任务图片/视频表数据访问服务实现
 */
@Repository
public class TableVehicleRepairPicServiceImpl implements TableVehicleRepairPicService {

    @Autowired
    private MtcVehicleRepairPicExtendMapper mtcVehicleRepairPicMapper;

    @Override
    public MtcVehicleRepairPic insert(MtcVehicleRepairPic record) {
        mtcVehicleRepairPicMapper.insertSelective(record);
        return record;
    }

    @Override
    public List<MtcVehicleRepairPic> selectByTaskNo(String taskNo) {
        SelectDSLCompleter completer = c -> c.where(MtcVehicleRepairPicDynamicSqlSupport.taskNo, isEqualTo(taskNo))
                .orderBy(id.descending());
        return mtcVehicleRepairPicMapper.select(completer);
    }

    @Override
    public List<MtcVehicleRepairPic> selectByTaskNoAndType(String taskNo, Short picType) {
        SelectDSLCompleter completer = c -> c.where(MtcVehicleRepairPicDynamicSqlSupport.taskNo, isEqualTo(taskNo))
                .and(MtcVehicleRepairPicDynamicSqlSupport.picType, isEqualTo(picType))
                .orderBy(id.descending());
        return mtcVehicleRepairPicMapper.select(completer);
    }

    @Override
    public int batchInsert(List<MtcVehicleRepairPic> picList) {
        int count = 0;
        for (MtcVehicleRepairPic pic : picList) {
            count += mtcVehicleRepairPicMapper.insertSelective(pic);
        }
        return count;
    }

    @Override
    public int deleteByTaskNo(String taskNo) {
        return mtcVehicleRepairPicMapper
                .delete(c -> c.where(MtcVehicleRepairPicDynamicSqlSupport.taskNo, isEqualTo(taskNo)));
    }

    @Override
    public void deleteByTaskIdAndPicTypes(String taskNo, List<Short> picTypes) {
        if (CollectionUtils.isEmpty(picTypes)) {
            return;
        }

        mtcVehicleRepairPicMapper.delete(c -> c.where()
                .and(MtcVehicleRepairPicDynamicSqlSupport.taskNo, isEqualTo(taskNo))
                .and(MtcVehicleRepairPicDynamicSqlSupport.picType, isInWhenPresent(picTypes)));
    }

    @Override
    public int delMaterialPic(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        return mtcVehicleRepairPicMapper.delete(c -> c.where(MtcVehicleRepairPicDynamicSqlSupport.id, isIn(ids)));
    }
}
