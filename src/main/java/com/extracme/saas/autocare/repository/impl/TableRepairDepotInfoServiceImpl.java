package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcRepairDepotInfoDynamicSqlSupport.mtcRepairDepotInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isLikeWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.extend.MtcRepairDepotInfoExtendMapper;
import com.extracme.saas.autocare.model.dto.RepairDepotQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairDepotInfo;
import com.extracme.saas.autocare.model.entity.SysTenant;
import com.extracme.saas.autocare.repository.TableRepairDepotInfoService;
import com.extracme.saas.autocare.repository.TableTenantService;
import com.extracme.saas.autocare.util.SessionUtils;
import com.extracme.saas.autocare.interceptor.tenant.TenantContextHolder;

import lombok.extern.slf4j.Slf4j;

/**
 * 修理厂信息Repository实现类
 */
@Slf4j
@Repository
public class TableRepairDepotInfoServiceImpl implements TableRepairDepotInfoService {

    @Autowired
    private MtcRepairDepotInfoExtendMapper mtcRepairDepotInfoExtendMapper;

    @Autowired
    private TableTenantService tableTenantService;


    @Override
    public List<MtcRepairDepotInfo> queryRepairDepotList(RepairDepotQueryDTO queryDTO) {
        SelectStatementProvider selectStatement = select(mtcRepairDepotInfo.allColumns())
                .from(mtcRepairDepotInfo)
                .where()
                .and(mtcRepairDepotInfo.repairDepotId, isEqualToWhenPresent(queryDTO.getRepairDepotId()))
                .and(mtcRepairDepotInfo.repairDepotOrgId, isEqualToWhenPresent(queryDTO.getMainOrgId()))
                .and(mtcRepairDepotInfo.repairDepotName, isLikeWhenPresent(transFuzzyQueryParam(queryDTO.getRepairDepotName())))
                .and(mtcRepairDepotInfo.status, isEqualToWhenPresent(queryDTO.getStatus()))
                .and(mtcRepairDepotInfo.repairDepotType, isEqualToWhenPresent(queryDTO.getRepairDepotType()))
                .and(mtcRepairDepotInfo.repairDepotOrgId, isEqualToWhenPresent(queryDTO.getMainOrgId()))
                .orderBy(mtcRepairDepotInfo.updatedTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mtcRepairDepotInfoExtendMapper.selectMany(selectStatement);
    }

    @Override
    public MtcRepairDepotInfo selectByRepairDepotName(String repairDepotName) {
        SelectStatementProvider selectStatement = select(mtcRepairDepotInfo.allColumns())
                .from(mtcRepairDepotInfo)
                .where()
                .and(mtcRepairDepotInfo.repairDepotName, isEqualTo(repairDepotName))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<MtcRepairDepotInfo> optional = mtcRepairDepotInfoExtendMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public MtcRepairDepotInfo selectByRepairDepotCode(String repairDepotCode) {
        SelectStatementProvider selectStatement = select(mtcRepairDepotInfo.allColumns())
                .from(mtcRepairDepotInfo)
                .where()
                .and(mtcRepairDepotInfo.repairDepotId, isEqualTo(repairDepotCode))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<MtcRepairDepotInfo> optional = mtcRepairDepotInfoExtendMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public MtcRepairDepotInfo selectById(Long id) {
        if (id == null) {
            return null;
        }
        Optional<MtcRepairDepotInfo> optional = mtcRepairDepotInfoExtendMapper.selectByPrimaryKey(id);
        return optional.orElse(null);
    }

    @Override
    public List<MtcRepairDepotInfo> findValidDepots() {
        // 构建查询条件
        SelectStatementProvider selectStatement = select(mtcRepairDepotInfo.repairDepotId,
                                                       mtcRepairDepotInfo.repairDepotName)
            .from(mtcRepairDepotInfo)
            .where(mtcRepairDepotInfo.status, isEqualTo(1))  // 状态为有效
            .and(mtcRepairDepotInfo.delFlag, isEqualTo(0))   // 未删除
            .orderBy(mtcRepairDepotInfo.repairDepotName)
            .build()
            .render(RenderingStrategies.MYBATIS3);

        // 执行查询并处理空结果情况
        return Optional.ofNullable(mtcRepairDepotInfoExtendMapper.selectMany(selectStatement))
            .orElse(Collections.emptyList());
    }

    @Override
    public List<MtcRepairDepotInfo> findAllValidDepotsForSuperAdmin() {
        // 保存当前租户上下文
        Long currentTenantId = TenantContextHolder.getTenantId();

        log.info("超级管理员查询所有租户修理厂信息开始，当前租户ID: {}", currentTenantId);

        List<MtcRepairDepotInfo> allRepairDepots = new ArrayList<>();

        try {
            // 1. 查询所有有效的租户列表
            List<SysTenant> allTenants = tableTenantService.findByCondition(null, null, null);

            if (allTenants.isEmpty()) {
                log.warn("未找到任何有效的租户信息");
                return Collections.emptyList();
            }

            log.info("找到 {} 个有效租户，开始分租户查询修理厂信息", allTenants.size());

            // 2. 遍历每个租户，分别查询修理厂信息
            for (SysTenant tenant : allTenants) {
                Long tenantId = tenant.getId();
                String tenantName = tenant.getTenantName();

                try {
                    // 临时设置当前租户上下文
                    TenantContextHolder.setTenant(tenantId);

                    log.debug("查询租户修理厂信息，租户ID: {}, 租户名称: {}", tenantId, tenantName);

                    // 查询该租户下的有效修理厂
                    List<MtcRepairDepotInfo> tenantRepairDepots = findValidDepots();

                    if (!tenantRepairDepots.isEmpty()) {
                        // 为每个修理厂添加租户标识信息
                        for (MtcRepairDepotInfo depot : tenantRepairDepots) {
                            // 在备注字段中记录租户信息，便于后续处理
                            String originalRemark = depot.getRemark();
                            String tenantInfo = "TENANT_ID:" + tenantId + ";TENANT_NAME:" + tenantName;
                            depot.setRemark(originalRemark != null ? originalRemark + ";" + tenantInfo : tenantInfo);
                        }

                        allRepairDepots.addAll(tenantRepairDepots);
                        log.debug("租户 {} 查询到 {} 个修理厂", tenantName, tenantRepairDepots.size());
                    } else {
                        log.debug("租户 {} 未找到修理厂信息", tenantName);
                    }

                } catch (Exception e) {
                    log.error("查询租户修理厂信息失败，租户ID: {}, 租户名称: {}", tenantId, tenantName, e);
                    // 继续处理下一个租户，不因单个租户查询失败而中断整个流程
                }
            }

            log.info("超级管理员查询所有租户修理厂信息完成，共找到 {} 个修理厂", allRepairDepots.size());
            return allRepairDepots;

        } catch (Exception e) {
            log.error("超级管理员查询所有租户修理厂信息失败", e);
            return Collections.emptyList();

        } finally {
            // 恢复原有的租户上下文
            if (currentTenantId != null) {
                TenantContextHolder.setTenant(currentTenantId);
                log.debug("恢复原始租户上下文，租户ID: {}", currentTenantId);
            } else {
                TenantContextHolder.clear();
                log.debug("清除租户上下文");
            }
        }
    }

    @Override
    public int updateSelectiveById(MtcRepairDepotInfo mtcRepairDepotInfo) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(mtcRepairDepotInfo, operator);
    }

    @Override
    public int updateSelectiveById(MtcRepairDepotInfo mtcRepairDepotInfo, String operator) {
        Date now = new Date();
        mtcRepairDepotInfo.setUpdatedTime(now);
        mtcRepairDepotInfo.setUpdateBy(operator);
        return mtcRepairDepotInfoExtendMapper.updateByPrimaryKeySelective(mtcRepairDepotInfo);
    }

    @Override
    public MtcRepairDepotInfo insert(MtcRepairDepotInfo mtcRepairDepotInfo) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return insert(mtcRepairDepotInfo, operator);
    }

    @Override
    public MtcRepairDepotInfo insert(MtcRepairDepotInfo mtcRepairDepotInfo, String operator) {
        if (mtcRepairDepotInfo == null){
            return null;
        }
        Date now = new Date();
        mtcRepairDepotInfo.setCreatedTime(now);
        mtcRepairDepotInfo.setCreateBy(operator);
        mtcRepairDepotInfo.setUpdatedTime(now);
        mtcRepairDepotInfo.setUpdateBy(operator);
        mtcRepairDepotInfoExtendMapper.insertSelective(mtcRepairDepotInfo);
        return mtcRepairDepotInfo;
    }
}
