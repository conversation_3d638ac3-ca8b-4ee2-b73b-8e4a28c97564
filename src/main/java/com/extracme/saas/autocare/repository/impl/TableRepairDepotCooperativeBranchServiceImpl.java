package com.extracme.saas.autocare.repository.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.base.MtcRepairDepotCooperativeBranchDynamicSqlSupport;
import com.extracme.saas.autocare.mapper.extend.MtcRepairDepotCooperativeBranchExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcRepairDepotCooperativeBranch;
import com.extracme.saas.autocare.repository.TableRepairDepotCooperativeBranchService;

import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * 维修厂合作分公司数据访问服务实现类
 */
@Repository
public class TableRepairDepotCooperativeBranchServiceImpl implements TableRepairDepotCooperativeBranchService {

    @Autowired
    private MtcRepairDepotCooperativeBranchExtendMapper mapper;

    @Override
    public List<MtcRepairDepotCooperativeBranch> queryCooperativeBranchList(String repairDepotId) {
        if (repairDepotId != null){
            return mapper.select(dsl -> dsl.where(MtcRepairDepotCooperativeBranchDynamicSqlSupport.repairDepotId, isEqualTo(repairDepotId)));
        }
        return Collections.emptyList();
    }

    @Override
    public int batchInsert(List<MtcRepairDepotCooperativeBranch> cooperativeBranchList) {
        if (CollectionUtils.isEmpty(cooperativeBranchList)){
            return 0;
        }
        Date now = new Date();
        for (MtcRepairDepotCooperativeBranch cooperativeBranch : cooperativeBranchList) {
            cooperativeBranch.setCreatedTime(now);
            cooperativeBranch.setUpdatedTime(now);
            mapper.insertSelective(cooperativeBranch);
        }
        return 1;
    }

    @Override
    public int deleteByRepairDepotId(String repairDepotId) {
        return mapper.delete(dsl -> dsl.where(MtcRepairDepotCooperativeBranchDynamicSqlSupport.repairDepotId, isEqualTo(repairDepotId)));
    }
}
