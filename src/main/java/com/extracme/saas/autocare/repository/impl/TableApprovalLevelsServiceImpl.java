package com.extracme.saas.autocare.repository.impl;

import com.extracme.saas.autocare.mapper.extend.MtcApprovalLevelsExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcApprovalLevels;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TableApprovalLevelsService;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

import static com.extracme.saas.autocare.mapper.base.MtcApprovalLevelsDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * 审批层级表服务实现类
 */
@Service
public class TableApprovalLevelsServiceImpl implements TableApprovalLevelsService {

    @Autowired
    private MtcApprovalLevelsExtendMapper approvalLevelsExtendMapper;

    @Override
    public MtcApprovalLevels insert(MtcApprovalLevels entity) {
        return insert(entity, "system");
    }

    @Override
    public MtcApprovalLevels insert(MtcApprovalLevels entity, String operator) {
        if (entity == null) {
            return null;
        }
        java.util.Date now = new java.util.Date();
        entity.setCreateTime(now);
        entity.setCreateBy(operator);
        entity.setUpdateTime(now);
        entity.setUpdateBy(operator);
        approvalLevelsExtendMapper.insertSelective(entity);
        return entity;
    }

    @Override
    public int updateSelectiveById(MtcApprovalLevels entity) {
        return updateSelectiveById(entity, "system");
    }

    @Override
    public int updateSelectiveById(MtcApprovalLevels entity, String operator) {
        if (entity == null) {
            return 0;
        }
        java.util.Date now = new java.util.Date();
        entity.setUpdateTime(now);
        entity.setUpdateBy(operator);
        return approvalLevelsExtendMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public MtcApprovalLevels selectById(Long id) {
        if (id == null) {
            return null;
        }
        return approvalLevelsExtendMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public int deleteById(Long id) {
        if (id == null) {
            return 0;
        }
        return approvalLevelsExtendMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<MtcApprovalLevels> findByCondition(Integer approvalLevel, BigDecimal minSelfApprovalAmount, BigDecimal maxSelfApprovalAmount) {
        SelectStatementProvider selectStatement = SqlBuilder.select(mtcApprovalLevels.allColumns())
            .from(mtcApprovalLevels)
            .where(mtcApprovalLevels.approvalLevel, isEqualToWhenPresent(approvalLevel))
            .and(mtcApprovalLevels.selfApprovalAmount, isGreaterThanOrEqualToWhenPresent(minSelfApprovalAmount))
            .and(mtcApprovalLevels.selfApprovalAmount, isLessThanOrEqualToWhenPresent(maxSelfApprovalAmount))
            .orderBy(mtcApprovalLevels.id.descending())
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return approvalLevelsExtendMapper.selectMany(selectStatement);
    }

    @Override
    public List<MtcApprovalLevels> findAll() {
        SelectStatementProvider selectStatement = SqlBuilder.select(mtcApprovalLevels.allColumns())
            .from(mtcApprovalLevels)
            .orderBy(mtcApprovalLevels.id.descending())
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return approvalLevelsExtendMapper.selectMany(selectStatement);
    }

    @Override
    public List<ComboVO<Long>> findComboByTenant(Long tenantId) {
        // 查询所有审批层级
        List<MtcApprovalLevels> approvalLevelsList = approvalLevelsExtendMapper.select(c ->
            c.orderBy(mtcApprovalLevels.approvalLevel)
        );

        // 转换为下拉框选项
        return approvalLevelsList.stream()
            .map(level -> {
                String label = getApprovalLevelName(level.getApprovalLevel()) +
                              " (≤" + level.getSelfApprovalAmount() + "元)";
                ComboVO<Long> combo = new ComboVO<>();
                combo.setId(level.getId());
                combo.setValue(label);
                return combo;
            })
            .collect(Collectors.toList());
    }

    @Override
    public boolean existsByApprovalLevel(Integer approvalLevel) {
        if (approvalLevel == null) {
            return false;
        }
        
        long count = approvalLevelsExtendMapper.count(c ->
            c.where(mtcApprovalLevels.approvalLevel, isEqualTo(approvalLevel))
        );
        
        return count > 0;
    }

    @Override
    public boolean existsByApprovalLevelExcludeId(Integer approvalLevel, Long excludeId) {
        if (approvalLevel == null) {
            return false;
        }
        
        long count = approvalLevelsExtendMapper.count(c ->
            c.where(mtcApprovalLevels.approvalLevel, isEqualTo(approvalLevel))
            .and(mtcApprovalLevels.id, isNotEqualToWhenPresent(excludeId))
        );
        
        return count > 0;
    }

    /**
     * 获取审批层级名称
     *
     * @param approvalLevel 审批层级
     * @return 审批层级名称
     */
    private String getApprovalLevelName(Integer approvalLevel) {
        if (approvalLevel == null) {
            return "未知";
        }
        switch (approvalLevel) {
            case 0:
                return "无审批";
            case 1:
                return "一级审批";
            case 2:
                return "二级审批";
            case 3:
                return "三级审批";
            case 4:
                return "四级审批";
            default:
                return "未知";
        }
    }
}
