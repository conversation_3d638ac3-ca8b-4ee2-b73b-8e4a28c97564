package com.extracme.saas.autocare.repository;

import java.util.List;

import com.extracme.saas.autocare.model.entity.MtcVehicleModel;
import com.extracme.saas.autocare.model.vo.base.ComboVO;

/**
 * 车型表服务接口
 */
public interface TableVehicleModelService extends DefaultTableService<MtcVehicleModel, Long> {

    /**
     * 根据车型名称模糊查询车型列表
     *
     * @param vehicleModelName 车型名称
     * @param limit 最大返回条数
     * @return 车型列表
     */
    List<MtcVehicleModel> findByCondition(String vehicleModelName, int limit);

    /**
     * 获取车型下拉列表数据
     *
     * @param vehicleModelName 车型名称，用于模糊查询
     * @param limit 最大返回条数
     * @return 下拉列表数据，ID类型为Long
     */
    List<ComboVO<Long>> getVehicleModelCombo(String vehicleModelName, int limit);
}
