package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.dto.ReplacePartItemQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcReplaceItem;

import java.util.List;

/**
 * 换件项目信息Repository接口
 */
public interface TableReplacePartItemService extends DefaultTableService<MtcReplaceItem, Long> {

    /**
     * 根据条件查询列表
     * @param queryDTO 查询条件
     * @return 修理厂列表信息
     */
    List<MtcReplaceItem> queryReplaceItemList(ReplacePartItemQueryDTO queryDTO);


    /**
     * 根据零件名称查询
     * @param partItemName 零件名称
     * @return 修理厂信息
     */
    MtcReplaceItem selectByPartItemName(String partItemName);

    /**
     * 根据ID删除
     * @param id ID
     * @return 删除结果
     */
    int deleteById(Long id);
}