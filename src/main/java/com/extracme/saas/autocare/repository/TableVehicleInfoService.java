package com.extracme.saas.autocare.repository;

import java.util.List;

import com.extracme.saas.autocare.model.entity.MtcVehicleInfo;

/**
 * 维修车辆信息表服务接口
 */
public interface TableVehicleInfoService extends DefaultTableService<MtcVehicleInfo, Long> {

 
    /**
     * 根据车架号(vin)查询车辆详情
     *
     * @param vin 车架号
     * @return 车辆详情
     */
    MtcVehicleInfo findByVin(String vin);

    /**
     * 根据车架号或车牌号模糊查询车辆信息列表
     *
     * @param vin 车架号
     * @param vehicleNo 车牌号
     * @param limit 最大返回条数
     * @return 车辆信息列表
     */
    List<MtcVehicleInfo> findByCondition(String vin, String vehicleNo, int limit);
} 