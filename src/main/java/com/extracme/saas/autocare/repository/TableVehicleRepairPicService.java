package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.MtcVehicleRepairPic;

import java.util.List;

/**
 * 维修任务图片/视频表数据访问服务接口
 */
public interface TableVehicleRepairPicService extends DefaultTableService<MtcVehicleRepairPic, Long> {

    /**
     * 根据任务编号查询维修图片列表
     * @param taskNo 任务编号
     * @return 维修图片列表
     */
    List<MtcVehicleRepairPic> selectByTaskNo(String taskNo);

    /**
     * 根据任务编号和图片类型查询维修图片列表
     * @param taskNo 任务编号
     * @param picType 图片类型
     * @return 维修图片列表
     */
    List<MtcVehicleRepairPic> selectByTaskNoAndType(String taskNo, Short picType);

    /**
     * 批量插入维修图片
     * @param picList 维修图片列表
     * @return 影响行数
     */
    int batchInsert(List<MtcVehicleRepairPic> picList);

    /**
     * 根据任务编号删除维修图片
     * @param taskNo 任务编号
     * @return 影响行数
     */
    int deleteByTaskNo(String taskNo);

    /**
     * 根据任务id和图片类型批量删除
     * @param taskId 任务id
     * @param picTypes 图片类型列表
     */
    void deleteByTaskIdAndPicTypes(String taskNo, List<Short> picTypes);

    /**
     * 批量删除图片
     * @param ids 图片ID列表
     * @return 影响行数
     */
    int delMaterialPic(List<Long> ids);
} 