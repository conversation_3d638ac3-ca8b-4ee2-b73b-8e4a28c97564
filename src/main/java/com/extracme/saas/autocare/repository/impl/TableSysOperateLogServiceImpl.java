package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.SysOperateLogDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.mapper.extend.SysOperateLogExtendMapper;
import com.extracme.saas.autocare.model.entity.SysOperateLog;
import com.extracme.saas.autocare.repository.TableSysOperateLogService;
import com.extracme.saas.autocare.util.SessionUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 操作日志表数据访问服务实现类
 *
 * 主要功能：
 * 1. 提供操作日志的基础CRUD操作
 * 2. 提供操作日志的查询功能
 * 3. 确保数据访问的事务性和安全性
 * 4. 支持多租户权限控制
 */
@Slf4j
@Repository
public class TableSysOperateLogServiceImpl implements TableSysOperateLogService {

    @Autowired
    private SysOperateLogExtendMapper operateLogExtendMapper;

    /**
     * 根据ID查询操作日志信息
     *
     * @param id 操作日志ID
     * @return 操作日志信息
     */
    @Override
    public SysOperateLog selectById(Long id) {
        if (id == null) {
            return null;
        }
        return operateLogExtendMapper.selectByPrimaryKey(id).orElse(null);
    }

    /**
     * 插入操作日志
     *
     * @param operateLog 操作日志对象
     * @param operator 操作人名称
     * @return 插入后的操作日志对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysOperateLog insert(SysOperateLog operateLog, String operator) {
        log.info("开始插入操作日志：operateLog={}, operator={}, 线程={}",
                operateLog, operator, Thread.currentThread().getName());

        if (operateLog == null) {
            log.warn("操作日志对象为空，返回null");
            return null;
        }

        try {
            // 设置审计字段
            Date now = new Date();
            operateLog.setCreateTime(now);
            operateLog.setCreateBy(operator);
            operateLog.setUpdateTime(now);
            operateLog.setUpdateBy(operator);

            // 设置默认状态为有效
            if (operateLog.getStatus() == null) {
                operateLog.setStatus(1);
            }

            log.info("审计字段设置完成，准备执行数据库插入：{}", operateLog);
            log.info("使用的Mapper：{}", operateLogExtendMapper);

            // 执行插入
            int result = operateLogExtendMapper.insertSelective(operateLog);

            log.info("数据库插入执行完成，影响行数：{}, 生成的ID：{}", result, operateLog.getId());

            if (result > 0) {
                log.info("操作日志插入成功：ID={}, 内容={}", operateLog.getId(), operateLog.getContent());
            } else {
                log.warn("操作日志插入失败，影响行数为0");
            }

            return operateLog;

        } catch (Exception e) {
            log.error("插入操作日志时发生异常：operateLog={}, operator={}, 异常类型={}, 异常信息={}",
                     operateLog, operator, e.getClass().getName(), e.getMessage(), e);
            throw e; // 重新抛出异常，让事务回滚
        }
    }

    /**
     * 根据主键更新操作日志
     *
     * @param operateLog 更新数据
     * @param operator 操作人
     * @return 影响行数
     */
    @Override
    public int updateSelectiveById(SysOperateLog operateLog, String operator) {
        if (operateLog == null || operateLog.getId() == null) {
            return 0;
        }

        // 设置审计字段
        Date now = new Date();
        operateLog.setUpdateTime(now);
        operateLog.setUpdateBy(operator);

        return operateLogExtendMapper.updateByPrimaryKeySelective(operateLog);
    }

    /**
     * 根据查询条件查找操作日志（支持多租户权限控制）
     * 使用MyBatis Dynamic SQL构建查询条件
     *
     * 多租户访问控制逻辑：
     * - 超级管理员：可以查询所有租户的操作日志（忽略tenantId参数）
     * - 普通用户：只能查询自己租户的操作日志
     *
     * @param tenantId 租户ID（可选，用于多租户数据隔离）
     * @return 操作日志列表
     */
    @Override
    public List<SysOperateLog> findByCondition(Long tenantId) {
        // 判断用户类型
        boolean isSuperAdmin = SessionUtils.isSuperAdmin();

        // 构建查询条件
        SelectStatementProvider selectStatement;

        if (isSuperAdmin) {
            // 超级管理员可以查询所有租户的操作日志
            selectStatement = select(sysOperateLog.allColumns())
                .from(sysOperateLog)
                .where(sysOperateLog.status, isEqualTo(1))  // 只查询有效的日志
                .orderBy(sysOperateLog.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        } else {
            // 普通用户只能查询自己租户的操作日志
            selectStatement = select(sysOperateLog.allColumns())
                .from(sysOperateLog)
                .where(sysOperateLog.status, isEqualTo(1))  // 只查询有效的日志
                .and(sysOperateLog.tenantId, isEqualToWhenPresent(tenantId))
                .orderBy(sysOperateLog.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        }

        return Optional.ofNullable(operateLogExtendMapper.selectMany(selectStatement))
            .orElse(Collections.emptyList());
    }
}
