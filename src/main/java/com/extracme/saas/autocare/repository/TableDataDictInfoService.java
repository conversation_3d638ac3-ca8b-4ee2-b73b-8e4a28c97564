package com.extracme.saas.autocare.repository;

import java.util.List;
import com.extracme.saas.autocare.model.entity.DataDictInfo;

/**
 * 数据字典表数据访问服务接口
 */
public interface TableDataDictInfoService extends DefaultTableService<DataDictInfo, Long> {

    /**
     * 查询所有数据字典记录
     *
     * @return 数据字典列表
     */
    List<DataDictInfo> selectAll();

    /**
     * 根据字典编码查询数据字典
     * @param dataCode 字典编码
     * @return 数据字典信息，若不存在返回null
     */
    DataDictInfo selectByDataCode(String dataCode);

    /**
     * 根据ID删除数据字典
     * @param id 数据字典ID
     * @return 删除的记录数
     */
    int deleteById(Long id);
}