package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.SysRoleDynamicSqlSupport.sysRole;
import static com.extracme.saas.autocare.mapper.base.SysUserRoleDynamicSqlSupport.sysUserRole;
import static org.mybatis.dynamic.sql.SqlBuilder.equalTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isLikeWhenPresent;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.mapper.extend.SysRoleExtendMapper;
import com.extracme.saas.autocare.model.entity.SysRole;
import com.extracme.saas.autocare.repository.TableRoleService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 角色表数据访问服务实现类
 *
 * 主要职责：
 * 1. 提供角色的增删改查基础操作
 * 2. 提供角色的高级查询功能
 * 3. 确保数据访问的事务性和安全性
 */
@Repository
public class TableRoleServiceImpl implements TableRoleService {

    @Autowired
    private SysRoleExtendMapper roleExtendMapper;

    /**
     * 根据角色编码查询角色信息
     * 只返回未删除的角色
     *
     * @param roleCode 角色编码
     * @return 角色信息
     */
    @Override
    public Optional<SysRole> findByRoleCode(String roleCode) {
        return roleExtendMapper.selectOne(c -> c.where(sysRole.roleCode, isEqualTo(roleCode)));
    }





    /**
     * 查询所有未删除的角色
     *
     * 多租户访问控制逻辑：
     * - 超级管理员：可以查询所有角色
     * - 普通用户：只能查询系统级角色（tenantId=-1）和自己租户的角色
     *
     * 实现方式：调用统一的 findByCondition 方法，避免代码重复
     *
     * @return 角色列表
     */
    @Override
    public List<SysRole> findAll() {
        // 调用统一的查询方法，不传入角色名称和租户ID参数
        return findByCondition(null, null);
    }

    /**
     * 根据查询条件查找角色（支持角色名称模糊查询和租户过滤）
     * 使用MyBatis Dynamic SQL的isLikeWhenPresent和isIn方法
     * 只有当参数不为空时才添加相应的查询条件
     *
     * 多租户访问控制逻辑：
     * - 超级管理员：可以查询所有角色（忽略tenantId参数）
     * - 普通用户：只能查询系统级角色（tenantId=-1）和自己租户的角色
     *
     * 实现方式：使用IN语句查询允许的租户ID列表，提高查询效率
     *
     * @param roleName 角色名称（可选，支持模糊查询）
     * @param tenantId 租户ID（可选，用于多租户数据隔离）
     * @return 角色列表
     */
    @Override
    public List<SysRole> findByCondition(String roleName, Long tenantId) {
        // 导入SessionUtils以判断用户类型
        boolean isSuperAdmin = SessionUtils.isSuperAdmin();

        if (isSuperAdmin) {
            // 超级管理员：可以查询所有角色
            return roleExtendMapper.select(c ->
                c.where(sysRole.roleName, isLikeWhenPresent(transFuzzyQueryParam(roleName)))
            );
        } else {
            // 普通用户：只能查询系统级角色（tenantId=-1）和自己租户的角色
            Long currentTenantId = SessionUtils.getTenantId();

            // 构建租户ID列表，包含系统级角色(-1L)和当前用户租户ID
            List<Long> allowedTenantIds = new ArrayList<>();
            allowedTenantIds.add(-1L); // 系统级角色
            if (currentTenantId != null) {
                allowedTenantIds.add(currentTenantId); // 当前用户租户的角色
            }

            return roleExtendMapper.select(c ->
                c.where(sysRole.roleName, isLikeWhenPresent(transFuzzyQueryParam(roleName)))
                 .and(sysRole.tenantId, isIn(allowedTenantIds))
            );
        }
    }

    /**
     * 批量创建角色
     * 通过循环单条插入的方式实现
     *
     * @param roles 角色列表
     * @return 成功创建的角色数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(List<SysRole> roles) {
        if (roles == null || roles.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        Date now = new Date();
        for (SysRole role : roles) {
            try {
                role.setCreatedTime(now);
                role.setUpdatedTime(now);
                roleExtendMapper.insertSelective(role);
                successCount++;
            } catch (Exception e) {
                // 记录失败但继续处理其他记录
                continue;
            }
        }
        return successCount;
    }

    /**
     * 检查角色编码是否已存在
     *
     * @param code 角色编码
     * @return true:已存在 false:不存在
     */
    @Override
    public boolean existsByCode(String code) {
        return roleExtendMapper.count(c -> c.where(sysRole.roleCode, isEqualTo(code))) > 0;
    }

    /**
     * 检查角色是否被用户使用
     *
     * @param roleId 角色ID
     * @return true:正在使用 false:未使用
     */
    @Override
    public boolean isUsedByUsers(Long roleId) {
        return roleExtendMapper.count(c -> c.where(sysRole.id, isEqualTo(roleId))) > 0;
    }

    /**
     * 根据用户ID查询角色列表
     * 使用MyBatis Dynamic SQL构建查询
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @Override
    public List<SysRole> findByUserId(Long userId) {
        // 参数校验
        if (userId == null) {
            return Collections.emptyList();
        }

        // 使用 MyBatis Dynamic SQL 构建查询
        // 这种方式会自动使用 SysRoleMapper 中定义的 ResultMap

        // 创建查询条件并执行查询
        SelectStatementProvider selectStatement = SqlBuilder.select(sysRole.allColumns())
            .from(sysRole)
            .join(sysUserRole)
            .on(sysRole.id, equalTo(sysUserRole.roleId))
            .where(sysUserRole.userId, isEqualTo(userId))
            .build()
            .render(RenderingStrategies.MYBATIS3);

        // 执行查询
        return roleExtendMapper.selectMany(selectStatement);
    }

    /**
     * 检查用户是否有指定角色
     * 使用MyBatis Dynamic SQL构建查询
     *
     * @param userId 用户ID
     * @param roleCode 角色编码
     * @return 是否有角色
     */
    @Override
    public boolean hasRole(Long userId, String roleCode) {
        // 参数校验
        if (userId == null || roleCode == null || roleCode.isEmpty()) {
            return false;
        }

        // 使用 MyBatis Dynamic SQL 构建查询

        // 创建查询条件
        SelectStatementProvider selectStatement = SqlBuilder.select(sysRole.allColumns())
            .from(sysRole)
            .join(sysUserRole)
            .on(sysRole.id, equalTo(sysUserRole.roleId))
            .where(sysUserRole.userId, isEqualTo(userId))
            .and(sysRole.roleCode, isEqualTo(roleCode))
            .build()
            .render(RenderingStrategies.MYBATIS3);

        // 执行查询并检查结果是否为空
        List<SysRole> roles = roleExtendMapper.selectMany(selectStatement);
        return !roles.isEmpty();
    }

    @Override
    public SysRole insert(SysRole role) {
        // 使用系统操作人
        return insert(role, "system");
    }

    @Override
    public SysRole insert(SysRole role, String operator) {
        Date now = new Date();
        role.setCreatedTime(now);
        role.setUpdatedTime(now);
        role.setCreateBy(operator);
        role.setUpdateBy(operator);
        roleExtendMapper.insertSelective(role);
        return role;
    }

    @Override
    public int updateSelectiveById(SysRole role) {
        // 使用系统操作人
        return updateSelectiveById(role, "system");
    }

    @Override
    public int updateSelectiveById(SysRole role, String operator) {
        Date now = new Date();
        role.setUpdatedTime(now);
        role.setUpdateBy(operator);
        return roleExtendMapper.updateByPrimaryKeySelective(role);
    }

    @Override
    public SysRole selectById(Long id) {
        return roleExtendMapper.selectByPrimaryKey(id).orElse(null);
    }
}