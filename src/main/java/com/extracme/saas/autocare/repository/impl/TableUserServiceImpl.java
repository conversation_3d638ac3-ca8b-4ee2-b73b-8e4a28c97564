package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.SysUserDynamicSqlSupport.sysUser;
import static com.extracme.saas.autocare.mapper.base.SysUserRoleDynamicSqlSupport.sysUserRole;
import static org.mybatis.dynamic.sql.SqlBuilder.*;


import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.extracme.saas.autocare.mapper.extend.SysUserExtendMapper;
import com.extracme.saas.autocare.mapper.extend.SysUserRoleExtendMapper;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.entity.SysUserRole;
import com.extracme.saas.autocare.repository.TableUserService;

/**
 * 用户表数据访问服务实现类
 *
 * 主要功能：
 * 1. 提供用户的基础CRUD操作
 * 2. 提供用户信息的查询功能
 * 3. 确保数据访问的事务性和安全性
 * 4. 处理用户名和手机号的唯一性校验
 */
@Repository
public class TableUserServiceImpl implements TableUserService {

    /**
     * 用户扩展Mapper，提供对用户表的操作能力
     */
    @Autowired
    private SysUserExtendMapper userExtendMapper;

    /**
     * 用户角色关联表扩展Mapper，提供对用户角色关联表的操作能力
     */
    @Autowired
    private SysUserRoleExtendMapper userRoleMapper;


    /**
     * 根据ID查询用户信息
     *
     * @param id 用户ID
     * @return 用户信息
     */
    @Override
    public SysUser selectById(Long id) {
        // 参数校验，如果ID为空则直接返回null
        if (id == null) {
            return null;
        }

        // 调用Mapper查询用户信息
        return userExtendMapper.selectByPrimaryKey(id).orElse(null);
    }

    /**
     * 更新用户信息
     *
     * @param user 用户信息
     * @return 更新的记录数
     */
    @Override
    public int updateSelectiveById(SysUser user) {
        if (user == null || user.getId() == null) {
            return 0;
        }

        // 使用系统操作人
        return updateSelectiveById(user, "system");
    }

    @Override
    public int updateSelectiveById(SysUser user, String operator) {
        Date now = new Date();
        user.setUpdatedTime(now);
        user.setUpdateBy(operator);
        return userExtendMapper.updateByPrimaryKeySelective(user);
    }

    /**
     * 插入新用户
     *
     * @param user 用户信息
     * @return 插入的用户对象（包含生成的ID）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysUser insert(SysUser user) {
        // 参数校验，如果用户对象为空则直接返回null
        if (user == null) {
            return null;
        }

        // 使用系统操作人
        return insert(user, "system");
    }

    /**
     * 插入新用户，并设置审计字段
     *
     * @param user 用户信息
     * @param operator 操作人
     * @return 插入的用户对象（包含生成的ID）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysUser insert(SysUser user, String operator) {
        // 参数校验，如果用户对象为空则直接返回null
        if (user == null) {
            return null;
        }

        // 设置创建时间、更新时间、创建人和更新人
        Date now = new Date();
        user.setCreatedTime(now);
        user.setUpdatedTime(now);
        user.setCreateBy(operator);
        user.setUpdateBy(operator);

        // 执行插入操作，只插入非空字段
        userExtendMapper.insertSelective(user);

        // 返回包含ID的用户对象
        return user;
    }

    /**
     * 根据用户名查询用户信息
     * 只返回有效的用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    @Override
    public Optional<SysUser> findByUsername(String username) {
        // 参数校验，如果用户名为空则直接返回空结果
        if (!StringUtils.hasText(username)) {
            return Optional.empty();
        }

        // 构建查询条件并执行查询
        return userExtendMapper.selectOne(c -> c.where(sysUser.username, isEqualTo(username)));
    }

    /**
     * 根据手机号查询用户信息
     * 只返回有效的用户
     *
     * @param mobile 手机号
     * @return 用户信息
     */
    @Override
    public Optional<SysUser> findByMobile(String mobile) {
        // 参数校验，如果手机号为空则直接返回空结果
        if (!StringUtils.hasText(mobile)) {
            return Optional.empty();
        }

        // 构建查询条件并执行查询
        return userExtendMapper.selectOne(c -> c.where(sysUser.mobile, isEqualTo(mobile)));
    }

    /**
     * 检查用户名是否已存在
     *
     * @param username 用户名
     * @return true:已存在 false:不存在
     */
    @Override
    public boolean existsByUsername(String username) {
        // 参数校验，如果用户名为空则直接返回不存在
        if (!StringUtils.hasText(username)) {
            return false;
        }

        // 调用计数方法判断是否存在
        return countByUsername(username) > 0;
    }

    /**
     * 检查手机号是否已存在
     *
     * @param mobile 手机号
     * @return true:已存在 false:不存在
     */
    @Override
    public boolean existsByMobile(String mobile) {
        // 参数校验，如果手机号为空则直接返回不存在
        if (!StringUtils.hasText(mobile)) {
            return false;
        }

        // 调用计数方法判断是否存在
        return countByMobile(mobile) > 0;
    }

    /**
     * 批量创建用户
     * 通过循环单条插入的方式实现
     * 自动设置创建时间和更新时间
     *
     * @param users 用户列表
     * @return 成功创建的用户数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(List<SysUser> users) {
        // 参数校验，如果用户列表为空则直接返回0
        if (users == null || users.isEmpty()) {
            return 0;
        }

        // 记录成功插入的用户数量
        int successCount = 0;

        // 获取当前时间，用于设置创建时间和更新时间
        Date now = new Date();

        // 遍历用户列表，逐个插入
        for (SysUser user : users) {
            user.setCreatedTime(now);
            user.setUpdatedTime(now);
            userExtendMapper.insertSelective(user);
            successCount++;
        }

        // 返回成功插入的用户数量
        return successCount;
    }

    /**
     * 根据用户名统计用户数量
     * 统计条件：
     * 1. 用户名完全匹配
     * 2. 只统计有效的用户
     *
     * @param username 用户名（区分大小写）
     * @return 符合条件的用户数量
     */
    @Override
    public int countByUsername(String username) {
        // 参数校验，如果用户名为空则直接返回0
        if (!StringUtils.hasText(username)) {
            return 0;
        }

        // 构建SQL查询语句
        SelectStatementProvider selectStatement = SqlBuilder.select(SqlBuilder.count())
                .from(sysUser)
                .where(sysUser.username, isEqualTo(username))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        // 执行计数查询并返回结果
        return (int) userExtendMapper.count(selectStatement);
    }

    /**
     * 根据手机号统计用户数量
     * 统计条件：
     * 1. 手机号完全匹配
     * 2. 只统计有效的用户
     *
     * @param mobile 手机号（11位数字）
     * @return 符合条件的用户数量
     */
    @Override
    public int countByMobile(String mobile) {
        // 参数校验，如果手机号为空则直接返回0
        if (!StringUtils.hasText(mobile)) {
            return 0;
        }

        // 构建SQL查询语句
        SelectStatementProvider selectStatement = SqlBuilder.select(SqlBuilder.count())
                .from(sysUser)
                .where(sysUser.mobile, isEqualTo(mobile))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        // 执行计数查询并返回结果
        return (int) userExtendMapper.count(selectStatement);
    }

    /**
     * 根据条件查询用户列表
     * 支持按用户名、手机号、角色ID和租户ID进行筛选
     * 使用MyBatis Dynamic SQL构建查询条件，支持模糊查询
     * 多个查询条件之间使用AND逻辑连接
     *
     * @param nickname 用户昵称（可选，支持模糊查询）
     * @param mobile 手机号（可选，支持模糊查询）
     * @param roleId 角色ID（可选）
     * @param tenantId 租户ID（可选，用于多租户数据隔离）
     * @return 符合条件的用户列表
     */
    @Override
    public List<SysUser> findByCondition(String nickname, String mobile, Long roleId, Long tenantId) {
        // 如果没有指定角色ID，使用基础查询条件
        if (roleId == null) {
            // 构建基础查询条件，支持nickname、mobile和tenantId的查询
            SelectStatementProvider selectStatement = SqlBuilder.select(sysUser.allColumns())
                .from(sysUser)
                .where(sysUser.nickname, isLikeWhenPresent(transFuzzyQueryParam(nickname)))
                .and(sysUser.mobile, isLikeWhenPresent(transFuzzyQueryParam(mobile)))
                .and(sysUser.tenantId, isEqualToWhenPresent(tenantId))
                .orderBy(sysUser.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);

            return userExtendMapper.selectMany(selectStatement);
        }

        // 查询指定角色下的所有用户ID
        List<SysUserRole> userRoles = userRoleMapper.select(c ->
            c.where(sysUserRole.roleId, isEqualTo(roleId))
        );

        // 如果没有找到用户，直接返回空列表
        if (userRoles.isEmpty()) {
            return Collections.emptyList();
        }

        // 提取用户ID列表
        List<Long> userIds = userRoles.stream()
            .map(SysUserRole::getUserId)
            .collect(Collectors.toList());

        // 使用IN条件查询用户信息，同时支持nickname、mobile和tenantId的查询
        SelectStatementProvider selectStatement = SqlBuilder.select(sysUser.allColumns())
            .from(sysUser)
            .where(sysUser.id, isIn(userIds))
            .and(sysUser.nickname, isLikeWhenPresent(transFuzzyQueryParam(nickname)))
            .and(sysUser.mobile, isLikeWhenPresent(transFuzzyQueryParam(mobile)))
            .and(sysUser.tenantId, isEqualToWhenPresent(tenantId))
            .orderBy(sysUser.id.descending())
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return userExtendMapper.selectMany(selectStatement);
    }

    /**
     * 查询商户管理员列表
     * 支持按商户名称、管理员姓名、手机号和状态进行筛选
     * 商户管理员的账号类型为1
     *
     * @param merchantName 商户名称(可选)
     * @param adminName 管理员姓名(可选)
     * @param mobile 手机号(可选)
     * @param status 状态(可选)
     * @return 商户管理员列表
     */
    @Override
    public List<SysUser> findMerchantAdmins(String merchantName, String adminName, String mobile, Integer status) {
        // 构建基本查询条件
        SelectStatementProvider selectStatement = SqlBuilder.select(sysUser.allColumns())
        .from(sysUser)
        .where(sysUser.accountType, isEqualTo( 1))
        .and(sysUser.nickname, isLikeWhenPresent(transFuzzyQueryParam(adminName)))
        .and(sysUser.mobile, isLikeWhenPresent(transFuzzyQueryParam(mobile)))
        .and(sysUser.status, isEqualToWhenPresent(status))
        .build()
        .render(RenderingStrategies.MYBATIS3);

        return userExtendMapper.selectMany(selectStatement);
    }

    /**
     * 根据手机号查询用户关联的所有租户ID
     *
     * @param mobile 手机号
     * @return 租户ID列表
     */
    @Override
    public List<Long> findTenantIdsByMobile(String mobile) {
        // 参数校验
        if (!StringUtils.hasText(mobile)) {
            return Collections.emptyList();
        }

        // 构建查询条件，查询指定手机号的所有用户的租户ID
        SelectStatementProvider selectStatement = SqlBuilder.select(sysUser.tenantId)
            .from(sysUser)
            .where(sysUser.mobile, isEqualTo(mobile))
            .and(sysUser.status, isEqualTo(1)) // 只查询启用的用户
            .build()
            .render(RenderingStrategies.MYBATIS3);

        // 执行查询并提取租户ID
        List<SysUser> users = userExtendMapper.selectMany(selectStatement);
        return users.stream()
            .map(SysUser::getTenantId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
    }

    /**
     * 根据手机号和租户ID查询用户
     *
     * @param mobile 手机号
     * @param tenantId 租户ID
     * @return 用户信息
     */
    @Override
    public Optional<SysUser> findByMobileAndTenantId(String mobile, Long tenantId) {
        // 参数校验
        if (!StringUtils.hasText(mobile) || tenantId == null) {
            return Optional.empty();
        }

        // 构建查询条件
        SelectStatementProvider selectStatement = SqlBuilder.select(sysUser.allColumns())
            .from(sysUser)
            .where(sysUser.mobile, isEqualTo(mobile))
            .and(sysUser.tenantId, isEqualTo(tenantId))
            .and(sysUser.status, isEqualTo(1)) // 只查询启用的用户
            .build()
            .render(RenderingStrategies.MYBATIS3);

        // 执行查询
        List<SysUser> users = userExtendMapper.selectMany(selectStatement);
        return users.isEmpty() ? Optional.empty() : Optional.of(users.get(0));
    }
}