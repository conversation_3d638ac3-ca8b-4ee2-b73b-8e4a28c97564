package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.dto.RepairItemLibraryQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairItemLibrary;

import java.util.List;

/**
 * 维修项目库表数据访问服务接口
 */
public interface TableRepairItemLibraryService extends DefaultTableService<MtcRepairItemLibrary, Long> {

    /**
     * 查询列表
     *
     * @param queryDTO 查询参数
     * @return
     */
    List<MtcRepairItemLibrary> queryList(RepairItemLibraryQueryDTO queryDTO);

}