package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcOrgInfoDynamicSqlSupport.mtcOrgInfo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.base.MtcOrgInfoExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcOrgInfo;
import com.extracme.saas.autocare.repository.TableOrgInfoService;

/**
 * 组织机构信息Repository实现类
 */
@Repository
public class TableOrgInfoServiceImpl implements TableOrgInfoService {

    @Autowired
    private MtcOrgInfoExtendMapper mtcOrgInfoExtendMapper;

    @Override
    public MtcOrgInfo insert(MtcOrgInfo record) {
        // 从会话中获取当前用户作为操作人
        String operator = "system";
        // 调用带操作人参数的方法
        return insert(record, operator);
    }

    @Override
    public MtcOrgInfo insert(MtcOrgInfo record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreateTime(now);
        record.setCreateBy(operator);
        record.setUpdateTime(now);
        record.setUpdateBy(operator);
        mtcOrgInfoExtendMapper.insertSelective(record);
        return record;
    }

    @Override
    public List<MtcOrgInfo> findValidOrgs() {
        // 构建查询条件，查询所有组织机构
        SelectStatementProvider selectStatement = select(mtcOrgInfo.orgId,mtcOrgInfo.orgName)
            .from(mtcOrgInfo)
            .orderBy(mtcOrgInfo.orgName)
            .build()
            .render(RenderingStrategies.MYBATIS3);

        // 执行查询并处理空结果情况
        return Optional.ofNullable(mtcOrgInfoExtendMapper.selectMany(selectStatement))
            .orElse(Collections.emptyList());
    }

    @Override
    public MtcOrgInfo selectByOrgId(String orgId) {
        // 参数校验
        if (orgId == null || orgId.isEmpty()) {
            return null;
        }
        
        // 使用MyBatis Dynamic SQL构建查询
        SelectStatementProvider selectStatement = select(mtcOrgInfo.allColumns())
            .from(mtcOrgInfo)
            .where(mtcOrgInfo.orgId, isEqualTo(orgId))
            .build()
            .render(RenderingStrategies.MYBATIS3);
        
        // 执行查询并处理结果
        return mtcOrgInfoExtendMapper.selectOne(selectStatement)
            .orElse(null);
    }
}
