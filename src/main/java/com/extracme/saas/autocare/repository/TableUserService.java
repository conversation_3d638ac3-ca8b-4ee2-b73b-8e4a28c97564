package com.extracme.saas.autocare.repository;

import java.util.List;
import java.util.Optional;

import com.extracme.saas.autocare.model.entity.SysUser;

/**
 * 用户表数据访问服务
 */
public interface TableUserService extends DefaultTableService<SysUser, Long> {

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    Optional<SysUser> findByUsername(String username);

    /**
     * 根据手机号查询用户
     *
     * @param mobile 手机号
     * @return 用户信息
     */
    Optional<SysUser> findByMobile(String mobile);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查手机号是否存在
     *
     * @param mobile 手机号
     * @return 是否存在
     */
    boolean existsByMobile(String mobile);

    /**
     * 批量创建用户
     *
     * @param users 用户列表
     * @return 创建成功的用户数量
     */
    int batchCreate(List<SysUser> users);

    /**
     * 统计用户名数量
     *
     * @param username 用户名
     * @return 数量
     */
    int countByUsername(String username);

    /**
     * 统计手机号数量
     *
     * @param mobile 手机号
     * @return 数量
     */
    int countByMobile(String mobile);

    /**
     * 根据条件查询用户列表
     *
     * @param nickname 真实姓名(可选)
     * @param mobile 手机号(可选)
     * @param roleId 角色ID(可选)
     * @param tenantId 租户ID(可选，用于多租户数据隔离)
     * @return 用户列表
     */
    List<SysUser> findByCondition(String nickname, String mobile, Long roleId, Long tenantId);

    /**
     * 查询商户管理员列表
     *
     * @param merchantName 商户名称(可选)
     * @param adminName 管理员姓名(可选)
     * @param mobile 手机号(可选)
     * @param status 状态(可选)
     * @return 商户管理员列表
     */
    List<SysUser> findMerchantAdmins(String merchantName, String adminName, String mobile, Integer status);

    /**
     * 根据手机号查询用户关联的所有租户ID
     *
     * @param mobile 手机号
     * @return 租户ID列表
     */
    List<Long> findTenantIdsByMobile(String mobile);

    /**
     * 根据手机号和租户ID查询用户
     *
     * @param mobile 手机号
     * @param tenantId 租户ID
     * @return 用户信息
     */
    Optional<SysUser> findByMobileAndTenantId(String mobile, Long tenantId);
}