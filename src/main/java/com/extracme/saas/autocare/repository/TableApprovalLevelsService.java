package com.extracme.saas.autocare.repository;

import java.math.BigDecimal;
import java.util.List;

import com.extracme.saas.autocare.model.entity.MtcApprovalLevels;
import com.extracme.saas.autocare.model.vo.base.ComboVO;

/**
 * 审批层级表服务接口
 */
public interface TableApprovalLevelsService {

    /**
     * 插入审批层级
     *
     * @param entity 审批层级实体
     * @return 插入后的实体
     */
    MtcApprovalLevels insert(MtcApprovalLevels entity);

    /**
     * 插入审批层级
     *
     * @param entity 审批层级实体
     * @param operator 操作人
     * @return 插入后的实体
     */
    MtcApprovalLevels insert(MtcApprovalLevels entity, String operator);

    /**
     * 根据ID更新审批层级
     *
     * @param entity 审批层级实体
     * @return 影响行数
     */
    int updateSelectiveById(MtcApprovalLevels entity);

    /**
     * 根据ID更新审批层级
     *
     * @param entity 审批层级实体
     * @param operator 操作人
     * @return 影响行数
     */
    int updateSelectiveById(MtcApprovalLevels entity, String operator);

    /**
     * 根据ID查询审批层级
     *
     * @param id 审批层级ID
     * @return 审批层级实体
     */
    MtcApprovalLevels selectById(Long id);

    /**
     * 根据条件查询审批层级列表
     *
     * @param approvalLevel 审批层级（可选）
     * @param minSelfApprovalAmount 最小自主审批金额（可选）
     * @param maxSelfApprovalAmount 最大自主审批金额（可选）
     * @return 符合条件的审批层级列表
     */
    List<MtcApprovalLevels> findByCondition(Integer approvalLevel, BigDecimal minSelfApprovalAmount, BigDecimal maxSelfApprovalAmount);

    /**
     * 查询所有审批层级列表（按ID降序排序）
     *
     * @return 所有审批层级列表
     */
    List<MtcApprovalLevels> findAll();

    /**
     * 根据租户ID查询审批层级下拉框选项
     *
     * @param tenantId 租户ID
     * @return 审批层级下拉框选项列表
     */
    List<ComboVO<Long>> findComboByTenant(Long tenantId);

    /**
     * 根据ID删除审批层级
     *
     * @param id 审批层级ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 检查指定审批层级是否已存在
     *
     * @param approvalLevel 审批层级
     * @return 是否存在
     */
    boolean existsByApprovalLevel(Integer approvalLevel);

    /**
     * 检查指定审批层级是否已存在（排除指定ID的记录）
     *
     * @param approvalLevel 审批层级
     * @param excludeId 要排除的记录ID
     * @return 是否存在
     */
    boolean existsByApprovalLevelExcludeId(Integer approvalLevel, Long excludeId);
}
