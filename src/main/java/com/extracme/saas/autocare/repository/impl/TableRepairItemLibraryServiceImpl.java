package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcRepairItemLibraryDynamicSqlSupport.mtcRepairItemLibrary;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.util.Date;
import java.util.List;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.extend.MtcRepairItemLibraryExtendMapper;
import com.extracme.saas.autocare.model.dto.RepairItemLibraryQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairItemLibrary;
import com.extracme.saas.autocare.repository.TableRepairItemLibraryService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 维修项目库表数据访问服务实现类
 */
@Repository
public class TableRepairItemLibraryServiceImpl implements TableRepairItemLibraryService {

    @Autowired
    private MtcRepairItemLibraryExtendMapper mapper;

    @Override
    public List<MtcRepairItemLibrary> queryList(RepairItemLibraryQueryDTO queryDTO) {
        SelectStatementProvider selectStatementProvider = select(mtcRepairItemLibrary.allColumns())
                .from(mtcRepairItemLibrary)
                .where(mtcRepairItemLibrary.status, isEqualToWhenPresent(queryDTO.getStatus()))
                .and(mtcRepairItemLibrary.itemNo, isEqualToWhenPresent(queryDTO.getItemNo()))
                .and(mtcRepairItemLibrary.itemName, isEqualToWhenPresent(queryDTO.getItemName()))
                .and(mtcRepairItemLibrary.itemType, isEqualToWhenPresent(queryDTO.getItemType()))
                .and(mtcRepairItemLibrary.vehicleModelSeq, isEqualToWhenPresent(queryDTO.getVehicleModelSeq()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mapper.selectMany(selectStatementProvider);
    }

    @Override
    public MtcRepairItemLibrary selectById(Long id) {
        return mapper.selectByPrimaryKey(id).orElse(null);
    }


    @Override
    public MtcRepairItemLibrary insert(MtcRepairItemLibrary repairItemLibrary) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        insert(repairItemLibrary, operator);
        return repairItemLibrary;
    }

    @Override
    public MtcRepairItemLibrary insert(MtcRepairItemLibrary repairItemLibrary, String operator) {
        Date now = new Date();
        repairItemLibrary.setCreatedTime(now);
        repairItemLibrary.setUpdatedTime(now);
        repairItemLibrary.setCreateBy(operator);
        repairItemLibrary.setUpdateBy(operator);
        mapper.insertSelective(repairItemLibrary);
        return repairItemLibrary;
    }


    @Override
    public int updateSelectiveById(MtcRepairItemLibrary repairItemLibrary, String operator) {
        repairItemLibrary.setUpdatedTime(new Date());
        repairItemLibrary.setUpdateBy(operator);
        return mapper.updateByPrimaryKeySelective(repairItemLibrary);
    }

    @Override
    public int updateSelectiveById(MtcRepairItemLibrary repairItemLibrary) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        return updateSelectiveById(repairItemLibrary, operator);
    }
}