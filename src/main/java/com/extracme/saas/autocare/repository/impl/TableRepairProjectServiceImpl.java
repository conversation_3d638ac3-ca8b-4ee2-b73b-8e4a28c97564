package com.extracme.saas.autocare.repository.impl;

import com.extracme.saas.autocare.mapper.extend.MtcRepairItemExtendMapper;
import com.extracme.saas.autocare.model.dto.RepairProjectQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairItem;
import com.extracme.saas.autocare.repository.TableRepairProjectService;
import com.extracme.saas.autocare.util.SessionUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.extracme.saas.autocare.mapper.base.MtcRepairItemDynamicSqlSupport.mtcRepairItem;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * 修理厂信息Repository实现类
 */
@Repository
public class TableRepairProjectServiceImpl implements TableRepairProjectService {

    @Autowired
    private MtcRepairItemExtendMapper extendMapper;

    @Override
    public List<MtcRepairItem> queryRepairItemList(RepairProjectQueryDTO queryDTO) {
        SelectStatementProvider selectStatement = select(mtcRepairItem.allColumns())
                .from(mtcRepairItem)
                .where()
                .and(mtcRepairItem.orgId, isEqualToWhenPresent(queryDTO.getOrgId()))
                .and(mtcRepairItem.repairName, isLikeWhenPresent(transFuzzyQueryParam(queryDTO.getRepairName())))
                .and(mtcRepairItem.groupingId, isEqualToWhenPresent(queryDTO.getGroupingId()))
                .and(mtcRepairItem.status, isEqualToWhenPresent(queryDTO.getStatus()))
                .orderBy(mtcRepairItem.updatedTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return extendMapper.selectMany(selectStatement);
    }

    @Override
    public MtcRepairItem selectByRepairName(String repairName) {
        SelectStatementProvider selectStatement = select(mtcRepairItem.allColumns())
                .from(mtcRepairItem)
                .where()
                .and(mtcRepairItem.repairName, isEqualTo(repairName))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<MtcRepairItem> optional = extendMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public MtcRepairItem selectById(Long id) {
        SelectStatementProvider selectStatement = select(mtcRepairItem.allColumns())
                .from(mtcRepairItem)
                .where()
                .and(mtcRepairItem.id, isEqualTo(id))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<MtcRepairItem> optional = extendMapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public int deleteById(Long id) {
        if (id == null) {
            return 0;
        }
        return extendMapper.deleteByPrimaryKey(id);
    }

    @Override
    public MtcRepairItem insert(MtcRepairItem mtcRepairItem) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        return insert(mtcRepairItem, operator);
    }

    @Override
    public MtcRepairItem insert(MtcRepairItem mtcRepairItem, String operator) {
        mtcRepairItem.setCreateBy(operator);
        mtcRepairItem.setUpdateBy(operator);
        extendMapper.insertSelective(mtcRepairItem);
        return mtcRepairItem;
    }

    @Override
    public int updateSelectiveById(MtcRepairItem mtcRepairItem) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(mtcRepairItem, operator);
    }

    @Override
    public int updateSelectiveById(MtcRepairItem mtcRepairItem, String operator) {
        if (mtcRepairItem == null) {
            return 0;
        }
        Date now = new Date();
        mtcRepairItem.setUpdatedTime(now);
        mtcRepairItem.setUpdateBy(operator);
        return extendMapper.updateByPrimaryKeySelective(mtcRepairItem);
    }
}