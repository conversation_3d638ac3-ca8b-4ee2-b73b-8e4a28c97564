package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.SysTenant;

import java.util.List;
import java.util.Optional;

/**
 * 租户表数据访问服务接口
 */
public interface TableTenantService extends DefaultTableService<SysTenant, Long> {

    /**
     * 根据租户编码查询租户
     *
     * @param tenantCode 租户编码
     * @return 租户信息
     */
    Optional<SysTenant> findByTenantCode(String tenantCode);

    /**
     * 根据条件查询租户列表
     *
     * @param tenantName 租户名称
     * @param tenantCode 租户编码
     * @param status 状态
     * @return 租户列表
     */
    List<SysTenant> findByCondition(String tenantName, String tenantCode, Integer status);

    /**
     * 检查租户编码是否存在
     *
     * @param tenantCode 租户编码
     * @return 是否存在
     */
    boolean existsByTenantCode(String tenantCode);
}
