package com.extracme.saas.autocare.repository;

import java.util.List;

import com.extracme.saas.autocare.model.entity.SysUserRole;

/**
 * 用户角色关联表数据访问服务
 */
public interface TableUserRoleService {
    
    /**
     * 根据用户ID查询用户角色关联
     *
     * @param userId 用户ID
     * @return 用户角色关联列表
     */
    List<SysUserRole> findByUserId(Long userId);
    
    /**
     * 根据用户ID删除用户角色关联
     *
     * @param userId 用户ID
     * @return 删除的记录数
     */
    int deleteByUserId(Long userId);
    
    /**
     * 批量创建用户角色关联
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 创建的记录数
     */
    int batchCreate(Long userId, List<Long> roleIds);
    
    /**
     * 更新用户角色关联
     * 先删除原有关联，再创建新关联
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 更新的记录数
     */
    int updateUserRoles(Long userId, List<Long> roleIds);
}
