package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.MtcOrgInfo;

import java.util.List;

/**
 * 组织机构信息Repository接口
 */
public interface TableOrgInfoService extends DefaultTableService<MtcOrgInfo, Long> {

    /**
     * 查询有效的组织机构列表
     * @return 组织机构列表
     */
    List<MtcOrgInfo> findValidOrgs();

    /**
     * 根据组织机构ID查询组织机构信息
     * @param orgId 组织机构ID
     * @return 组织机构信息
     */
    MtcOrgInfo selectByOrgId(String orgId);
}
