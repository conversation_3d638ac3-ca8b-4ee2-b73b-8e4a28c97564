package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcVehicleModelDynamicSqlSupport.id;
import static com.extracme.saas.autocare.mapper.base.MtcVehicleModelDynamicSqlSupport.mtcVehicleModel;
import static com.extracme.saas.autocare.mapper.base.MtcVehicleModelDynamicSqlSupport.vehicleModelName;
import static org.mybatis.dynamic.sql.SqlBuilder.isLikeWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.extend.MtcVehicleModelExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcVehicleModel;
import com.extracme.saas.autocare.model.vo.base.ComboVO;
import com.extracme.saas.autocare.repository.TableVehicleModelService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 车型表服务实现类
 */
@Repository
public class TableVehicleModelServiceImpl implements TableVehicleModelService {

    @Autowired
    private MtcVehicleModelExtendMapper mtcVehicleModelMapper;

    @Override
    public MtcVehicleModel insert(MtcVehicleModel record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return insert(record, operator);
    }

    @Override
    public MtcVehicleModel insert(MtcVehicleModel record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreateTime(now);
        record.setCreateBy(operator);
        record.setUpdateTime(now);
        record.setUpdateBy(operator);
        mtcVehicleModelMapper.insertSelective(record);
        return record;
    }

    @Override
    public int updateSelectiveById(MtcVehicleModel record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(record, operator);
    }

    @Override
    public int updateSelectiveById(MtcVehicleModel record, String operator) {
        if (record == null) {
            return 0;
        }
        Date now = new Date();
        record.setUpdateTime(now);
        record.setUpdateBy(operator);
        return mtcVehicleModelMapper.updateByPrimaryKeySelective(record);
    }


    @Override
    public MtcVehicleModel selectById(Long id) {
        return mtcVehicleModelMapper.selectByPrimaryKey(id).orElse(null);
    }


    @Override
    public List<MtcVehicleModel> findByCondition(String vehicleModelNameParam, int limit) {
        // 使用SelectStatementProvider构建查询，利用isLikeWhenPresent自动处理空参数
        SelectStatementProvider selectStatement = select(mtcVehicleModel.allColumns())
            .from(mtcVehicleModel)
            .where()
            .and(vehicleModelName, isLikeWhenPresent(transFuzzyQueryParam(vehicleModelNameParam)))
            .orderBy(id.descending())
            .limit(limit)
            .build()
            .render(RenderingStrategies.MYBATIS3);

        return mtcVehicleModelMapper.selectMany(selectStatement);
    }

    @Override
    public List<ComboVO<Long>> getVehicleModelCombo(String vehicleModelNameParam, int limit) {
        // 查询车型列表
        List<MtcVehicleModel> vehicleModels = findByCondition(vehicleModelNameParam, limit);

        // 转换为下拉列表数据
        List<ComboVO<Long>> comboList = new ArrayList<>();
        for (MtcVehicleModel model : vehicleModels) {
            ComboVO<Long> combo = new ComboVO<>();
            combo.setId(model.getId());
            combo.setValue(model.getVehicleModelName());
            comboList.add(combo);
        }

        return comboList;
    }
}
