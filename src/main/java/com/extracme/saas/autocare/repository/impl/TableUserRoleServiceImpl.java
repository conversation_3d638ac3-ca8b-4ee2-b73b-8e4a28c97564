package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.SysUserRoleDynamicSqlSupport.sysUserRole;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.mapper.extend.SysUserRoleExtendMapper;
import com.extracme.saas.autocare.model.entity.SysUserRole;
import com.extracme.saas.autocare.repository.TableUserRoleService;

/**
 * 用户角色关联表数据访问服务实现类
 */
@Repository
public class TableUserRoleServiceImpl implements TableUserRoleService {

    @Autowired
    private SysUserRoleExtendMapper userRoleMapper;

    @Override
    public List<SysUserRole> findByUserId(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        return userRoleMapper.select(c -> c.where(sysUserRole.userId, isEqualTo(userId)));
    }

    @Override
    public int deleteByUserId(Long userId) {
        if (userId == null) {
            return 0;
        }
        return userRoleMapper.delete(c -> c.where(sysUserRole.userId, isEqualTo(userId)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreate(Long userId, List<Long> roleIds) {
        if (userId == null || roleIds == null || roleIds.isEmpty()) {
            return 0;
        }

        int count = 0;
        Date now = new Date();
        for (Long roleId : roleIds) {
            SysUserRole userRole = new SysUserRole();
            userRole.setUserId(userId);
            userRole.setRoleId(roleId);
            userRole.setCreatedTime(now);
            userRoleMapper.insertSelective(userRole);
            count++;
        }
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateUserRoles(Long userId, List<Long> roleIds) {
        if (userId == null) {
            return 0;
        }

        // 删除原有关联
        deleteByUserId(userId);

        // 如果角色ID列表为空，则只删除不创建
        if (roleIds == null || roleIds.isEmpty()) {
            return 0;
        }

        // 创建新关联
        return batchCreate(userId, roleIds);
    }
}
