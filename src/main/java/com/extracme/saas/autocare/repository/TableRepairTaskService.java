package com.extracme.saas.autocare.repository;

import java.util.List;

import com.extracme.saas.autocare.model.dto.RepairDepotQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskListQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskProcessQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.vo.RepairDepotInRepairingVO;
import com.extracme.saas.autocare.model.vo.RepairTaskProcessListVO;

/**
 * 维修任务表数据访问服务接口
 */
public interface TableRepairTaskService extends DefaultTableService<MtcRepairTask, Long> {

    /**
     * 根据任务编号查询维修任务
     * @param taskNo 任务编号
     * @return 维修任务
     */
    MtcRepairTask selectByTaskNo(String taskNo);

    /**
     * 根据车架号查询维修任务
     * @param vin 车架号
     * @return 维修任务列表
     */
    List<MtcRepairTask> selectByVin(String vin);

    /**
     * 流程查询-查询维修任务列表
     * @param queryDTO 查询条件
     * @return 维修任务列表
     */
    List<RepairTaskProcessListVO> queryProcessList(RepairTaskProcessQueryDTO queryDTO);

    /**
     * 获取任务是否超时
     * @param id 维修任务ID
     * @return 是否超时 0:是 1:否
     */
    String getOverTime(Long id);

    /**
     * 通过活动实例表查询维修任务列表
     * @param queryDTO 查询条件
     * @param tenantId 租户ID
     * @return 维修任务列表
     */
    List<RepairTaskProcessListVO> queryRepairTaskListByActivityInstance(RepairTaskListQueryDTO queryDTO, Integer tenantId);

    /**
     * 清除任务核损核价占有人
     * @param taskNo 维修任务编号
     * @param operator 操作人
     * @return 更新结果
     */
    int clearOwner(Long taskId, String operator);

    /**
     * 获取当前维修厂正在维修的数量
     * 
     * @param repairDepotId
     * @param repairGrade
     * @param currentActivityCode
     * @return
     */
    long getCurrentRepairNum(String repairDepotId, String repairGrade, String currentActivityCode);

    /**
     * 获取当前维修厂正在维修的数量
     * 
     * @param repairDepotId
     * @return
     */
    long getTodayAddRepairNum(String repairDepotId);

    /**
     * 查询修理厂在修信息
     * 
     * @param queryDTO 查询条件
     * @return 修理厂在修信息列表
     */
    List<RepairDepotInRepairingVO> queryRepairDepotInRepairingInfo(RepairDepotQueryDTO queryDTO);
}
