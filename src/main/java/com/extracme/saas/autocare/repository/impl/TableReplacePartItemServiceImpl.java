package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcReplaceItemDynamicSqlSupport.mtcReplaceItem;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isLikeWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.extend.MtcReplaceItemExtendMapper;
import com.extracme.saas.autocare.model.dto.ReplacePartItemQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcReplaceItem;
import com.extracme.saas.autocare.repository.TableReplacePartItemService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 修理厂信息Repository实现类
 */
@Repository
public class TableReplacePartItemServiceImpl implements TableReplacePartItemService {

    @Autowired
    private MtcReplaceItemExtendMapper mapper;

    @Override
    public List<MtcReplaceItem> queryReplaceItemList(ReplacePartItemQueryDTO queryDTO) {
        SelectStatementProvider selectStatement = select(mtcReplaceItem.allColumns())
                .from(mtcReplaceItem)
                .where()
                .and(mtcReplaceItem.orgId, isEqualToWhenPresent(queryDTO.getOrgId()))
                .and(mtcReplaceItem.partName, isLikeWhenPresent(transFuzzyQueryParam(queryDTO.getPartName())))
                .and(mtcReplaceItem.groupingId, isEqualToWhenPresent(queryDTO.getGroupingId()))
                .and(mtcReplaceItem.vehicleModelSeq, isEqualToWhenPresent(queryDTO.getVehicleModelSeq()))
                .and(mtcReplaceItem.status, isEqualToWhenPresent(queryDTO.getStatus()))
                .orderBy(mtcReplaceItem.updatedTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mapper.selectMany(selectStatement);
    }

    @Override
    public MtcReplaceItem selectByPartItemName(String partItemName) {
        SelectStatementProvider selectStatement = select(mtcReplaceItem.allColumns())
                .from(mtcReplaceItem)
                .where()
                .and(mtcReplaceItem.partName, isEqualTo(partItemName))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<MtcReplaceItem> optional = mapper.selectOne(selectStatement);
        return optional.orElse(null);
    }

    @Override
    public int deleteById(Long id) {
        if (id == null) {
            return 0;
        }
        return mapper.deleteByPrimaryKey(id);
    }


    @Override
    public MtcReplaceItem selectById(Long id) {
        Optional<MtcReplaceItem> optional = mapper.selectByPrimaryKey(id);
        return optional.orElse(null);
    }

    @Override
    public MtcReplaceItem insert(MtcReplaceItem mtcReplaceItem) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return insert(mtcReplaceItem, operator);
    }

    @Override
    public MtcReplaceItem insert(MtcReplaceItem mtcReplaceItem, String operator) {
        if (mtcReplaceItem == null) {
            return null;
        }
        Date now = new Date();
        mtcReplaceItem.setCreatedTime(now);
        mtcReplaceItem.setCreateBy(operator);
        mtcReplaceItem.setUpdatedTime(now);
        mtcReplaceItem.setUpdateBy(operator);
        mapper.insertSelective(mtcReplaceItem);
        return mtcReplaceItem;
    }

    @Override
    public int updateSelectiveById(MtcReplaceItem mtcReplaceItem) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(mtcReplaceItem, operator);
    }

    @Override
    public int updateSelectiveById(MtcReplaceItem mtcReplaceItem, String operator) {
        if (mtcReplaceItem == null) {
            return 0;
        }
        Date now = new Date();
        mtcReplaceItem.setUpdatedTime(now);
        mtcReplaceItem.setUpdateBy(operator);
        return mapper.updateByPrimaryKeySelective(mtcReplaceItem);
    }
}