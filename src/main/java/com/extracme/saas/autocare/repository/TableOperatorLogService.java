package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.dto.OperatorLogDTO;
import com.extracme.saas.autocare.model.entity.MtcOperatorLog;

import java.util.List;

/**
 * 操作日志数据访问服务
 */
public interface TableOperatorLogService {

    /**
     * 查询操作日志
     *
     * @param recordId  记录id
     * @param tableName 表名
     * @return 操作日志DTO列表
     */
    List<OperatorLogDTO> queryOperatorLog(Long recordId, String tableName);

    /**
     * 插入操作日志
     *
     * @param log 操作日志实体
     * @return 影响行数
     */
    int insertSelective(MtcOperatorLog log);

    /**
     * 根据记录ID查询操作日志
     *
     * @param recordId 记录ID
     * @return 操作日志列表
     */
    List<MtcOperatorLog> selectByRecordId(Long recordId);

    /**
     * 根据记录ID和环节查询操作日志
     *
     * @param recordId 记录ID
     * @param currentTache 当前环节
     * @return 操作日志列表
     */
    List<MtcOperatorLog> selectByRecordIdAndTache(Long recordId, String currentTache);

    /**
     * 根据主键选择性更新操作日志
     *
     * @param log 操作日志实体
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(MtcOperatorLog log);
} 