package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.SysPermissionDynamicSqlSupport.sysPermission;
import static com.extracme.saas.autocare.mapper.base.SysRolePermissionDynamicSqlSupport.sysRolePermission;
import static com.extracme.saas.autocare.mapper.base.SysUserRoleDynamicSqlSupport.sysUserRole;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.mapper.extend.SysPermissionExtendMapper;
import com.extracme.saas.autocare.mapper.extend.SysRolePermissionExtendMapper;
import com.extracme.saas.autocare.mapper.extend.SysUserRoleExtendMapper;
import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.entity.SysRolePermission;
import com.extracme.saas.autocare.model.entity.SysUserRole;
import com.extracme.saas.autocare.repository.TablePermissionService;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;

/**
 * 权限表数据访问服务实现类
 *
 * 主要功能：
 * 1. 提供权限的基础CRUD操作
 * 2. 提供权限与角色的关联操作
 * 3. 提供权限的高级查询功能
 * 4. 确保数据访问的事务性和安全性
 */
@Repository
public class TablePermissionServiceImpl implements TablePermissionService {

    @Autowired
    private SysPermissionExtendMapper permissionExtendMapper;

    @Autowired
    private SysRolePermissionExtendMapper rolePermissionExtendMapper;

    @Autowired
    private SysUserRoleExtendMapper userRoleMapper;


    /**
     * 根据权限编码查询权限信息
     * 只返回未删除的权限
     *
     * @param permissionCode 权限编码
     * @return 权限信息
     */
    @Override
    public Optional<SysPermission> findByPermissionCode(String permissionCode) {
        return permissionExtendMapper.selectOne(c -> c.where(sysPermission.permissionCode, isEqualTo(permissionCode)));
    }

    /**
     * 根据父级ID查询权限列表
     * 只返回未删除的权限
     *
     * @param parentId 父级权限ID
     * @return 权限列表
     */
    @Override
    public List<SysPermission> findByParentId(Long parentId) {
        return permissionExtendMapper.select(c -> c.where(sysPermission.parentId, isEqualTo(parentId)));
    }

    /**
     * 查询所有未删除的权限
     *
     * @return 权限列表
     */
    @Override
    public List<SysPermission> findAll() {
        return permissionExtendMapper.select(c -> c);
    }

    /**
     * 删除指定角色的所有权限关联
     *
     * @param roleId 角色ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByRoleId(Long roleId) {
        if (roleId == null) {
            return;
        }
        rolePermissionExtendMapper.delete(c ->
            c.where(sysRolePermission.roleId, isEqualTo(roleId)));
    }

    /**
     * 批量创建角色权限关联
     * 通过循环单条插入的方式实现
     *
     * @param rolePermissions 角色权限关联列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCreate(List<SysRolePermission> rolePermissions) {
        if (rolePermissions == null || rolePermissions.isEmpty()) {
            return;
        }
        Date now = new Date();
        for (SysRolePermission rolePermission : rolePermissions) {
            rolePermission.setCreatedTime(now);
            rolePermissionExtendMapper.insertSelective(rolePermission);
        }
    }

    /**
     * 根据角色ID查询权限ID列表
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    @Override
    public List<Long> findPermissionIdsByRoleId(Long roleId) {
        if (roleId == null) {
            return Collections.emptyList();
        }
        SelectStatementProvider selectStatement = SqlBuilder.select(sysRolePermission.permissionId)
                .from(sysRolePermission)
                .where(sysRolePermission.roleId, isEqualTo(roleId))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        return rolePermissionExtendMapper.selectMany(selectStatement)
                .stream()
                .map(SysRolePermission::getPermissionId)
                .collect(Collectors.toList());
    }

    /**
     * 根据角色ID列表查询权限ID列表
     *
     * @param roleIds 角色ID列表
     * @return 权限ID列表
     */
    @Override
    public List<Long> findPermissionIdsByRoleIds(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return Collections.emptyList();
        }
        SelectStatementProvider selectStatement = SqlBuilder.select(sysRolePermission.permissionId)
                .from(sysRolePermission)
                .where(sysRolePermission.roleId, isIn(roleIds))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        return rolePermissionExtendMapper.selectMany(selectStatement)
                .stream()
                .map(SysRolePermission::getPermissionId)
                .collect(Collectors.toList());
    }

    /**
     * 根据权限类型查询权限列表
     * 只返回未删除的权限，按排序字段排序
     *
     * @param type 权限类型
     * @return 权限列表
     */
    @Override
    public List<SysPermission> findByType(Integer type) {
        if (type == null) {
            return Collections.emptyList();
        }
        return permissionExtendMapper.select(c -> c.where(sysPermission.permissionType, isEqualTo(type.toString())).orderBy(sysPermission.sort));
    }

    /**
     * 根据用户ID查询权限列表
     * 查询流程：用户->角色->权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    @Override
    public List<SysPermission> findByUserId(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        // 获取用户角色
        SelectStatementProvider selectRoleIdsStatement = SqlBuilder.select(sysUserRole.roleId)
            .from(sysUserRole)
            .where(sysUserRole.userId, isEqualTo(userId))
            .build()
            .render(RenderingStrategies.MYBATIS3);
        List<Long> roleIds = userRoleMapper.selectMany(selectRoleIdsStatement)
            .stream()
            .map(SysUserRole::getRoleId)
            .collect(Collectors.toList());

        if (roleIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取角色权限
        SelectStatementProvider selectPermissionIdsStatement = SqlBuilder.select(sysRolePermission.permissionId)
            .from(sysRolePermission)
            .where(sysRolePermission.roleId, isIn(roleIds))
            .build()
            .render(RenderingStrategies.MYBATIS3);
        List<Long> permissionIds = rolePermissionExtendMapper.selectMany(selectPermissionIdsStatement)
            .stream()
            .map(SysRolePermission::getPermissionId)
            .collect(Collectors.toList());

        // 获取权限详情
        if (permissionIds.isEmpty()) {
            return Collections.emptyList();
        }
        return permissionExtendMapper.select(c ->
            c.where(sysPermission.id, isIn(permissionIds)));
    }

    /**
     * 根据角色ID查询权限列表
     * 只返回未删除的权限
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    @Override
    public List<SysPermission> findByRoleId(Long roleId) {
        if (roleId == null) {
            return Collections.emptyList();
        }
        // 获取角色权限ID列表
        SelectStatementProvider selectStatement = SqlBuilder.select(sysRolePermission.permissionId)
            .from(sysRolePermission)
            .where(sysRolePermission.roleId, isEqualTo(roleId))
            .build()
            .render(RenderingStrategies.MYBATIS3);
        List<Long> permissionIds = rolePermissionExtendMapper.selectMany(selectStatement)
            .stream()
            .map(SysRolePermission::getPermissionId)
            .collect(Collectors.toList());

        // 获取权限详情
        if (permissionIds.isEmpty()) {
            return Collections.emptyList();
        }
        return permissionExtendMapper.select(c ->
            c.where(sysPermission.id, isIn(permissionIds)));
    }

    /**
     * 分配角色权限
     * 先删除原有权限，再添加新权限
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignRolePermissions(Long roleId, List<Long> permissionIds) {
        if (roleId == null) {
            return;
        }
        // 删除原有权限
        rolePermissionExtendMapper.delete(c ->
            c.where(sysRolePermission.roleId, isEqualTo(roleId)));

        // 分配新权限
        if (permissionIds != null && !permissionIds.isEmpty()) {
            Date now = new Date();
            for (Long permissionId : permissionIds) {
                SysRolePermission rolePermission = new SysRolePermission();
                rolePermission.setRoleId(roleId);
                rolePermission.setPermissionId(permissionId);
                rolePermission.setCreatedTime(now);
                rolePermissionExtendMapper.insertSelective(rolePermission);
            }
        }
    }

    /**
     * 检查用户是否拥有指定权限
     *
     * @param userId 用户ID
     * @param permissionCode 权限编码
     * @return true:有权限 false:无权限
     */
    @Override
    public boolean hasPermission(Long userId, String permissionCode) {
        if (userId == null || permissionCode == null || permissionCode.isEmpty()) {
            return false;
        }
        // 获取用户权限列表
        List<SysPermission> permissions = findByUserId(userId);

        // 检查是否包含指定权限
        return permissions.stream()
                .anyMatch(p -> permissionCode.equals(p.getPermissionCode()));
    }

    /**
     * 检查权限编码是否已存在
     *
     * @param code 权限编码
     * @return true:已存在 false:不存在
     */
    @Override
    public boolean existsByCode(String code) {
        if (code == null || code.isEmpty()) {
            return false;
        }
        return permissionExtendMapper.count(c -> c.where(sysPermission.permissionCode, isEqualTo(code))) > 0;
    }

    /**
     * 检查权限是否被角色使用
     *
     * @param permissionId 权限ID
     * @return true:正在使用 false:未使用
     */
    @Override
    public boolean isUsedByRoles(Long permissionId) {
        if (permissionId == null) {
            return false;
        }
        return rolePermissionExtendMapper.count(c ->
            c.where(sysRolePermission.permissionId, isEqualTo(permissionId))) > 0;
    }

    @Override
    public SysPermission insert(SysPermission permission) {
        // 使用系统操作人
        return insert(permission, "system");
    }

    @Override
    public SysPermission insert(SysPermission permission, String operator) {
        Date now = new Date();
        permission.setCreatedTime(now);
        permission.setUpdatedTime(now);
        permission.setCreateBy(operator);
        permission.setUpdateBy(operator);
        permissionExtendMapper.insertSelective(permission);
        return permission;
    }

    @Override
    public int updateSelectiveById(SysPermission permission) {
        // 使用系统操作人
        return updateSelectiveById(permission, "system");
    }

    @Override
    public int updateSelectiveById(SysPermission permission, String operator) {
        Date now = new Date();
        permission.setUpdatedTime(now);
        permission.setUpdateBy(operator);
        return permissionExtendMapper.updateByPrimaryKeySelective(permission);
    }

    @Override
    public SysPermission selectById(Long id) {
        return permissionExtendMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public int deleteById(Long id) {
        if (id == null) {
            return 0;
        }
        return permissionExtendMapper.deleteByPrimaryKey(id);
    }
}