package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.MtcRepairRemark;

import java.util.List;

/**
 * 维修备注表数据访问服务接口
 */
public interface TableRepairRemarkService extends DefaultTableService<MtcRepairRemark, Long> {

    /**
     * 根据任务编号查询维修备注列表
     * @param taskNo 任务编号
     * @return 维修备注列表
     */
    List<MtcRepairRemark> selectByTaskNo(String taskNo);
    
    /**
     * 根据任务编号和维修阶段查询维修备注
     * @param taskNo 任务编号
     * @param repairStage 维修阶段
     * @return 维修备注列表
     */
    List<MtcRepairRemark> selectByTaskNoAndStage(String taskNo, Short repairStage);
    
    /**
     * 批量插入维修备注
     * @param remarkList 维修备注列表
     * @return 影响行数
     */
    int batchInsert(List<MtcRepairRemark> remarkList);
    
    /**
     * 根据ID删除维修备注
     * @param id 维修备注ID
     * @return 影响行数
     */
    int deleteById(Long id);
}
