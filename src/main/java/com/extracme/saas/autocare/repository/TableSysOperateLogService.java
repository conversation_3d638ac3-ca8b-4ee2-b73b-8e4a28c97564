package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.entity.SysOperateLog;

import java.util.List;

/**
 * 操作日志表数据访问服务接口
 */
public interface TableSysOperateLogService extends DefaultTableService<SysOperateLog, Long> {

    /**
     * 根据查询条件查找操作日志（支持多租户权限控制）
     * 
     * 多租户访问控制逻辑：
     * - 超级管理员：可以查询所有租户的操作日志
     * - 普通用户：只能查询自己租户的操作日志
     *
     * @param tenantId 租户ID（可选，用于多租户数据隔离）
     * @return 操作日志列表
     */
    List<SysOperateLog> findByCondition(Long tenantId);
}
