package com.extracme.saas.autocare.repository.impl;

import com.extracme.saas.autocare.mapper.extend.ActivityInstanceExtendMapper;
import com.extracme.saas.autocare.model.entity.ActivityInstance;
import com.extracme.saas.autocare.repository.TableActivityInstanceService;
import com.extracme.saas.autocare.util.SessionUtils;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.HashMap;
import java.util.Map;

import static com.extracme.saas.autocare.mapper.base.ActivityInstanceDynamicSqlSupport.activityInstance;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static com.extracme.saas.autocare.mapper.base.WorkflowInstanceDynamicSqlSupport.workflowInstance;
import static org.mybatis.dynamic.sql.SqlBuilder.equalTo;

/**
 * 活动实例记录表数据访问服务实现类
 *
 * <AUTHOR>
 * @date 2024/03/21
 */
@Slf4j
@Repository
public class TableActivityInstanceServiceImpl implements TableActivityInstanceService {

    @Autowired
    private ActivityInstanceExtendMapper activityInstanceMapper;

    @Override
    public ActivityInstance insert(ActivityInstance record) {
        // 使用系统操作人
        return insert(record, "system");
    }

    @Override
    public ActivityInstance selectById(Long id) {
        if (id == null) {
            return null;
        }
        Optional<ActivityInstance> optional = activityInstanceMapper.selectByPrimaryKey(id);
        return optional.orElse(null);
    }

    @Override
    public List<ActivityInstance> selectByInstanceId(Long instanceId) {
        if (instanceId == null) {
            return Collections.emptyList();
        }
        return activityInstanceMapper.select(c ->
                c.where(activityInstance.instanceId, isEqualTo(instanceId))
                .orderBy(activityInstance.startTime.descending())
        );
    }

    @Override
    public ActivityInstance selectByInstanceIdAndToActivityCode(Long instanceId, String toActivityCode) {
        if (instanceId == null || toActivityCode == null) {
            return null;
        }
        Optional<ActivityInstance> optional = activityInstanceMapper.selectOne(c ->
                c.where(activityInstance.instanceId, isEqualTo(instanceId))
                .and(activityInstance.toActivityCode, isEqualTo(toActivityCode))
        );
        return optional.orElse(null);
    }

    @Override
    public List<ActivityInstance> selectByInstanceIdAndStatusCode(Long instanceId, String currentStatusCode) {
        if (instanceId == null || currentStatusCode == null) {
            return Collections.emptyList();
        }
        return activityInstanceMapper.select(c ->
                c.where(activityInstance.instanceId, isEqualTo(instanceId))
                .and(activityInstance.currentStatusCode, isEqualTo(currentStatusCode))
                .orderBy(activityInstance.startTime.descending())
        );
    }

    @Override
    public int updateEndTimeAndDuration(Long id, Date endTime, Integer duration) {
        if (id == null || endTime == null || duration == null) {
            return 0;
        }
        ActivityInstance record = new ActivityInstance();
        record.setId(id);
        record.setEndTime(endTime);
        record.setDuration(duration);
        return activityInstanceMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateStatusCode(Long id, String statusCode) {
        if (id == null || statusCode == null) {
            log.warn("updateStatusCode 参数无效，id: {}, statusCode: {}", id, statusCode);
            return 0;
        }

        log.debug("开始更新活动实例状态码，ID: {}, 新状态码: {}", id, statusCode);

        try {
            // 查询更新前的状态，用于日志记录
            Optional<ActivityInstance> existingOptional = activityInstanceMapper.selectByPrimaryKey(id);
            if (!existingOptional.isPresent()) {
                log.error("活动实例不存在，无法更新状态码，ID: {}", id);
                return 0;
            }

            ActivityInstance existing = existingOptional.get();
            String oldStatusCode = existing.getCurrentStatusCode();
            log.debug("活动实例更新前状态，ID: {}, 原状态码: {}, 新状态码: {}", id, oldStatusCode, statusCode);

            // 如果状态码相同，无需更新
            if (statusCode.equals(oldStatusCode)) {
                log.debug("状态码未发生变化，跳过更新，ID: {}, 状态码: {}", id, statusCode);
                return 1; // 返回1表示逻辑上成功，但实际未执行更新
            }

            // 构建更新对象
            ActivityInstance record = new ActivityInstance();
            record.setId(id);
            record.setCurrentStatusCode(statusCode);
            record.setUpdateTime(new Date());
            record.setUpdateBy("system"); // 使用系统作为更新人

            // 执行更新操作
            int result = activityInstanceMapper.updateByPrimaryKeySelective(record);

            if (result > 0) {
                log.info("活动实例状态码更新成功，ID: {}, 从 {} 更新为 {}, 影响行数: {}",
                        id, oldStatusCode, statusCode, result);
            } else {
                log.error("活动实例状态码更新失败，ID: {}, 从 {} 更新为 {}, 影响行数: {}",
                        id, oldStatusCode, statusCode, result);
            }

            return result;
        } catch (Exception e) {
            log.error("更新活动实例状态码时发生异常，ID: {}, 状态码: {}, 异常: {}", id, statusCode, e.getMessage(), e);
            throw new RuntimeException("更新活动实例状态码失败", e);
        }
    }

    @Override
    public int updateSelectiveById(ActivityInstance record) {
        if (record == null || record.getId() == null) {
            return 0;
        }
        // 使用系统操作人
        return updateSelectiveById(record, "system");
    }

    @Override
    public ActivityInstance insert(ActivityInstance record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreateTime(now);
        record.setCreateBy(operator);
        record.setUpdateTime(now);
        record.setUpdateBy(operator);
        activityInstanceMapper.insertSelective(record);
        return record;
    }

    @Override
    public int updateSelectiveById(ActivityInstance record, String operator) {
        if (record == null || record.getId() == null) {
            return 0;
        }
        record.setUpdateTime(new Date());
        record.setUpdateBy(operator);
        return activityInstanceMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Map<String, Long> countByToActivityCodeGroupByStatusCode(String toActivityCode) {
        if (toActivityCode == null) {
            return new HashMap<>();
        }

        // 直接查询所有符合条件的记录
        List<ActivityInstance> allInstances = activityInstanceMapper.select(c ->
                c.join(workflowInstance)
                .on(activityInstance.instanceId, equalTo(workflowInstance.id))
                .where(activityInstance.toActivityCode, isEqualTo(toActivityCode))
                .and(workflowInstance.tenantId, isEqualTo(SessionUtils.getTenantId().intValue()))
        );

        // 在内存中进行分组统计
        Map<String, Long> resultMap = new HashMap<>();
        for (ActivityInstance instance : allInstances) {
            String statusCode = instance.getCurrentStatusCode();
            if (statusCode != null) {
                resultMap.put(statusCode, resultMap.getOrDefault(statusCode, 0L) + 1);
            }
        }

        return resultMap;
    }
}
