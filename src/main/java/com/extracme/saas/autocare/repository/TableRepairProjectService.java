package com.extracme.saas.autocare.repository;

import com.extracme.saas.autocare.model.dto.RepairProjectQueryDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairItem;

import java.util.List;

/**
 * 修理项目信息Repository接口
 */
public interface TableRepairProjectService extends DefaultTableService<MtcRepairItem, Long> {

    /**
     * 根据条件查询列表
     * @param queryDTO 查询条件
     * @return 修理厂列表信息
     */
    List<MtcRepairItem> queryRepairItemList(RepairProjectQueryDTO queryDTO);


    /**
     * 根据修理名称查询
     * @param repairName 修理名称
     * @return 修理厂信息
     */
    MtcRepairItem selectByRepairName(String repairName);

    /**
     * 根据ID删除
     * @param id ID
     * @return 删除结果
     */
    int deleteById(Long id);
}