package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcRepairTaskLeavingFactoryDynamicSqlSupport.id;
import static com.extracme.saas.autocare.mapper.base.MtcRepairTaskLeavingFactoryDynamicSqlSupport.updateBy;
import static com.extracme.saas.autocare.mapper.base.MtcRepairTaskLeavingFactoryDynamicSqlSupport.updatedTime;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.mapper.base.MtcRepairTaskLeavingFactoryDynamicSqlSupport;
import com.extracme.saas.autocare.mapper.extend.MtcRepairTaskLeavingFactoryExtendMapper;
import com.extracme.saas.autocare.model.entity.MtcRepairTaskLeavingFactory;
import com.extracme.saas.autocare.repository.TableRepairTaskLeavingFactoryService;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 维修任务出厂表数据访问服务实现
 */
@Repository
public class TableRepairTaskLeavingFactoryServiceImpl implements TableRepairTaskLeavingFactoryService {

    @Autowired
    private MtcRepairTaskLeavingFactoryExtendMapper mtcRepairTaskLeavingFactoryMapper;

    @Override
    public MtcRepairTaskLeavingFactory insert(MtcRepairTaskLeavingFactory record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return insert(record, operator);
    }

    @Override
    public MtcRepairTaskLeavingFactory insert(MtcRepairTaskLeavingFactory record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreatedTime(now);
        record.setCreateBy(operator);
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        mtcRepairTaskLeavingFactoryMapper.insertSelective(record);
        return record;
    }

    @Override
    public int updateSelectiveById(MtcRepairTaskLeavingFactory record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(record, operator);
    }

    @Override
    public int updateSelectiveById(MtcRepairTaskLeavingFactory record, String operator) {
        if (record == null) {
            return 0;
        }
        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        return mtcRepairTaskLeavingFactoryMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public MtcRepairTaskLeavingFactory selectById(Long id) {
        if (id == null) {
            return null;
        }
        return mtcRepairTaskLeavingFactoryMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public MtcRepairTaskLeavingFactory selectByTaskNo(String taskNo) {
        SelectDSLCompleter completer = c -> c.where(MtcRepairTaskLeavingFactoryDynamicSqlSupport.taskNo, isEqualTo(taskNo));
        Optional<MtcRepairTaskLeavingFactory> optionalLeaving = mtcRepairTaskLeavingFactoryMapper.selectOne(completer);
        return optionalLeaving.orElse(null);
    }

    @Override
    public List<MtcRepairTaskLeavingFactory> selectByVin(String vin) {
        SelectDSLCompleter completer = c -> c.where(MtcRepairTaskLeavingFactoryDynamicSqlSupport.vin, isEqualTo(vin))
                .orderBy(id.descending());
        return mtcRepairTaskLeavingFactoryMapper.select(completer);
    }

    @Override
    public List<MtcRepairTaskLeavingFactory> selectByRepairDepotId(String repairDepotId) {
        SelectDSLCompleter completer = c -> c.where(MtcRepairTaskLeavingFactoryDynamicSqlSupport.repairDepotId, isEqualTo(repairDepotId))
                .orderBy(id.descending());
        return mtcRepairTaskLeavingFactoryMapper.select(completer);
    }

    @Override
    public int updateLeavingStatus(String taskNo, Integer leavingStatus, String operator) {
        MtcRepairTaskLeavingFactory record = new MtcRepairTaskLeavingFactory();
        record.setLeavingStatus(leavingStatus);
        record.setUpdateBy(operator);
        record.setUpdatedTime(new Date());

        return mtcRepairTaskLeavingFactoryMapper.update(c ->
            c.set(MtcRepairTaskLeavingFactoryDynamicSqlSupport.leavingStatus).equalTo(record::getLeavingStatus)
             .set(updateBy).equalTo(record::getUpdateBy)
             .set(updatedTime).equalTo(record::getUpdatedTime)
             .where(MtcRepairTaskLeavingFactoryDynamicSqlSupport.taskNo, isEqualTo(taskNo)));
    }
}
