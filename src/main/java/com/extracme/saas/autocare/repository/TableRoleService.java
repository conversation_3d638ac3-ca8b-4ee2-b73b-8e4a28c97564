package com.extracme.saas.autocare.repository;

import java.util.List;
import java.util.Optional;

import com.extracme.saas.autocare.model.entity.SysRole;

/**
 * 角色表数据访问服务接口
 */
public interface TableRoleService extends DefaultTableService<SysRole, Long> {

    /**
     * 根据角色编码查找角色
     *
     * @param roleCode 角色编码
     * @return 角色信息
     */
    Optional<SysRole> findByRoleCode(String roleCode);




    /**
     * 查找所有未删除的角色
     *
     * @return 角色列表
     */
    List<SysRole> findAll();


    /**
     * 根据查询条件查找角色（支持角色名称模糊查询和租户过滤）
     *
     * @param roleName 角色名称（可选，支持模糊查询）
     * @param tenantId 租户ID（可选，用于多租户数据隔离）
     * @return 角色列表
     */
    List<SysRole> findByCondition(String roleName, Long tenantId);

    /**
     * 检查角色编码是否存在
     *
     * @param code 角色编码
     * @return 是否存在
     */
    boolean existsByCode(String code);

    /**
     * 检查角色是否被用户使用
     *
     * @param roleId 角色ID
     * @return 是否被使用
     */
    boolean isUsedByUsers(Long roleId);

    /**
     * 批量创建角色
     *
     * @param roles 角色列表
     * @return 创建成功的角色数量
     */
    int batchCreate(List<SysRole> roles);

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRole> findByUserId(Long userId);

    /**
     * 检查用户是否有指定角色
     *
     * @param userId 用户ID
     * @param roleCode 角色编码
     * @return 是否有角色
     */
    boolean hasRole(Long userId, String roleCode);
}