package com.extracme.saas.autocare.enums;

import javax.servlet.http.HttpServletResponse;

/**
 * 系统错误码枚举
 *
 * 错误码规则：
 * 1. HTTP状态码作为基础
 * 2. 业务错误码格式：模块代码(2位) + 错误类型(2位) + 序号(2位)
 *    例如：100101 表示用户模块(10)的参数错误(01)的第一个错误(01)
 */
public enum ErrorCode {

    /**
     * 通用错误码
     */
    SUCCESS(HttpServletResponse.SC_OK, "操作成功"),
    PARAM_MISSING(HttpServletResponse.SC_BAD_REQUEST, "参数缺失"),
    PARAM_TYPE_ERROR(HttpServletResponse.SC_BAD_REQUEST, "参数类型错误"),
    PARAM_VALID_ERROR(HttpServletResponse.SC_BAD_REQUEST, "参数校验错误"),
    PARAM_BIND_ERROR(HttpServletResponse.SC_BAD_REQUEST, "参数绑定错误"),
    UNAUTHORIZED(HttpServletResponse.SC_UNAUTHORIZED, "未授权"),
    FORBIDDEN(HttpServletResponse.SC_FORBIDDEN, "禁止访问"),
    NOT_FOUND(HttpServletResponse.SC_NOT_FOUND, "资源不存在"),
    METHOD_NOT_ALLOWED(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "请求方法不允许"),
    INTERNAL_SERVER_ERROR(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "服务器内部错误"),

    /**
     * 权限模块错误码 (20)
     */
    PERMISSION_NOT_FOUND(200001, "权限不存在"),
    PERMISSION_CODE_EXISTS(200002, "权限编码已存在"),
    PERMISSION_IN_USE(200003, "权限正在被使用，无法删除"),

    /**
     * 角色模块错误码 (21)
     */
    ROLE_NOT_FOUND(210001, "角色不存在"),
    ROLE_CODE_EXISTS(210002, "角色编码已存在"),
    ROLE_IN_USE(210003, "角色正在被使用，无法删除"),

    /**
     * 用户模块错误码 (22)
     */
    USER_NOT_FOUND(220001, "用户不存在"),
    USER_ALREADY_EXISTS(220002, "用户已存在"),
    USERNAME_OR_PASSWORD_ERROR(220003, "用户名或密码错误"),
    ACCOUNT_LOCKED(220004, "账号已锁定"),
    MULTIPLE_TENANT_ACCOUNTS(220005, "用户存在多个商户账号，请选择具体的租户进行登录"),
    NO_TENANT_ACCOUNTS(220006, "用户未关联任何商户账号"),
    TENANT_ACCESS_DENIED(220007, "用户无权限访问指定的租户"),

    /**
     * 数据字典模块错误码 (23)
     */
    DICT_NOT_FOUND(230001, "数据字典不存在"),
    DICT_CODE_EXISTS(230002, "数据字典编码已存在"),

    /**
     * 租户模块错误码 (24)
     */
    TENANT_NOT_FOUND(240001, "租户不存在"),
    TENANT_CODE_EXISTS(240002, "租户编码已存在"),

    /**
     * 商户管理员模块错误码 (25)
     */
    MERCHANT_ADMIN_NOT_FOUND(250001, "商户管理员不存在"),
    MERCHANT_ADMIN_ALREADY_EXISTS(250002, "商户管理员已存在");

    /**
     * 错误码
     */
    private final int code;

    /**
     * 错误信息
     */
    private final String message;

    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
