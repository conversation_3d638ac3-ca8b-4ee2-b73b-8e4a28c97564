package com.extracme.saas.autocare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;

/**
 * 维修任务图片类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RepairPicTypeEnum {
    /**
     * 图片类型
     */
    DAMAGED_PART_PICTURE(new BigDecimal(1), "损坏部位图片", "damagedPartPicture"),
    REPAIR_PICTURE(new BigDecimal(2), "维修图片/车辆验收图片", "repairPicture"),
    CREATE_PICTURE(new BigDecimal(3), "创建图片/视频", "createPicture"),
    DRIVING_LICENSE_PICTURE(new BigDecimal(10), "行驶证图片", "drivingLicensePicture"),
    POLICY_PICTURE(new BigDecimal(11), "保单图片", "policyPicture"),
    ACCIDENT_PICTURE(new BigDecimal(12), "事故图片", "accidentPicture"),
    DAMAGE_A_PICTURE(new BigDecimal(13), "车损图片(标的车)", "damageAPicture"),
    DAMAGE_B_PICTURE(new BigDecimal(14), "车损图片(三者车)", "damageBPicture"),
    CLAIMS_PICTURE(new BigDecimal(15), "理赔材料", "claimsPicture"),
    OTHER_PICTURE(new BigDecimal(16), "其他图片", "otherPicture"),
    CHECK_VIDEO(new BigDecimal(17), "验收视频", "checkVideo"),
    ACCIDENT_LIABILITY_CONFIRMATION_PICTURE(new BigDecimal(19), "事故责任认定书图片", "accidentLiabilityConfirmationPicture"),
    INSURANCE_COMPANY_LOSS_ORDER_PICTURE(new BigDecimal(20), "保司定损单图片", "insuranceCompanyLossOrderPicture"),
    OUR_DRIVER_LICENSE_PICTURE(new BigDecimal(21), "我方驾驶证图片", "ourDriverLicensePicture"),
    CUST_PICTURE(new BigDecimal(22), "客户直付凭证", "custPicture"),
    DAMAGED_PART_VIDEO(new BigDecimal(23), "损坏部位视频", "damagedPartVideo");

    /**
     * 图片类型ID
     */
    private final BigDecimal typeId;

    /**
     * 图片类型名称
     */
    private final String typeName;

    /**
     * 参数名称
     */
    private final String paramName;

    /**
     * 根据图片类型ID获取图片类型名称
     * @param typeId 图片类型ID
     * @return 图片类型名称
     */
    public static String getTypeNameById(BigDecimal typeId) {
        for (RepairPicTypeEnum item : RepairPicTypeEnum.values()) {
            if (item.getTypeId().equals(typeId)) {
                return item.getTypeName();
            }
        }
        return "";
    }

    /**
     * 根据图片类型ID获取参数名称
     * @param typeId 图片类型ID
     * @return 参数名称
     */
    public static String getParamNameById(BigDecimal typeId) {
        for (RepairPicTypeEnum item : RepairPicTypeEnum.values()) {
            if (item.getTypeId().equals(typeId)) {
                return item.getParamName();
            }
        }
        return "";
    }

    /**
     * 根据参数名称获取图片类型ID
     * @param paramName 参数名称
     * @return 图片类型ID
     */
    public static BigDecimal getTypeIdByParamName(String paramName) {
        for (RepairPicTypeEnum item : RepairPicTypeEnum.values()) {
            if (item.getParamName().equals(paramName)) {
                return item.getTypeId();
            }
        }
        return null;
    }
}