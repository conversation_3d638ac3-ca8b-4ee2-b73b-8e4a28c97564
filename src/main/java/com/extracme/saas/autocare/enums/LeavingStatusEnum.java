package com.extracme.saas.autocare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum LeavingStatusEnum {

    // 1-未登记 2-已登记 3-已关闭
    REGISTERED(1, "已登记"),
    NOT_REGISTERED(2, "未登记"),
    CLOSED(3, "已关闭");

    private final Integer code;
    private final String desc;

    public static String getEnumDesc(Integer code){
        for (LeavingStatusEnum item : LeavingStatusEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }
}
