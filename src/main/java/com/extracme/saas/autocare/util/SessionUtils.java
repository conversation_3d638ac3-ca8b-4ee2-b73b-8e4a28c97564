package com.extracme.saas.autocare.util;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.extracme.saas.autocare.model.entity.SysPermission;
import com.extracme.saas.autocare.model.entity.SysUser;
import com.extracme.saas.autocare.model.security.LoginUser;
import com.extracme.saas.autocare.repository.TablePermissionService;
import com.extracme.saas.autocare.repository.TableUserService;

/**
 * Session工具类
 */
@Component
public class SessionUtils implements ApplicationContextAware {

    private static final Logger log = LoggerFactory.getLogger(SessionUtils.class);

    private static final String SESSION_USER_KEY = "LOGIN_USER";
    private static final String SESSION_PERMISSIONS_KEY = "USER_PERMISSIONS";
    private static final String SESSION_TOKEN_KEY = "USER_TOKEN";

    private static ApplicationContext applicationContext;
    private static JwtUtil jwtUtil;
    private static TableUserService tableUserService;
    private static TablePermissionService tablePermissionService;

    @Override
    public void setApplicationContext(@NonNull ApplicationContext context) throws BeansException {
        applicationContext = context;
        jwtUtil = applicationContext.getBean(JwtUtil.class);
        tableUserService = applicationContext.getBean(TableUserService.class);
        tablePermissionService = applicationContext.getBean(TablePermissionService.class);
    }

    /**
     * 获取当前请求的HttpSession
     */
    public static HttpSession getSession() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            log.error("当前线程没有绑定Request上下文，线程ID: {}, 线程名称: {}",
                    Thread.currentThread().getId(), Thread.currentThread().getName());
            throw new IllegalStateException("当前线程没有绑定Request上下文");
        }
        HttpServletRequest request = attributes.getRequest();
        HttpSession session = request.getSession();

        log.debug("获取Session，ID: {}, 创建时间: {}, 最后访问时间: {}, 最大不活动间隔: {}秒, 请求URI: {}, 线程ID: {}",
                session.getId(),
                formatDate(new Date(session.getCreationTime())),
                formatDate(new Date(session.getLastAccessedTime())),
                session.getMaxInactiveInterval(),
                request.getRequestURI(),
                Thread.currentThread().getId());

        return session;
    }

    /**
     * 格式化日期为可读字符串
     */
    private static String formatDate(Date date) {
        if (date == null) return "null";
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).toString();
    }

    /**
     * 获取当前登录用户
     * 首先尝试从当前请求的Authorization头中获取JWT token
     * 如果找到有效的token，使用JwtUtil解析token并获取用户ID
     * 根据用户ID从数据库中查询完整的用户信息
     * 返回构建好的LoginUser对象
     */
    public static LoginUser getLoginUser() {
        try {
            // 1. 尝试从请求头中获取JWT token
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                log.error("当前线程没有绑定Request上下文，线程ID: {}, 线程名称: {}",
                        Thread.currentThread().getId(), Thread.currentThread().getName());
                return null;
            }

            HttpServletRequest request = attributes.getRequest();
            String token = request.getHeader("Authorization");

            // 如果token以Bearer开头，去掉前缀
            if (token != null && token.startsWith("Bearer ")) {
                token = token.substring(7);
            }

            // 2. 如果没有找到token或token为空，尝试从session中获取
            if (token == null || token.isEmpty()) {
                log.debug("请求头中未找到有效的JWT token，尝试从session中获取用户信息");
                return getLoginUserFromSession();
            }

            // 3. 验证token有效性
            if (!jwtUtil.validateToken(token)) {
                log.warn("JWT token无效或已过期，token: {}", token);
                return null;
            }

            // 4. 从token中获取用户ID
            String userIdStr = jwtUtil.getUserIdFromToken(token);
            if (userIdStr == null) {
                log.warn("无法从JWT token中获取用户ID，token: {}", token);
                return null;
            }

            Long userId = Long.parseLong(userIdStr);

            // 5. 根据用户ID查询用户信息
            SysUser user = tableUserService.selectById(userId);
            if (user == null) {
                log.warn("根据用户ID未找到用户信息，userId: {}", userId);
                return null;
            }

            // 6. 获取用户权限
            List<SysPermission> permissionList = tablePermissionService.findByUserId(userId);
            Set<String> permissions = permissionList.stream()
                .map(SysPermission::getPermissionCode)
                .collect(Collectors.toSet());

            // 7. 构建LoginUser对象
            LoginUser loginUser = new LoginUser();
            loginUser.setUser(user);
            loginUser.setPermissions(permissions);
            loginUser.setToken(token);
            loginUser.setTenantId(user.getTenantId());
            loginUser.setLoginTime(System.currentTimeMillis());

            // 8. 从JWT中获取过期时间并设置到LoginUser中
            Date expirationDate = jwtUtil.getExpirationDateFromToken(token);
            if (expirationDate != null) {
                loginUser.setExpireTime(expirationDate.getTime());
            }
            return loginUser;

        } catch (IllegalStateException e) {
            log.error("获取登录用户异常 - 当前线程没有绑定Request上下文, 线程ID: {}, 线程名称: {}, 异常信息: {}",
                    Thread.currentThread().getId(), Thread.currentThread().getName(), e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("获取登录用户异常 - 线程ID: {}, 线程名称: {}, 异常类型: {}, 异常信息: {}",
                    Thread.currentThread().getId(), Thread.currentThread().getName(),
                    e.getClass().getName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从Session中获取登录用户信息（兼容旧方式）
     */
    private static LoginUser getLoginUserFromSession() {
        try {
            HttpSession session = getSession();
            Object userObj = session.getAttribute(SESSION_USER_KEY);

            // 记录详细的会话信息
            log.info("从Session获取登录用户信息 - 会话ID: {}, 会话是否为新创建: {}, 会话创建时间: {}, 会话最后访问时间: {}, 会话最大不活动间隔: {}秒, 线程ID: {}, 线程名称: {}",
                    session.getId(),
                    session.isNew(),
                    formatDate(new Date(session.getCreationTime())),
                    formatDate(new Date(session.getLastAccessedTime())),
                    session.getMaxInactiveInterval(),
                    Thread.currentThread().getId(),
                    Thread.currentThread().getName());

            if (userObj == null) {
                log.warn("会话中不存在用户信息 - 会话ID: {}, 线程ID: {}",
                        session.getId(), Thread.currentThread().getId());
                return null;
            }

            if (!(userObj instanceof LoginUser)) {
                log.error("会话中的用户信息类型错误 - 会话ID: {}, 实际类型: {}, 线程ID: {}",
                        session.getId(), userObj.getClass().getName(), Thread.currentThread().getId());
                return null;
            }

            LoginUser loginUser = (LoginUser) userObj;

            // 检查用户信息是否完整
            if (loginUser.getUser() == null) {
                log.warn("登录用户的User对象为空 - 会话ID: {}, 线程ID: {}",
                        session.getId(), Thread.currentThread().getId());
            }

            // 检查Token是否过期
            if (loginUser.getExpireTime() != null) {
                long currentTime = System.currentTimeMillis();
                long expireTime = loginUser.getExpireTime();

                if (currentTime > expireTime) {
                    log.warn("用户Token已过期 - 会话ID: {}, 过期时间: {}, 当前时间: {}, 线程ID: {}",
                            session.getId(),
                            formatDate(new Date(expireTime)),
                            formatDate(new Date(currentTime)),
                            Thread.currentThread().getId());
                }
            }

            return loginUser;
        } catch (Exception e) {
            log.error("从Session获取登录用户异常 - 线程ID: {}, 线程名称: {}, 异常类型: {}, 异常信息: {}",
                    Thread.currentThread().getId(), Thread.currentThread().getName(),
                    e.getClass().getName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取当前用户权限
     */
    @SuppressWarnings("unchecked")
    public static Set<String> getUserPermissions() {
        try {
            HttpSession session = getSession();
            Object permissionsObj = session.getAttribute(SESSION_PERMISSIONS_KEY);

            if (permissionsObj == null) {
                log.warn("会话中不存在权限信息 - 会话ID: {}, 线程ID: {}",
                        session.getId(), Thread.currentThread().getId());
                return null;
            }

            if (!(permissionsObj instanceof Set)) {
                log.error("会话中的权限信息类型错误 - 会话ID: {}, 实际类型: {}, 线程ID: {}",
                        session.getId(), permissionsObj.getClass().getName(), Thread.currentThread().getId());
                return null;
            }

            return (Set<String>) permissionsObj;
        } catch (Exception e) {
            log.error("获取用户权限异常 - 线程ID: {}, 线程名称: {}, 异常类型: {}, 异常信息: {}",
                    Thread.currentThread().getId(), Thread.currentThread().getName(),
                    e.getClass().getName(), e.getMessage());
            return null;
        }
    }

    /**
     * 获取当前用户Token
     */
    public static String getUserToken() {
        try {
            HttpSession session = getSession();
            Object tokenObj = session.getAttribute(SESSION_TOKEN_KEY);

            if (tokenObj == null) {
                log.warn("会话中不存在Token信息 - 会话ID: {}, 线程ID: {}",
                        session.getId(), Thread.currentThread().getId());
                return null;
            }

            if (!(tokenObj instanceof String)) {
                log.error("会话中的Token信息类型错误 - 会话ID: {}, 实际类型: {}, 线程ID: {}",
                        session.getId(), tokenObj.getClass().getName(), Thread.currentThread().getId());
                return null;
            }

            return (String) tokenObj;
        } catch (Exception e) {
            log.error("获取用户Token异常 - 线程ID: {}, 线程名称: {}, 异常类型: {}, 异常信息: {}",
                    Thread.currentThread().getId(), Thread.currentThread().getName(),
                    e.getClass().getName(), e.getMessage());
            return null;
        }
    }

    /**
     * 获取当前用户ID
     */
    public static Long getUserId() {
        LoginUser loginUser = getLoginUser();
        return loginUser != null && loginUser.getUser() != null ? loginUser.getUser().getId() : null;
    }

    /**
     * 获取当前用户名
     */
    public static String getUsername() {
        LoginUser loginUser = getLoginUser();
        return loginUser != null && loginUser.getUser() != null ? loginUser.getUser().getUsername() : null;
    }

    /**
     * 获取当前用户租户ID
     */
    public static Long getTenantId() {
        LoginUser loginUser = getLoginUser();
        return loginUser != null ? loginUser.getTenantId() : null;
    }

    /**
     * 清除Session中的用户信息
     */
    public static void clearUserInfo() {
        HttpSession session = getSession();
        session.removeAttribute(SESSION_USER_KEY);
        session.removeAttribute(SESSION_PERMISSIONS_KEY);
        session.removeAttribute(SESSION_TOKEN_KEY);
    }

    /**
     * 判断当前登录用户是否为超级管理员
     * 超级管理员的判断条件：当前登录用户不为空 && 用户实体不为空 && accountType 不为空 && accountType == 0
     *
     * @return true-超级管理员，false-普通用户或未登录
     */
    public static boolean isSuperAdmin() {
        LoginUser currentLoginUser = getLoginUser();
        return currentLoginUser != null &&
               currentLoginUser.getUser() != null &&
               currentLoginUser.getUser().getAccountType() != null &&
               currentLoginUser.getUser().getAccountType() == 0;
    }
}