package com.extracme.saas.autocare.util;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.extracme.saas.autocare.model.entity.MtcVehicleRepairPic;

/**
 * 维修任务图片工具类
 */
public class RepairPicUtil {

    /**
     * 获取需要插入的图片列表
     * 
     * @param existingPics 已存在的图片列表
     * @param newPics 新的图片URL列表
     * @param picType 图片类型
     * @return 需要插入的图片URL列表
     */
    public static List<String> getInsertPics(List<MtcVehicleRepairPic> existingPics, List<String> newPics, BigDecimal picType) {
        if (CollectionUtils.isEmpty(newPics)) {
            return new ArrayList<>();
        }
        
        // 获取已存在的同类型图片URL列表
        List<String> existingUrls = existingPics.stream()
                .filter(pic -> picType.equals(pic.getPicType()))
                .map(MtcVehicleRepairPic::getPicUrl)
                .collect(Collectors.toList());
        
        // 过滤出需要新增的图片URL
        return newPics.stream()
                .filter(url -> !existingUrls.contains(url))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取需要删除的图片列表
     * 
     * @param existingPics 已存在的图片列表
     * @param newPics 新的图片URL列表
     * @param picType 图片类型
     * @return 需要删除的图片列表
     */
    public static List<MtcVehicleRepairPic> getDeletePics(List<MtcVehicleRepairPic> existingPics, List<String> newPics, BigDecimal picType) {
        if (CollectionUtils.isEmpty(existingPics)) {
            return new ArrayList<>();
        }
        
        // 过滤出同类型的图片
        List<MtcVehicleRepairPic> typedPics = existingPics.stream()
                .filter(pic -> picType.equals(pic.getPicType()))
                .collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(typedPics)) {
            return new ArrayList<>();
        }
        
        // 如果新图片列表为空，则删除所有同类型图片
        if (CollectionUtils.isEmpty(newPics)) {
            return typedPics;
        }
        
        // 过滤出需要删除的图片
        return typedPics.stream()
                .filter(pic -> !newPics.contains(pic.getPicUrl()))
                .collect(Collectors.toList());
    }
    
    /**
     * 将图片URL列表转换为MtcVehicleRepairPic对象列表
     * 
     * @param picUrls 图片URL列表
     * @param picType 图片类型
     * @param taskNo 任务编号
     * @param comModel 通用模型对象
     * @return MtcVehicleRepairPic对象列表
     */
    public static List<MtcVehicleRepairPic> transferStringToVehicleRepairPic(List<String> picUrls, BigDecimal picType, String taskNo, String operator) {
        if (CollectionUtils.isEmpty(picUrls)) {
            return new ArrayList<>();
        }
        
        Date now = new Date();
        
        return picUrls.stream().map(url -> {
            MtcVehicleRepairPic pic = new MtcVehicleRepairPic();
            pic.setTaskNo(taskNo);
            pic.setPicType(picType.shortValue());
            pic.setPicUrl(url);
            pic.setCreateBy(operator);
            pic.setCreatedTime(now);
            pic.setUpdateBy(operator);
            pic.setUpdatedTime(now);
            return pic;
        }).collect(Collectors.toList());
    }
}
